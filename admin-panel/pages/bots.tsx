import { NextPage } from 'next'
import { useEffect } from 'react'
import { useRouter } from 'next/router'
import Layout from '../components/Layout'
import TeleShopBots from '../components/TeleShopBots'
import { useAuth } from '../contexts/AuthContext'

const BotsPage: NextPage = () => {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    } else if (!loading && user && !['ADMIN', 'SELLER'].includes(user.role)) {
      // Only ADMIN and SELLER roles can access bot management
      router.push('/dashboard')
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      </Layout>
    )
  }

  if (!user || !['ADMIN', 'SELLER'].includes(user.role)) {
    return null
  }

  return (
    <Layout>
      <div className="p-6">
        <TeleShopBots />
      </div>
    </Layout>
  )
}

export default BotsPage
