import { useState, useEffect } from 'react'
import Layout from '../components/Layout'
import { useAuth } from '../contexts/AuthContext'
import { toast } from 'react-hot-toast'
import { Save, User, Lock, Globe, Wallet, Bot, Play, Square, RefreshCw, AlertCircle, CheckCircle, Copy, ExternalLink } from 'lucide-react'

interface BotStatus {
  isRunning: boolean
  isConfigured: boolean
  lastSeen?: string
  webhookStatus?: string
  botInfo?: {
    id: number
    username: string
    first_name: string
  }
}

export default function Settings() {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState('profile')
  const [botConfig, setBotConfig] = useState({
    telegramBotToken: '',
    telegramWebhookUrl: '',
    apiBaseUrl: 'http://localhost:4000/api',
    botName: 'Digital Store Bot',
    botUsername: ''
  })
  const [botStatus, setBotStatus] = useState<BotStatus>({
    isRunning: false,
    isConfigured: false
  })
  const [isStartingBot, setIsStartingBot] = useState(false)
  const [isStoppingBot, setIsStoppingBot] = useState(false)

  const tabs = [
    { id: 'profile', name: 'Profile', icon: User },
    { id: 'security', name: 'Security', icon: Lock },
    { id: 'telegram', name: 'Telegram Bot', icon: Bot },
    { id: 'store', name: 'Store Settings', icon: Globe },
    { id: 'wallets', name: 'Crypto Wallets', icon: Wallet },
  ]

  // Check bot status periodically
  useEffect(() => {
    const checkBotStatus = async () => {
      try {
        // Simulate bot status check - replace with actual API call
        setBotStatus({
          isRunning: false,
          isConfigured: botConfig.telegramBotToken.length > 0,
          lastSeen: new Date().toISOString(),
          webhookStatus: 'Not configured'
        })
      } catch (error) {
        console.error('Failed to check bot status:', error)
      }
    }

    checkBotStatus()
    const interval = setInterval(checkBotStatus, 30000) // Check every 30 seconds

    return () => clearInterval(interval)
  }, [botConfig.telegramBotToken])

  const handleSave = () => {
    toast.success('Settings saved successfully!')
  }

  const handleBotConfigSave = () => {
    // Save bot configuration
    localStorage.setItem('botConfig', JSON.stringify(botConfig))
    toast.success('Bot configuration saved successfully!')
  }

  const handleStartBot = async () => {
    if (!botConfig.telegramBotToken) {
      toast.error('Please configure the bot token first!')
      return
    }

    setIsStartingBot(true)
    try {
      // Simulate bot start - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      setBotStatus(prev => ({ ...prev, isRunning: true }))
      toast.success('Bot started successfully!')
    } catch (error) {
      toast.error('Failed to start bot')
    } finally {
      setIsStartingBot(false)
    }
  }

  const handleStopBot = async () => {
    setIsStoppingBot(true)
    try {
      // Simulate bot stop - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setBotStatus(prev => ({ ...prev, isRunning: false }))
      toast.success('Bot stopped successfully!')
    } catch (error) {
      toast.error('Failed to stop bot')
    } finally {
      setIsStoppingBot(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied to clipboard!')
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div>
          <h1 className="text-4xl font-bold gradient-text">Settings</h1>
          <p className="text-lg mt-3" style={{ color: 'var(--text-secondary)' }}>
            Manage your store and account settings
          </p>
        </div>

        {/* Tab Navigation */}
        <div style={{ borderBottom: '1px solid var(--border-color)' }}>
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-3 px-1 border-b-2 font-medium text-sm transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'border-primary text-primary'
                      : 'border-transparent hover:border-gray-300'
                  }`}
                  style={{
                    color: activeTab === tab.id ? 'var(--primary)' : 'var(--text-secondary)',
                    borderBottomColor: activeTab === tab.id ? 'var(--primary)' : 'transparent'
                  }}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.name}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="card">
          {activeTab === 'profile' && (
            <div className="space-y-6">
              <h3 className="text-xl font-bold" style={{ color: 'var(--text-primary)' }}>Profile Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={user?.email || ''}
                    disabled
                    className="input opacity-60 cursor-not-allowed"
                    style={{ backgroundColor: 'var(--bg-tertiary)' }}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                    Username
                  </label>
                  <input
                    type="text"
                    value={user?.username || ''}
                    className="input"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                    Role
                  </label>
                  <input
                    type="text"
                    value={user?.role || ''}
                    disabled
                    className="input opacity-60 cursor-not-allowed"
                    style={{ backgroundColor: 'var(--bg-tertiary)' }}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                    Account Status
                  </label>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span style={{ color: 'var(--text-secondary)' }}>Active</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <div className="space-y-6">
              <h3 className="text-xl font-bold" style={{ color: 'var(--text-primary)' }}>Security Settings</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                    Current Password
                  </label>
                  <input
                    type="password"
                    className="input"
                    placeholder="Enter current password"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                    New Password
                  </label>
                  <input
                    type="password"
                    className="input"
                    placeholder="Enter new password"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                    Confirm New Password
                  </label>
                  <input
                    type="password"
                    className="input"
                    placeholder="Confirm new password"
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'telegram' && (
            <div className="space-y-8">
              {/* Bot Status */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                    <Bot className="h-6 w-6 mr-3 text-blue-600" />
                    Telegram Bot Status
                  </h3>
                  <div className="flex items-center space-x-2">
                    {botStatus.isRunning ? (
                      <div className="flex items-center text-green-600">
                        <CheckCircle className="h-5 w-5 mr-2" />
                        <span className="font-medium">Running</span>
                      </div>
                    ) : (
                      <div className="flex items-center text-red-600">
                        <AlertCircle className="h-5 w-5 mr-2" />
                        <span className="font-medium">Stopped</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <div className="text-sm text-gray-600 dark:text-gray-400">Configuration</div>
                    <div className="text-lg font-semibold text-gray-900 dark:text-white">
                      {botStatus.isConfigured ? 'Configured' : 'Not Configured'}
                    </div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <div className="text-sm text-gray-600 dark:text-gray-400">Webhook Status</div>
                    <div className="text-lg font-semibold text-gray-900 dark:text-white">
                      {botStatus.webhookStatus || 'Not Set'}
                    </div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <div className="text-sm text-gray-600 dark:text-gray-400">Last Seen</div>
                    <div className="text-lg font-semibold text-gray-900 dark:text-white">
                      {botStatus.lastSeen ? new Date(botStatus.lastSeen).toLocaleTimeString() : 'Never'}
                    </div>
                  </div>
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={handleStartBot}
                    disabled={botStatus.isRunning || isStartingBot || !botStatus.isConfigured}
                    className="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isStartingBot ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Play className="h-4 w-4 mr-2" />
                    )}
                    {isStartingBot ? 'Starting...' : 'Start Bot'}
                  </button>
                  <button
                    onClick={handleStopBot}
                    disabled={!botStatus.isRunning || isStoppingBot}
                    className="btn btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isStoppingBot ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Square className="h-4 w-4 mr-2" />
                    )}
                    {isStoppingBot ? 'Stopping...' : 'Stop Bot'}
                  </button>
                </div>
              </div>

              {/* Bot Configuration */}
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Bot Configuration</h3>

                {/* Setup Instructions */}
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-6 border border-blue-200 dark:border-blue-800">
                  <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-3">📋 Setup Instructions</h4>
                  <ol className="list-decimal list-inside space-y-2 text-sm text-blue-800 dark:text-blue-200">
                    <li>Create a new bot with <a href="https://t.me/BotFather" target="_blank" rel="noopener noreferrer" className="underline hover:text-blue-600">@BotFather</a> on Telegram</li>
                    <li>Copy the bot token and paste it below</li>
                    <li>Set the bot name and username (optional)</li>
                    <li>Configure the API base URL (usually http://localhost:4000/api for development)</li>
                    <li>Save the configuration and start the bot</li>
                  </ol>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Telegram Bot Token *
                    </label>
                    <div className="flex">
                      <input
                        type="password"
                        value={botConfig.telegramBotToken}
                        onChange={(e) => setBotConfig(prev => ({ ...prev, telegramBotToken: e.target.value }))}
                        placeholder="Enter your bot token from @BotFather"
                        className="input flex-1"
                      />
                      <button
                        onClick={() => copyToClipboard(botConfig.telegramBotToken)}
                        className="ml-2 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                        title="Copy token"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Get this from @BotFather on Telegram. Keep it secure!
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Bot Name
                    </label>
                    <input
                      type="text"
                      value={botConfig.botName}
                      onChange={(e) => setBotConfig(prev => ({ ...prev, botName: e.target.value }))}
                      placeholder="Digital Store Bot"
                      className="input"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Bot Username
                    </label>
                    <input
                      type="text"
                      value={botConfig.botUsername}
                      onChange={(e) => setBotConfig(prev => ({ ...prev, botUsername: e.target.value }))}
                      placeholder="@your_bot_username"
                      className="input"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      API Base URL
                    </label>
                    <input
                      type="url"
                      value={botConfig.apiBaseUrl}
                      onChange={(e) => setBotConfig(prev => ({ ...prev, apiBaseUrl: e.target.value }))}
                      placeholder="http://localhost:4000/api"
                      className="input"
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      The base URL where your backend API is running
                    </p>
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Webhook URL (Optional)
                    </label>
                    <input
                      type="url"
                      value={botConfig.telegramWebhookUrl}
                      onChange={(e) => setBotConfig(prev => ({ ...prev, telegramWebhookUrl: e.target.value }))}
                      placeholder="https://yourdomain.com/webhook/telegram"
                      className="input"
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Leave empty to use polling mode (recommended for development)
                    </p>
                  </div>
                </div>

                <div className="flex justify-end mt-6">
                  <button
                    onClick={handleBotConfigSave}
                    className="btn btn-primary"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save Bot Configuration
                  </button>
                </div>
              </div>

              {/* Environment Variables */}
              <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-xl p-6 border border-yellow-200 dark:border-yellow-800">
                <h3 className="text-xl font-bold text-yellow-900 dark:text-yellow-100 mb-4 flex items-center">
                  <AlertCircle className="h-6 w-6 mr-3" />
                  Environment Configuration
                </h3>
                <p className="text-yellow-800 dark:text-yellow-200 mb-4">
                  For the bot to work, you need to create a <code className="bg-yellow-200 dark:bg-yellow-800 px-2 py-1 rounded">.env</code> file in the telegram-bot directory with the following content:
                </p>
                <div className="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm overflow-x-auto">
                  <div className="flex justify-between items-start mb-2">
                    <span className="text-gray-400"># telegram-bot/.env</span>
                    <button
                      onClick={() => copyToClipboard(`TELEGRAM_BOT_TOKEN=${botConfig.telegramBotToken}\nAPI_BASE_URL=${botConfig.apiBaseUrl}\nNODE_ENV=development`)}
                      className="text-gray-400 hover:text-white"
                      title="Copy environment variables"
                    >
                      <Copy className="h-4 w-4" />
                    </button>
                  </div>
                  <div>TELEGRAM_BOT_TOKEN={botConfig.telegramBotToken || 'your_bot_token_here'}</div>
                  <div>API_BASE_URL={botConfig.apiBaseUrl}</div>
                  <div>NODE_ENV=development</div>
                  {botConfig.telegramWebhookUrl && (
                    <div>TELEGRAM_WEBHOOK_URL={botConfig.telegramWebhookUrl}</div>
                  )}
                </div>
                <div className="mt-4 flex items-start space-x-2">
                  <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-yellow-800 dark:text-yellow-200">
                    <p className="font-medium mb-1">Manual Setup Required:</p>
                    <ol className="list-decimal list-inside space-y-1">
                      <li>Create the <code className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">.env</code> file in the <code className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">telegram-bot/</code> directory</li>
                      <li>Copy the environment variables above into the file</li>
                      <li>Run <code className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">cd telegram-bot && npm run dev</code> to start the bot</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'store' && (
            <div className="space-y-6">
              <h3 className="text-xl font-bold" style={{ color: 'var(--text-primary)' }}>Store Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                    Store Name
                  </label>
                  <input
                    type="text"
                    defaultValue="Digital Store"
                    className="input"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                    Support Email
                  </label>
                  <input
                    type="email"
                    defaultValue="<EMAIL>"
                    className="input"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                    Store Description
                  </label>
                  <textarea
                    rows={3}
                    defaultValue="Your one-stop shop for digital products"
                    className="input resize-none"
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'wallets' && (
            <div className="space-y-6">
              <h3 className="text-xl font-bold" style={{ color: 'var(--text-primary)' }}>Cryptocurrency Wallets</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                    Bitcoin Address
                  </label>
                  <input
                    type="text"
                    placeholder="Enter Bitcoin wallet address"
                    className="input"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                    Ethereum Address
                  </label>
                  <input
                    type="text"
                    placeholder="Enter Ethereum wallet address"
                    className="input"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                    USDT Address
                  </label>
                  <input
                    type="text"
                    placeholder="Enter USDT wallet address"
                    className="input"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                    Litecoin Address
                  </label>
                  <input
                    type="text"
                    placeholder="Enter Litecoin wallet address"
                    className="input"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Save Button */}
          <div className="flex justify-end pt-6" style={{ borderTop: '1px solid var(--border-color)' }}>
            <button
              onClick={handleSave}
              className="btn btn-primary"
            >
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </button>
          </div>
        </div>
      </div>
    </Layout>
  )
}
