@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* TeleShop Dark Theme Variables */
:root {
  /* Dark Theme Primary Colors */
  --bg-primary: #0f1419;
  --bg-secondary: #1a1f2e;
  --bg-tertiary: #252b3b;
  --bg-quaternary: #2d3748;
  --bg-card: #1e2532;
  --bg-sidebar: #161b26;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #a0aec0;
  --text-tertiary: #718096;
  --text-muted: #4a5568;

  /* Border Colors */
  --border-color: #2d3748;
  --border-light: #4a5568;
  --border-focus: #4299e1;

  /* Brand Colors */
  --primary: #4299e1;
  --primary-hover: #3182ce;
  --primary-light: #63b3ed;
  --primary-dark: #2b6cb0;
  --primary-bg: #1a365d;

  /* Status Colors */
  --success: #48bb78;
  --success-light: #68d391;
  --warning: #ed8936;
  --warning-light: #f6ad55;
  --error: #f56565;
  --error-light: #fc8181;
  --info: #4299e1;
  --info-light: #63b3ed;

  /* Accent Colors */
  --accent-blue: #4299e1;
  --accent-green: #48bb78;
  --accent-purple: #9f7aea;
  --accent-orange: #ed8936;
  --accent-red: #f56565;

  /* Shadow Colors */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);
}

@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

@layer components {
  /* TeleShop Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    background-color: var(--primary);
    color: white;
    @apply hover:shadow-lg focus:ring-blue-500;
  }

  .btn-primary:hover {
    background-color: var(--primary-hover);
  }

  .btn-secondary {
    background-color: var(--bg-card);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    @apply hover:shadow-lg focus:ring-blue-500;
  }

  .btn-secondary:hover {
    background-color: var(--bg-tertiary);
  }

  .btn-ghost {
    color: var(--text-secondary);
    background-color: transparent;
    @apply hover:bg-gray-700 focus:ring-blue-500;
  }

  .btn-ghost:hover {
    color: var(--text-primary);
  }

  .btn-success {
    background-color: var(--success);
    color: white;
    @apply hover:shadow-lg;
  }

  .btn-warning {
    background-color: var(--warning);
    color: white;
    @apply hover:shadow-lg;
  }

  .btn-error {
    background-color: var(--error);
    color: white;
    @apply hover:shadow-lg;
  }

  /* TeleShop Card Components */
  .card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    @apply rounded-lg p-6 shadow-sm transition-all duration-200;
  }

  .card-hover:hover {
    @apply shadow-md transform translate-y-[-1px];
  }

  .stat-card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    @apply rounded-lg p-6 shadow-sm transition-all duration-200 hover:shadow-md;
  }

  /* TeleShop Input Components */
  .input {
    background-color: var(--bg-card);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    @apply block w-full px-4 py-3 rounded-lg placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
  }

  .select {
    background-color: var(--bg-card);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    @apply block w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
  }

  /* TeleShop Table Styles */
  .table-teleshop {
    @apply w-full border-collapse;
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
  }

  .table-teleshop th {
    color: var(--text-secondary);
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    @apply text-left py-4 px-6 text-sm font-semibold uppercase tracking-wide;
  }

  .table-teleshop td {
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    @apply py-4 px-6 text-sm;
  }

  .table-teleshop tr:hover {
    background-color: var(--bg-tertiary);
  }

  .table-teleshop tr:last-child td {
    border-bottom: none;
  }

  /* Status Badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-success {
    background-color: rgba(72, 187, 120, 0.1);
    color: var(--success-light);
  }

  .badge-warning {
    background-color: rgba(237, 137, 54, 0.1);
    color: var(--warning-light);
  }

  .badge-error {
    background-color: rgba(245, 101, 101, 0.1);
    color: var(--error-light);
  }

  .badge-info {
    background-color: rgba(66, 153, 225, 0.1);
    color: var(--info-light);
  }

  /* Sidebar Styles */
  .sidebar {
    background-color: var(--bg-sidebar);
    border-right: 1px solid var(--border-color);
  }

  .sidebar-item {
    color: var(--text-secondary);
    @apply flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 hover:bg-gray-700;
  }

  .sidebar-item:hover {
    color: var(--text-primary);
  }

  .sidebar-item.active {
    background-color: var(--primary-bg);
    color: var(--primary-light);
  }

  /* Utility Classes */
  .gradient-text {
    @apply bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent;
  }

  .text-success {
    color: var(--success);
  }

  .text-warning {
    color: var(--warning);
  }

  .text-error {
    color: var(--error);
  }

  .text-info {
    color: var(--info);
  }

  /* TeleShop Animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Hover Effects */
  .hover-lift:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease;
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px rgba(66, 153, 225, 0.3);
    transition: box-shadow 0.3s ease;
  }

  /* Loading States */
  .loading-shimmer {
    background: linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-quaternary) 50%, var(--bg-tertiary) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
}
