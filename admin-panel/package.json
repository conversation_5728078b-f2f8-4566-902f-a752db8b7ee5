{"name": "@teleshop/admin-panel", "version": "1.0.0", "description": "TeleShop admin panel for the e-commerce system", "private": true, "repository": {"type": "git", "url": "https://github.com/marysarahmccolley44/TG.git"}, "homepage": "https://github.com/marysarahmccolley44/TG", "bugs": {"url": "https://github.com/marysarahmccolley44/TG/issues"}, "scripts": {"dev": "next dev -p 4002", "build": "next build", "start": "next start -p 4002", "lint": "next lint"}, "dependencies": {"@tanstack/react-query": "^5.77.2", "@teleshop/shared": "file:../shared", "axios": "^1.6.2", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.5.2", "recharts": "^2.8.0", "tailwind-merge": "^2.2.0"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.3"}}