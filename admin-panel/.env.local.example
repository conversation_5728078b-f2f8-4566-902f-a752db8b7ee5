# TeleShop Admin Panel Environment Variables

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:4000/api

# Authentication
NEXT_PUBLIC_MOCK_AUTH=true

# Development Settings
NODE_ENV=development
NEXT_PUBLIC_DEBUG=true

# Theme Configuration
NEXT_PUBLIC_DEFAULT_THEME=dark

# File Upload Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=100MB
NEXT_PUBLIC_ALLOWED_FILE_TYPES=pdf,zip,rar,7z,tar,gz,mp4,mp3,jpg,jpeg,png,gif,doc,docx,xls,xlsx,ppt,pptx

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_TELEGRAM_BOT=true
NEXT_PUBLIC_ENABLE_CRYPTO_PAYMENTS=true

# Optional: Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=

# Optional: Error Reporting
NEXT_PUBLIC_SENTRY_DSN=

# Optional: CDN Configuration
NEXT_PUBLIC_CDN_URL=
