import React, { useState, useEffect } from 'react'
import {
  Wifi,
  WifiOff,
  <PERSON>fresh<PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>gle,
  CheckCircle,
  XCircle,
  Settings,
  Globe,
  Shield,
  Clock,
  Activity,
  Zap,
  Copy,
  ExternalLink,
  FileText,
  Download,
  Loader2,
  Bot,
  Server,
  Lock,
  Unlock,
  Eye,
  EyeOff,
  TestTube,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface WebhookManagerProps {
  botId: string
  botName: string
  webhookUrl?: string
  webhookStatus: 'CONNECTED' | 'DISCONNECTED' | 'ERROR'
  onClose: () => void
  onWebhookUpdate: (status: string) => void
}

interface WebhookConfig {
  url: string
  isSecure: boolean
  maxConnections: number
  allowedUpdates: string[]
  dropPendingUpdates: boolean
  secretToken?: string
}

interface WebhookLog {
  id: string
  timestamp: string
  type: 'incoming' | 'outgoing' | 'error' | 'info'
  method: string
  status: number
  message: string
  payload?: any
}

interface ConnectionTest {
  step: string
  status: 'pending' | 'success' | 'error'
  message: string
  duration?: number
}

const WebhookManager: React.FC<WebhookManagerProps> = ({
  botId,
  botName,
  webhookUrl,
  webhookStatus,
  onClose,
  onWebhookUpdate
}) => {
  const [config, setConfig] = useState<WebhookConfig>({
    url: webhookUrl || `https://api.teleshop.com/webhook/${botId}`,
    isSecure: true,
    maxConnections: 40,
    allowedUpdates: ['message', 'callback_query', 'inline_query'],
    dropPendingUpdates: false,
    secretToken: ''
  })
  
  const [logs, setLogs] = useState<WebhookLog[]>([])
  const [activeTab, setActiveTab] = useState('status')
  const [testing, setTesting] = useState(false)
  const [refreshing, setRefreshing] = useState(false)
  const [showSecretToken, setShowSecretToken] = useState(false)
  const [testResults, setTestResults] = useState<ConnectionTest[]>([])
  const [autoRefresh, setAutoRefresh] = useState(false)

  useEffect(() => {
    fetchWebhookLogs()
    
    let interval: NodeJS.Timeout
    if (autoRefresh) {
      interval = setInterval(fetchWebhookLogs, 5000)
    }
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [autoRefresh])

  const fetchWebhookLogs = async () => {
    // Mock webhook logs
    const mockLogs: WebhookLog[] = [
      {
        id: '1',
        timestamp: new Date(Date.now() - 300000).toISOString(),
        type: 'incoming',
        method: 'POST',
        status: 200,
        message: 'Message received from user',
        payload: { update_id: 123456, message: { text: '/start' } }
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 600000).toISOString(),
        type: 'outgoing',
        method: 'POST',
        status: 200,
        message: 'Response sent to Telegram',
        payload: { method: 'sendMessage', chat_id: 123456 }
      },
      {
        id: '3',
        timestamp: new Date(Date.now() - 900000).toISOString(),
        type: 'error',
        method: 'POST',
        status: 500,
        message: 'Internal server error',
        payload: { error: 'Database connection failed' }
      },
      {
        id: '4',
        timestamp: new Date(Date.now() - 1200000).toISOString(),
        type: 'info',
        method: 'GET',
        status: 200,
        message: 'Webhook health check',
        payload: { status: 'healthy' }
      }
    ]
    
    setLogs(mockLogs)
  }

  const handleWebhookTest = async () => {
    setTesting(true)
    setTestResults([])
    
    const tests: ConnectionTest[] = [
      { step: 'Validating webhook URL', status: 'pending', message: 'Checking URL format and accessibility' },
      { step: 'Testing SSL certificate', status: 'pending', message: 'Verifying SSL/TLS configuration' },
      { step: 'Checking connectivity', status: 'pending', message: 'Testing connection to webhook endpoint' },
      { step: 'Sending test payload', status: 'pending', message: 'Sending sample webhook data' },
      { step: 'Verifying response', status: 'pending', message: 'Checking webhook response handling' }
    ]
    
    setTestResults([...tests])
    
    try {
      for (let i = 0; i < tests.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000))
        
        const success = Math.random() > 0.2 // 80% success rate for demo
        tests[i].status = success ? 'success' : 'error'
        tests[i].duration = Math.floor(Math.random() * 500 + 100)
        
        if (!success && i === 2) {
          tests[i].message = 'Connection timeout - webhook endpoint unreachable'
        } else if (!success && i === 4) {
          tests[i].message = 'Invalid response format received'
        }
        
        setTestResults([...tests])
        
        if (!success) break
      }
      
      const allSuccess = tests.every(test => test.status === 'success')
      if (allSuccess) {
        toast.success('Webhook test completed successfully!')
        onWebhookUpdate('CONNECTED')
      } else {
        toast.error('Webhook test failed - check configuration')
        onWebhookUpdate('ERROR')
      }
    } catch (error) {
      toast.error('Webhook test failed')
      onWebhookUpdate('ERROR')
    } finally {
      setTesting(false)
    }
  }

  const handleWebhookRefresh = async () => {
    setRefreshing(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 2000))
      toast.success('Webhook refreshed successfully!')
      onWebhookUpdate('CONNECTED')
      fetchWebhookLogs()
    } catch (error) {
      toast.error('Failed to refresh webhook')
      onWebhookUpdate('ERROR')
    } finally {
      setRefreshing(false)
    }
  }

  const handleConfigSave = async () => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Webhook configuration saved!')
    } catch (error) {
      toast.error('Failed to save webhook configuration')
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied to clipboard!')
  }

  const exportLogs = () => {
    const csvData = [
      ['Timestamp', 'Type', 'Method', 'Status', 'Message'],
      ...logs.map(log => [
        new Date(log.timestamp).toLocaleString(),
        log.type,
        log.method,
        log.status.toString(),
        log.message
      ])
    ]
    
    const csvContent = csvData.map(row => row.join(',')).join('\n')
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `webhook_logs_${botId}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
    
    toast.success('Webhook logs exported!')
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'CONNECTED':
        return 'text-green-600 bg-green-100'
      case 'DISCONNECTED':
        return 'text-gray-600 bg-gray-100'
      case 'ERROR':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'CONNECTED':
        return <Wifi className="h-4 w-4" />
      case 'DISCONNECTED':
        return <WifiOff className="h-4 w-4" />
      case 'ERROR':
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <WifiOff className="h-4 w-4" />
    }
  }

  const getLogTypeColor = (type: string) => {
    switch (type) {
      case 'incoming':
        return 'text-blue-600 bg-blue-100'
      case 'outgoing':
        return 'text-green-600 bg-green-100'
      case 'error':
        return 'text-red-600 bg-red-100'
      case 'info':
        return 'text-gray-600 bg-gray-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getTestStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'pending':
        return <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const tabs = [
    { id: 'status', name: 'Status', icon: Activity },
    { id: 'config', name: 'Configuration', icon: Settings },
    { id: 'logs', name: 'Logs', icon: FileText },
    { id: 'test', name: 'Test', icon: TestTube }
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
      <div className="relative mx-auto border w-full max-w-4xl shadow-2xl rounded-2xl bg-white">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Zap className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900">Webhook Management</h3>
              <p className="text-sm text-gray-600">{botName} - Connection & Monitoring</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          >
            <XCircle className="h-6 w-6 text-gray-500" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6 max-h-96 overflow-y-auto">
          {/* Status Tab */}
          {activeTab === 'status' && (
            <div className="space-y-6">
              <div className="bg-gray-50 p-6 rounded-xl">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                    <Server className="w-5 h-5" />
                    Webhook Status
                  </h4>
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold ${getStatusColor(webhookStatus)}`}>
                    {getStatusIcon(webhookStatus)}
                    <span className="ml-1">{webhookStatus}</span>
                  </span>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Webhook URL</label>
                    <div className="flex items-center gap-2">
                      <input
                        type="text"
                        value={config.url}
                        readOnly
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-sm"
                      />
                      <button
                        onClick={() => copyToClipboard(config.url)}
                        className="p-2 text-gray-400 hover:text-gray-600"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Security</label>
                    <div className="flex items-center gap-2">
                      {config.isSecure ? (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <Lock className="w-3 h-3 mr-1" />
                          SSL Enabled
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          <Unlock className="w-3 h-3 mr-1" />
                          SSL Disabled
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex gap-3">
                  <button
                    onClick={handleWebhookRefresh}
                    disabled={refreshing}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  >
                    {refreshing ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <RefreshCw className="w-4 h-4" />
                    )}
                    Refresh Connection
                  </button>
                  <button
                    onClick={handleWebhookTest}
                    disabled={testing}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  >
                    {testing ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <TestTube className="w-4 h-4" />
                    )}
                    Test Connection
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-xl">
                  <div className="flex items-center gap-3">
                    <Activity className="w-6 h-6 text-blue-600" />
                    <div>
                      <div className="text-lg font-bold text-blue-900">98.5%</div>
                      <div className="text-sm text-blue-600">Uptime (24h)</div>
                    </div>
                  </div>
                </div>
                <div className="bg-green-50 p-4 rounded-xl">
                  <div className="flex items-center gap-3">
                    <Clock className="w-6 h-6 text-green-600" />
                    <div>
                      <div className="text-lg font-bold text-green-900">145ms</div>
                      <div className="text-sm text-green-600">Avg Response</div>
                    </div>
                  </div>
                </div>
                <div className="bg-purple-50 p-4 rounded-xl">
                  <div className="flex items-center gap-3">
                    <Zap className="w-6 h-6 text-purple-600" />
                    <div>
                      <div className="text-lg font-bold text-purple-900">1,247</div>
                      <div className="text-sm text-purple-600">Requests (24h)</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default WebhookManager
