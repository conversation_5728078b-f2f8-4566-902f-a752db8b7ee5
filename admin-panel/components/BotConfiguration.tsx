import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  MessageSquare,
  CreditCard,
  Globe,
  Palette,
  Zap,
  Wifi,
  WifiOff,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Save,
  RefreshCw,
  Eye,
  EyeOff,
  Copy,
  ExternalLink,
  Loader2,
  ArrowLeft,
  HelpCircle,
  Info,
  Shield,
  Users,
  Package,
  DollarSign,
  Clock,
  Languages,
  Smartphone,
  Monitor
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface BotConfigurationProps {
  botId: string
  onBack: () => void
}

interface BotConfig {
  id: string
  name: string
  username: string
  token: string
  description?: string
  isActive: boolean
  status: 'ACTIVE' | 'INACTIVE' | 'ERROR' | 'SETUP'
  webhookUrl?: string
  webhookStatus: 'CONNECTED' | 'DISCONNECTED' | 'ERROR'
  settings: {
    welcomeMessage: string
    language: string
    currency: string
    paymentMethods: string[]
    supportContact?: string
    storeInfo?: string
    theme: {
      primaryColor: string
      accentColor: string
    }
    features: {
      categories: boolean
      userProfiles: boolean
      balanceTopUp: boolean
      orderHistory: boolean
    }
    notifications: {
      orderReceived: boolean
      paymentConfirmed: boolean
      lowStock: boolean
    }
    security: {
      rateLimiting: boolean
      spamProtection: boolean
      adminNotifications: boolean
    }
  }
}

const BotConfiguration: React.FC<BotConfigurationProps> = ({ botId, onBack }) => {
  const [config, setConfig] = useState<BotConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('general')
  const [showToken, setShowToken] = useState(false)
  const [webhookTesting, setWebhookTesting] = useState(false)

  useEffect(() => {
    fetchBotConfig()
  }, [botId])

  const fetchBotConfig = async () => {
    try {
      setLoading(true)
      
      // Mock data for demonstration
      const mockConfig: BotConfig = {
        id: botId,
        name: 'Digital Store Bot',
        username: 'digitalstore_bot',
        token: '1234567890:AAEhBOweik6ad6PsVQF6XJhiknbhd6k-Ks',
        description: 'Premium digital products and software marketplace',
        isActive: true,
        status: 'ACTIVE',
        webhookUrl: 'https://api.teleshop.com/webhook/bot_001',
        webhookStatus: 'CONNECTED',
        settings: {
          welcomeMessage: 'Welcome to Digital Store! 🛍️ Browse our premium digital products and find exactly what you need.',
          language: 'en',
          currency: 'USD',
          paymentMethods: ['BITCOIN', 'ETHEREUM', 'USDT'],
          supportContact: '@digitalsupport',
          storeInfo: 'Premium digital marketplace with instant delivery and 24/7 support.',
          theme: {
            primaryColor: '#3B82F6',
            accentColor: '#8B5CF6'
          },
          features: {
            categories: true,
            userProfiles: true,
            balanceTopUp: true,
            orderHistory: true
          },
          notifications: {
            orderReceived: true,
            paymentConfirmed: true,
            lowStock: false
          },
          security: {
            rateLimiting: true,
            spamProtection: true,
            adminNotifications: true
          }
        }
      }
      
      setConfig(mockConfig)
      setLoading(false)
    } catch (error) {
      console.error('Error fetching bot config:', error)
      toast.error('Failed to load bot configuration')
      setLoading(false)
    }
  }

  const handleSave = async () => {
    if (!config) return

    setSaving(true)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/bots/${botId}/config`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(config)
      })

      if (response.ok) {
        toast.success('Bot configuration saved successfully!')
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || 'Failed to save configuration')
      }
    } catch (error) {
      console.error('Error saving config:', error)
      toast.error('Error saving configuration')
    } finally {
      setSaving(false)
    }
  }

  const handleWebhookTest = async () => {
    setWebhookTesting(true)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/bots/${botId}/webhook/test`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (response.ok) {
        toast.success('Webhook test successful!')
        fetchBotConfig() // Refresh to get updated webhook status
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || 'Webhook test failed')
      }
    } catch (error) {
      console.error('Error testing webhook:', error)
      toast.error('Error testing webhook')
    } finally {
      setWebhookTesting(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied to clipboard!')
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800'
      case 'INACTIVE':
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800'
      case 'ERROR':
        return 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800'
      case 'SETUP':
        return 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <CheckCircle className="h-3 w-3" />
      case 'INACTIVE':
        return <XCircle className="h-3 w-3" />
      case 'ERROR':
        return <AlertTriangle className="h-3 w-3" />
      case 'SETUP':
        return <Clock className="h-3 w-3" />
      default:
        return <XCircle className="h-3 w-3" />
    }
  }

  const getWebhookStatusColor = (status: string) => {
    switch (status) {
      case 'CONNECTED':
        return 'text-green-600'
      case 'DISCONNECTED':
        return 'text-gray-600'
      case 'ERROR':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getWebhookStatusIcon = (status: string) => {
    switch (status) {
      case 'CONNECTED':
        return <Wifi className="h-4 w-4" />
      case 'DISCONNECTED':
        return <WifiOff className="h-4 w-4" />
      case 'ERROR':
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <WifiOff className="h-4 w-4" />
    }
  }

  const tabs = [
    { id: 'general', name: 'General', icon: Settings },
    { id: 'messages', name: 'Messages', icon: MessageSquare },
    { id: 'payments', name: 'Payments', icon: CreditCard },
    { id: 'features', name: 'Features', icon: Package },
    { id: 'theme', name: 'Theme', icon: Palette },
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'webhook', name: 'Webhook', icon: Zap }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-3">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <span className="text-lg font-medium text-gray-600">Loading configuration...</span>
        </div>
      </div>
    )
  }

  if (!config) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-16 w-16 mx-auto text-red-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Configuration Not Found</h3>
        <p className="text-gray-600 mb-6">Unable to load bot configuration.</p>
        <button
          onClick={onBack}
          className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg flex items-center gap-2 mx-auto"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Bots
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div className="flex items-center gap-4">
          <button
            onClick={onBack}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Bot Configuration
            </h1>
            <p className="text-gray-600 mt-1 flex items-center gap-2">
              <Bot className="h-4 w-4" />
              {config.name} (@{config.username})
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3 mt-4 sm:mt-0">
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold ${getStatusColor(config.status)} shadow-sm`}>
            {getStatusIcon(config.status)}
            <span className="ml-1">{config.status}</span>
          </span>
          <button
            onClick={handleSave}
            disabled={saving}
            className="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {saving ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                Save Changes
              </>
            )}
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'general' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Bot className="h-4 w-4 inline mr-1" />
                    Bot Name
                  </label>
                  <input
                    type="text"
                    value={config.name}
                    onChange={(e) => setConfig({...config, name: e.target.value})}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Smartphone className="h-4 w-4 inline mr-1" />
                    Bot Username
                  </label>
                  <input
                    type="text"
                    value={config.username}
                    onChange={(e) => setConfig({...config, username: e.target.value})}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <MessageSquare className="h-4 w-4 inline mr-1" />
                  Description
                </label>
                <textarea
                  value={config.description || ''}
                  onChange={(e) => setConfig({...config, description: e.target.value})}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  rows={3}
                  placeholder="Brief description of your bot store..."
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Zap className="h-4 w-4 inline mr-1" />
                  Bot Token
                </label>
                <div className="relative">
                  <input
                    type={showToken ? 'text' : 'password'}
                    value={config.token}
                    onChange={(e) => setConfig({...config, token: e.target.value})}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 pr-20 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center gap-2 pr-3">
                    <button
                      type="button"
                      onClick={() => setShowToken(!showToken)}
                      className="p-1 text-gray-400 hover:text-gray-600"
                    >
                      {showToken ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                    <button
                      type="button"
                      onClick={() => copyToClipboard(config.token)}
                      className="p-1 text-gray-400 hover:text-gray-600"
                    >
                      <Copy className="h-4 w-4" />
                    </button>
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-1">Get this from @BotFather on Telegram</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Languages className="h-4 w-4 inline mr-1" />
                    Language
                  </label>
                  <select
                    value={config.settings.language}
                    onChange={(e) => setConfig({
                      ...config,
                      settings: { ...config.settings, language: e.target.value }
                    })}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  >
                    <option value="en">🇺🇸 English</option>
                    <option value="es">🇪🇸 Spanish</option>
                    <option value="fr">🇫🇷 French</option>
                    <option value="de">🇩🇪 German</option>
                    <option value="it">🇮🇹 Italian</option>
                    <option value="pt">🇵🇹 Portuguese</option>
                    <option value="ru">🇷🇺 Russian</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <DollarSign className="h-4 w-4 inline mr-1" />
                    Currency
                  </label>
                  <select
                    value={config.settings.currency}
                    onChange={(e) => setConfig({
                      ...config,
                      settings: { ...config.settings, currency: e.target.value }
                    })}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  >
                    <option value="USD">USD - US Dollar</option>
                    <option value="EUR">EUR - Euro</option>
                    <option value="GBP">GBP - British Pound</option>
                    <option value="BTC">BTC - Bitcoin</option>
                    <option value="ETH">ETH - Ethereum</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'messages' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <MessageSquare className="h-4 w-4 inline mr-1" />
                  Welcome Message
                </label>
                <textarea
                  value={config.settings.welcomeMessage}
                  onChange={(e) => setConfig({
                    ...config,
                    settings: { ...config.settings, welcomeMessage: e.target.value }
                  })}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  rows={4}
                  placeholder="Welcome message shown to new users..."
                />
                <p className="text-xs text-gray-500 mt-1">This message will be sent when users first start your bot</p>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Info className="h-4 w-4 inline mr-1" />
                  Store Information
                </label>
                <textarea
                  value={config.settings.storeInfo || ''}
                  onChange={(e) => setConfig({
                    ...config,
                    settings: { ...config.settings, storeInfo: e.target.value }
                  })}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  rows={3}
                  placeholder="Information about your store, policies, etc..."
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Users className="h-4 w-4 inline mr-1" />
                  Support Contact
                </label>
                <input
                  type="text"
                  value={config.settings.supportContact || ''}
                  onChange={(e) => setConfig({
                    ...config,
                    settings: { ...config.settings, supportContact: e.target.value }
                  })}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  placeholder="@support_<NAME_EMAIL>"
                />
                <p className="text-xs text-gray-500 mt-1">Telegram username or email for customer support</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default BotConfiguration
