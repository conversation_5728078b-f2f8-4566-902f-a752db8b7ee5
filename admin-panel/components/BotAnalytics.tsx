import React, { useState, useEffect } from 'react'
import {
  Bar<PERSON>hart3,
  Tren<PERSON>Up,
  TrendingDown,
  Users,
  ShoppingCart,
  DollarSign,
  Activity,
  Globe,
  CreditCard,
  Calendar,
  Download,
  Filter,
  RefreshCw,
  ArrowLeft,
  Eye,
  Target,
  Zap,
  Clock,
  MapPin,
  Smartphone,
  Monitor,
  Bot,
  AlertCircle,
  CheckCircle,
  XCircle,
  Info,
  Loader2,
  FileText,
  PieChart,
  LineChart
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface BotAnalyticsProps {
  botId: string
  botName: string
  onBack: () => void
}

interface AnalyticsData {
  overview: {
    totalRevenue: number
    totalCustomers: number
    totalOrders: number
    conversionRate: number
    averageOrderValue: number
    activeUsers24h: number
    revenueGrowth: number
    customerGrowth: number
  }
  revenueData: {
    daily: Array<{ date: string; revenue: number; orders: number }>
    monthly: Array<{ month: string; revenue: number; orders: number }>
  }
  customerData: {
    acquisition: Array<{ date: string; newCustomers: number; totalCustomers: number }>
    retention: Array<{ period: string; retentionRate: number }>
    demographics: Array<{ country: string; customers: number; percentage: number }>
  }
  orderData: {
    patterns: Array<{ hour: number; orders: number }>
    categories: Array<{ category: string; orders: number; revenue: number }>
    status: Array<{ status: string; count: number; percentage: number }>
  }
  paymentData: {
    methods: Array<{ method: string; transactions: number; volume: number; percentage: number }>
    trends: Array<{ date: string; bitcoin: number; ethereum: number; usdt: number }>
  }
  conversionFunnel: Array<{
    stage: string
    users: number
    conversionRate: number
    dropoffRate: number
  }>
}

const BotAnalytics: React.FC<BotAnalyticsProps> = ({ botId, botName, onBack }) => {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')
  const [dateRange, setDateRange] = useState('7d')
  const [exporting, setExporting] = useState(false)

  useEffect(() => {
    fetchAnalytics()
  }, [botId, dateRange])

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      
      // Mock comprehensive analytics data
      const mockAnalytics: AnalyticsData = {
        overview: {
          totalRevenue: 45670.50,
          totalCustomers: 1247,
          totalOrders: 856,
          conversionRate: 68.7,
          averageOrderValue: 53.35,
          activeUsers24h: 89,
          revenueGrowth: 12.5,
          customerGrowth: 8.3
        },
        revenueData: {
          daily: Array.from({ length: 30 }, (_, i) => ({
            date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            revenue: Math.random() * 2000 + 500,
            orders: Math.floor(Math.random() * 50 + 10)
          })),
          monthly: Array.from({ length: 12 }, (_, i) => ({
            month: new Date(2024, i, 1).toLocaleDateString('en-US', { month: 'short' }),
            revenue: Math.random() * 15000 + 5000,
            orders: Math.floor(Math.random() * 300 + 100)
          }))
        },
        customerData: {
          acquisition: Array.from({ length: 30 }, (_, i) => ({
            date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            newCustomers: Math.floor(Math.random() * 20 + 5),
            totalCustomers: 1000 + i * 8
          })),
          retention: [
            { period: 'Day 1', retentionRate: 85.2 },
            { period: 'Day 7', retentionRate: 72.8 },
            { period: 'Day 30', retentionRate: 45.6 },
            { period: 'Day 90', retentionRate: 28.3 }
          ],
          demographics: [
            { country: 'United States', customers: 387, percentage: 31.0 },
            { country: 'Germany', customers: 249, percentage: 20.0 },
            { country: 'United Kingdom', customers: 187, percentage: 15.0 },
            { country: 'France', customers: 125, percentage: 10.0 },
            { country: 'Canada', customers: 112, percentage: 9.0 },
            { country: 'Others', customers: 187, percentage: 15.0 }
          ]
        },
        orderData: {
          patterns: Array.from({ length: 24 }, (_, i) => ({
            hour: i,
            orders: Math.floor(Math.random() * 30 + 5)
          })),
          categories: [
            { category: 'Digital Products', orders: 342, revenue: 18450.75 },
            { category: 'Software Tools', orders: 298, revenue: 15920.30 },
            { category: 'Educational Content', orders: 156, revenue: 8340.20 },
            { category: 'Templates', orders: 60, revenue: 2959.25 }
          ],
          status: [
            { status: 'Completed', count: 742, percentage: 86.7 },
            { status: 'Processing', count: 67, percentage: 7.8 },
            { status: 'Pending', count: 32, percentage: 3.7 },
            { status: 'Cancelled', count: 15, percentage: 1.8 }
          ]
        },
        paymentData: {
          methods: [
            { method: 'Bitcoin', transactions: 456, volume: 24580.50, percentage: 53.8 },
            { method: 'Ethereum', transactions: 298, volume: 15670.25, percentage: 34.3 },
            { method: 'USDT', transactions: 102, volume: 5420.75, percentage: 11.9 }
          ],
          trends: Array.from({ length: 30 }, (_, i) => ({
            date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            bitcoin: Math.random() * 1000 + 200,
            ethereum: Math.random() * 600 + 100,
            usdt: Math.random() * 300 + 50
          }))
        },
        conversionFunnel: [
          { stage: 'Bot Started', users: 2450, conversionRate: 100, dropoffRate: 0 },
          { stage: 'Viewed Products', users: 1960, conversionRate: 80.0, dropoffRate: 20.0 },
          { stage: 'Added to Cart', users: 1372, conversionRate: 70.0, dropoffRate: 30.0 },
          { stage: 'Initiated Checkout', users: 1098, conversionRate: 80.0, dropoffRate: 20.0 },
          { stage: 'Completed Payment', users: 856, conversionRate: 77.9, dropoffRate: 22.1 }
        ]
      }
      
      setAnalytics(mockAnalytics)
      setLoading(false)
    } catch (error) {
      console.error('Error fetching analytics:', error)
      toast.error('Failed to load analytics data')
      setLoading(false)
    }
  }

  const handleExport = async () => {
    setExporting(true)
    try {
      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Create and download CSV
      const csvData = analytics ? [
        ['Metric', 'Value'],
        ['Total Revenue', `$${analytics.overview.totalRevenue.toLocaleString()}`],
        ['Total Customers', analytics.overview.totalCustomers.toLocaleString()],
        ['Total Orders', analytics.overview.totalOrders.toLocaleString()],
        ['Conversion Rate', `${analytics.overview.conversionRate}%`],
        ['Average Order Value', `$${analytics.overview.averageOrderValue}`],
        ['Active Users (24h)', analytics.overview.activeUsers24h.toLocaleString()]
      ] : []
      
      const csvContent = csvData.map(row => row.join(',')).join('\n')
      const blob = new Blob([csvContent], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${botName}_analytics_${dateRange}.csv`
      a.click()
      window.URL.revokeObjectURL(url)
      
      toast.success('Analytics data exported successfully!')
    } catch (error) {
      console.error('Error exporting data:', error)
      toast.error('Failed to export analytics data')
    } finally {
      setExporting(false)
    }
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: BarChart3 },
    { id: 'revenue', name: 'Revenue', icon: DollarSign },
    { id: 'customers', name: 'Customers', icon: Users },
    { id: 'orders', name: 'Orders', icon: ShoppingCart },
    { id: 'payments', name: 'Payments', icon: CreditCard },
    { id: 'conversion', name: 'Conversion', icon: Target }
  ]

  const dateRanges = [
    { value: '24h', label: 'Last 24 Hours' },
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 90 Days' },
    { value: '1y', label: 'Last Year' }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-3">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <span className="text-lg font-medium text-gray-600">Loading analytics...</span>
        </div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-16 w-16 mx-auto text-red-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Analytics Not Available</h3>
        <p className="text-gray-600 mb-6">Unable to load analytics data for this bot.</p>
        <button
          onClick={onBack}
          className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg flex items-center gap-2 mx-auto"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Bots
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div className="flex items-center gap-4">
          <button
            onClick={onBack}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Bot Analytics
            </h1>
            <p className="text-gray-600 mt-1 flex items-center gap-2">
              <Bot className="h-4 w-4" />
              {botName} - Performance Dashboard
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3 mt-4 sm:mt-0">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
          >
            {dateRanges.map((range) => (
              <option key={range.value} value={range.value}>
                {range.label}
              </option>
            ))}
          </select>
          <button
            onClick={fetchAnalytics}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200"
            title="Refresh Data"
          >
            <RefreshCw className="w-5 h-5" />
          </button>
          <button
            onClick={handleExport}
            disabled={exporting}
            className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg hover:from-green-600 hover:to-emerald-700 transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {exporting ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="w-4 h-4" />
                Export
              </>
            )}
          </button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">${analytics.overview.totalRevenue.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+{analytics.overview.revenueGrowth}%</span>
              </div>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Customers</p>
              <p className="text-2xl font-bold text-gray-900">{analytics.overview.totalCustomers.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-blue-500 mr-1" />
                <span className="text-sm text-blue-600">+{analytics.overview.customerGrowth}%</span>
              </div>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Orders</p>
              <p className="text-2xl font-bold text-gray-900">{analytics.overview.totalOrders.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <Activity className="w-4 h-4 text-purple-500 mr-1" />
                <span className="text-sm text-purple-600">{analytics.overview.activeUsers24h} active today</span>
              </div>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <ShoppingCart className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
              <p className="text-2xl font-bold text-gray-900">{analytics.overview.conversionRate}%</p>
              <div className="flex items-center mt-2">
                <Target className="w-4 h-4 text-orange-500 mr-1" />
                <span className="text-sm text-orange-600">${analytics.overview.averageOrderValue} AOV</span>
              </div>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <Target className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Revenue Chart */}
                <div className="bg-gray-50 p-6 rounded-xl">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <LineChart className="w-5 h-5" />
                    Revenue Trend (Last 30 Days)
                  </h3>
                  <div className="h-64 flex items-center justify-center bg-white rounded-lg border border-gray-200">
                    <div className="text-center">
                      <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500">Interactive chart would be rendered here</p>
                      <p className="text-sm text-gray-400">Peak: ${Math.max(...analytics.revenueData.daily.map(d => d.revenue)).toFixed(2)}</p>
                    </div>
                  </div>
                </div>

                {/* Customer Acquisition */}
                <div className="bg-gray-50 p-6 rounded-xl">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    Customer Acquisition
                  </h3>
                  <div className="h-64 flex items-center justify-center bg-white rounded-lg border border-gray-200">
                    <div className="text-center">
                      <TrendingUp className="w-12 h-12 text-blue-400 mx-auto mb-2" />
                      <p className="text-gray-500">Customer growth chart</p>
                      <p className="text-sm text-gray-400">New customers: {analytics.customerData.acquisition.slice(-7).reduce((sum, d) => sum + d.newCustomers, 0)} this week</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gradient-to-r from-blue-50 to-cyan-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-3">Top Performing Day</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Revenue:</span>
                      <span className="font-semibold">${Math.max(...analytics.revenueData.daily.map(d => d.revenue)).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Orders:</span>
                      <span className="font-semibold">{Math.max(...analytics.revenueData.daily.map(d => d.orders))}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-3">Customer Retention</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Day 1:</span>
                      <span className="font-semibold">{analytics.customerData.retention[0].retentionRate}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Day 30:</span>
                      <span className="font-semibold">{analytics.customerData.retention[2].retentionRate}%</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-3">Payment Methods</h4>
                  <div className="space-y-2">
                    {analytics.paymentData.methods.slice(0, 2).map((method) => (
                      <div key={method.method} className="flex justify-between">
                        <span className="text-gray-600">{method.method}:</span>
                        <span className="font-semibold">{method.percentage}%</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default BotAnalytics
