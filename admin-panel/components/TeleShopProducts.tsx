import React, { useState, useEffect } from 'react'
import {
  Plus,
  Search,
  Download,
  Upload,
  Filter,
  MoreVertical,
  Package,
  DollarSign,
  Tag,
  FileText,
  Image,
  Paperclip,
  X,
  AlertCircle,
  CheckCircle,
  Store as StoreIcon
} from 'lucide-react'
import { apiService } from '../services/api'
import { toast } from 'react-hot-toast'
import { useAuth } from '../contexts/AuthContext'

interface Product {
  id: string
  name: string
  description: string
  price: number
  currency: string
  category: string
  tags: string[]
  isActive: boolean
  fileUrl: string
  fileName: string
  fileSize: number
  downloadLimit: number
  previewUrl?: string
  thumbnailUrl?: string
  storeId: string
  createdAt: string
  updatedAt: string
  store?: {
    id: string
    name: string
  }
}

interface Store {
  id: string
  name: string
  description?: string
  isActive: boolean
}

interface CreateProductData {
  name: string
  description: string
  price: number
  currency: string
  category: string
  tags: string[]
  downloadLimit: number
  storeId: string
  file?: File
  previewFile?: File
  thumbnailFile?: File
}

const TeleShopProducts: React.FC = () => {
  const { user } = useAuth()
  const [products, setProducts] = useState<Product[]>([])
  const [stores, setStores] = useState<Store[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All Categories')
  const [selectedStatus, setSelectedStatus] = useState('All Status')
  const [currentPage, setCurrentPage] = useState(1)
  const [showAddModal, setShowAddModal] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const itemsPerPage = 6

  const [createProductData, setCreateProductData] = useState<CreateProductData>({
    name: '',
    description: '',
    price: 0,
    currency: 'USD',
    category: '',
    tags: [],
    downloadLimit: 3,
    storeId: '',
    file: undefined,
    previewFile: undefined,
    thumbnailFile: undefined
  })

  const [tagInput, setTagInput] = useState('')
  const [dragActive, setDragActive] = useState(false)

  useEffect(() => {
    fetchProducts()
    fetchStores()
  }, [])

  const fetchStores = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/stores`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setStores(data.data || [])

        // Set default store for user
        if (user && user.storeId && data.data) {
          const userStore = data.data.find((store: Store) => store.id === user.storeId)
          if (userStore) {
            setCreateProductData(prev => ({ ...prev, storeId: userStore.id }))
          }
        }
      }
    } catch (error) {
      console.error('Error fetching stores:', error)
    }
  }

  const fetchProducts = async () => {
    try {
      setLoading(true)

      // Try to fetch from API first, fallback to mock data
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/products`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })

        if (response.ok) {
          const data = await response.json()
          setProducts(data.data.products || [])
        } else {
          throw new Error('API request failed')
        }
      } catch (apiError) {
        console.log('Using mock data due to API error:', apiError)

        // Mock data fallback with proper structure
        setProducts([
          {
            id: '1',
            name: 'Premium WordPress Theme',
            description: 'A beautiful and responsive WordPress theme',
            price: 49.99,
            currency: 'USD',
            category: 'Templates',
            tags: ['wordpress', 'theme'],
            isActive: true,
            fileUrl: '',
            fileName: 'theme.zip',
            fileSize: 15728640,
            downloadLimit: 3,
            storeId: 'default-store',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '2',
            name: 'JavaScript Course',
            description: 'Complete JavaScript mastery course',
            price: 199.99,
            currency: 'USD',
            category: 'Education',
            tags: ['javascript', 'course'],
            isActive: true,
            fileUrl: '',
            fileName: 'course.zip',
            fileSize: 524288000,
            downloadLimit: 5,
            storeId: 'default-store',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ])
      }

      setLoading(false)
    } catch (error) {
      console.error('Error fetching products:', error)
      toast.error('Failed to fetch products')
      setLoading(false)
    }
  }

  // Product creation functions
  const handleCreateProduct = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsCreating(true)

    try {
      const formData = new FormData()

      // Add product data
      formData.append('name', createProductData.name)
      formData.append('description', createProductData.description)
      formData.append('price', createProductData.price.toString())
      formData.append('currency', createProductData.currency)
      formData.append('category', createProductData.category)
      formData.append('tags', JSON.stringify(createProductData.tags))
      formData.append('downloadLimit', createProductData.downloadLimit.toString())
      formData.append('storeId', createProductData.storeId)

      // Add files if present
      if (createProductData.file) {
        formData.append('file', createProductData.file)
      }
      if (createProductData.previewFile) {
        formData.append('previewFile', createProductData.previewFile)
      }
      if (createProductData.thumbnailFile) {
        formData.append('thumbnailFile', createProductData.thumbnailFile)
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/products`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      })

      if (response.ok) {
        toast.success('Product created successfully!')
        setShowAddModal(false)
        resetCreateForm()
        fetchProducts()
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || 'Failed to create product')
      }
    } catch (error) {
      console.error('Error creating product:', error)
      toast.error('Error creating product')
    } finally {
      setIsCreating(false)
    }
  }

  const resetCreateForm = () => {
    setCreateProductData({
      name: '',
      description: '',
      price: 0,
      currency: 'USD',
      category: '',
      tags: [],
      downloadLimit: 3,
      storeId: user?.storeId || '',
      file: undefined,
      previewFile: undefined,
      thumbnailFile: undefined
    })
    setTagInput('')
  }

  const addTag = () => {
    if (tagInput.trim() && !createProductData.tags.includes(tagInput.trim())) {
      setCreateProductData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }))
      setTagInput('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setCreateProductData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleFileUpload = (file: File, type: 'file' | 'previewFile' | 'thumbnailFile') => {
    setCreateProductData(prev => ({
      ...prev,
      [type]: file
    }))
  }

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'All Categories' || product.category === selectedCategory
    const matchesStatus = selectedStatus === 'All Status' ||
      (selectedStatus === 'Available' && product.isActive) ||
      (selectedStatus === 'Unavailable' && !product.isActive)
    return matchesSearch && matchesCategory && matchesStatus
  })

  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedProducts = filteredProducts.slice(startIndex, startIndex + itemsPerPage)

  const StatusBadge: React.FC<{ isActive: boolean }> = ({ isActive }) => (
    <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold ${
      isActive
        ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800'
        : 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800'
    }`}>
      {isActive ? (
        <>
          <CheckCircle className="h-3 w-3 mr-1" />
          Available
        </>
      ) : (
        <>
          <AlertCircle className="h-3 w-3 mr-1" />
          Unavailable
        </>
      )}
    </span>
  )

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-700 rounded w-64 mb-6"></div>
          <div className="grid grid-cols-1 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
            Product Management
          </h1>
          <p className="text-sm mt-1" style={{ color: 'var(--text-secondary)' }}>
            Manage your product inventory and listings
          </p>
        </div>
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <button className="btn btn-secondary">
            <Download className="w-4 h-4 mr-2" />
            Export
          </button>
          <button className="btn btn-secondary">
            <Upload className="w-4 h-4 mr-2" />
            Import
          </button>
          <button
            className="btn btn-primary"
            onClick={() => setShowAddModal(true)}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Product
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4" style={{ color: 'var(--text-secondary)' }} />
            <input
              type="text"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input pl-10 pr-4 py-2 w-full"
            />
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-3">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="select"
            >
              <option>All Categories</option>
              <option>Electronics</option>
              <option>Accessories</option>
            </select>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="select"
            >
              <option>All Status</option>
              <option>Available</option>
              <option>Unavailable</option>
            </select>
          </div>
        </div>
      </div>

      {/* Products Table */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Product
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Price
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center gap-2">
                    <Tag className="h-4 w-4" />
                    Category
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center gap-2">
                    <Paperclip className="h-4 w-4" />
                    File Size
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Status
                  </div>
                </th>
                <th className="px-6 py-4 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center justify-end gap-2">
                    <MoreVertical className="h-4 w-4" />
                    Actions
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {paginatedProducts.map((product) => (
                <tr key={product.id} className="hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-200">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-12 w-12">
                        <div className="h-12 w-12 rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                          <Package className="h-6 w-6 text-white" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-semibold text-gray-900">
                          {product.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {product.description.length > 50
                            ? `${product.description.substring(0, 50)}...`
                            : product.description}
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          {product.tags.slice(0, 2).map((tag, index) => (
                            <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                              {tag}
                            </span>
                          ))}
                          {product.tags.length > 2 && (
                            <span className="text-xs text-gray-500">+{product.tags.length - 2} more</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-semibold text-gray-900">
                      ${product.price.toFixed(2)}
                    </div>
                    <div className="text-sm text-gray-500">
                      {product.currency}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800">
                      {product.category}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center gap-1">
                      <Paperclip className="h-4 w-4 text-gray-400" />
                      {formatFileSize(product.fileSize)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {product.downloadLimit} downloads
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <StatusBadge isActive={product.isActive} />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200">
                      <MoreVertical className="w-4 h-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
          Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredProducts.length)} of {filteredProducts.length} entries
        </p>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 rounded-lg border border-gray-600 text-sm disabled:opacity-50"
            style={{ color: 'var(--text-secondary)' }}
          >
            Previous
          </button>
          <span className="px-3 py-1 bg-blue-500 text-white rounded-lg text-sm">
            {currentPage}
          </span>
          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 rounded-lg border border-gray-600 text-sm disabled:opacity-50"
            style={{ color: 'var(--text-secondary)' }}
          >
            Next
          </button>
        </div>
      </div>

      {/* Add Product Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="relative mx-auto border w-full max-w-4xl shadow-2xl rounded-2xl bg-white max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-white border-b border-gray-200 px-8 py-6 rounded-t-2xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                    <Plus className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Add New Product</h3>
                </div>
                <button
                  onClick={() => {
                    setShowAddModal(false)
                    resetCreateForm()
                  }}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                >
                  <X className="h-6 w-6 text-gray-500" />
                </button>
              </div>
            </div>

            <form onSubmit={handleCreateProduct} className="p-8 space-y-8">
              {/* Basic Information */}
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-4">
                  <Package className="h-5 w-5 text-blue-500" />
                  <h4 className="text-lg font-semibold text-gray-800">Basic Information</h4>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      <Package className="h-4 w-4 inline mr-1" />
                      Product Name *
                    </label>
                    <input
                      type="text"
                      required
                      value={createProductData.name}
                      onChange={(e) => setCreateProductData(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      placeholder="Enter product name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      <Tag className="h-4 w-4 inline mr-1" />
                      Category *
                    </label>
                    <select
                      required
                      value={createProductData.category}
                      onChange={(e) => setCreateProductData(prev => ({ ...prev, category: e.target.value }))}
                      className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    >
                      <option value="">Select category</option>
                      <option value="Templates">Templates</option>
                      <option value="Education">Education</option>
                      <option value="Software">Software</option>
                      <option value="Graphics">Graphics</option>
                      <option value="Audio">Audio</option>
                      <option value="Video">Video</option>
                      <option value="Documents">Documents</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <FileText className="h-4 w-4 inline mr-1" />
                    Description *
                  </label>
                  <textarea
                    required
                    rows={4}
                    value={createProductData.description}
                    onChange={(e) => setCreateProductData(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="Describe your product in detail..."
                  />
                </div>
              </div>

              {/* Pricing & Settings */}
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-4">
                  <DollarSign className="h-5 w-5 text-green-500" />
                  <h4 className="text-lg font-semibold text-gray-800">Pricing & Settings</h4>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      <DollarSign className="h-4 w-4 inline mr-1" />
                      Price *
                    </label>
                    <input
                      type="number"
                      required
                      min="0"
                      step="0.01"
                      value={createProductData.price}
                      onChange={(e) => setCreateProductData(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                      className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Currency
                    </label>
                    <select
                      value={createProductData.currency}
                      onChange={(e) => setCreateProductData(prev => ({ ...prev, currency: e.target.value }))}
                      className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    >
                      <option value="USD">USD</option>
                      <option value="BITCOIN">Bitcoin</option>
                      <option value="ETHEREUM">Ethereum</option>
                      <option value="USDT">USDT</option>
                      <option value="LITECOIN">Litecoin</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Download Limit
                    </label>
                    <input
                      type="number"
                      min="1"
                      value={createProductData.downloadLimit}
                      onChange={(e) => setCreateProductData(prev => ({ ...prev, downloadLimit: parseInt(e.target.value) || 3 }))}
                      className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      placeholder="3"
                    />
                  </div>
                </div>

                {/* Store Selection */}
                {(user?.role === 'ADMIN' || user?.role === 'MANAGER') && stores.length > 0 && (
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      <StoreIcon className="h-4 w-4 inline mr-1" />
                      Store *
                    </label>
                    <select
                      required
                      value={createProductData.storeId}
                      onChange={(e) => setCreateProductData(prev => ({ ...prev, storeId: e.target.value }))}
                      className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    >
                      <option value="">Select store</option>
                      {stores.map((store) => (
                        <option key={store.id} value={store.id}>
                          {store.name}
                        </option>
                      ))}
                    </select>
                  </div>
                )}
              </div>

              {/* Tags */}
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-4">
                  <Tag className="h-5 w-5 text-purple-500" />
                  <h4 className="text-lg font-semibold text-gray-800">Tags</h4>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Product Tags
                  </label>
                  <div className="flex gap-2 mb-3">
                    <input
                      type="text"
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                      className="flex-1 border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      placeholder="Enter tag and press Enter"
                    />
                    <button
                      type="button"
                      onClick={addTag}
                      className="px-4 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors duration-200"
                    >
                      Add
                    </button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {createProductData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                      >
                        {tag}
                        <button
                          type="button"
                          onClick={() => removeTag(tag)}
                          className="ml-2 text-blue-600 hover:text-blue-800"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {/* File Upload */}
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-4">
                  <Paperclip className="h-5 w-5 text-orange-500" />
                  <h4 className="text-lg font-semibold text-gray-800">File Upload</h4>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Main Product File */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Product File *
                    </label>
                    <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-500 transition-colors duration-200">
                      <input
                        type="file"
                        onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'file')}
                        className="hidden"
                        id="product-file"
                        accept=".zip,.rar,.pdf,.doc,.docx,.mp4,.mp3,.jpg,.png,.gif"
                      />
                      <label htmlFor="product-file" className="cursor-pointer">
                        <Paperclip className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-600">
                          {createProductData.file ? createProductData.file.name : 'Click to upload main file'}
                        </p>
                      </label>
                    </div>
                  </div>

                  {/* Preview File */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Preview File (Optional)
                    </label>
                    <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-500 transition-colors duration-200">
                      <input
                        type="file"
                        onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'previewFile')}
                        className="hidden"
                        id="preview-file"
                        accept=".jpg,.png,.gif,.pdf,.mp4"
                      />
                      <label htmlFor="preview-file" className="cursor-pointer">
                        <Image className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-600">
                          {createProductData.previewFile ? createProductData.previewFile.name : 'Click to upload preview'}
                        </p>
                      </label>
                    </div>
                  </div>

                  {/* Thumbnail */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Thumbnail (Optional)
                    </label>
                    <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-500 transition-colors duration-200">
                      <input
                        type="file"
                        onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'thumbnailFile')}
                        className="hidden"
                        id="thumbnail-file"
                        accept=".jpg,.png,.gif"
                      />
                      <label htmlFor="thumbnail-file" className="cursor-pointer">
                        <Image className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-600">
                          {createProductData.thumbnailFile ? createProductData.thumbnailFile.name : 'Click to upload thumbnail'}
                        </p>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              {/* Submit Buttons */}
              <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => {
                    setShowAddModal(false)
                    resetCreateForm()
                  }}
                  className="px-6 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isCreating}
                  className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isCreating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white inline mr-2"></div>
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 inline mr-2" />
                      Create Product
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default TeleShopProducts
