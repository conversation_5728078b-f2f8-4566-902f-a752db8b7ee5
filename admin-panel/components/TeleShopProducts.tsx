import React, { useState, useEffect } from 'react'
import {
  Plus,
  Search,
  Download,
  Upload,
  Filter,
  MoreVertical,
  Package,
  DollarSign,
  Tag,
  FileText,
  Image,
  Paperclip,
  X,
  AlertCircle,
  CheckCircle,
  Store as StoreIcon,
  FileSpreadsheet,
  UploadCloud,
  DownloadCloud,
  FileCheck,
  AlertTriangle,
  Clock,
  CheckCircle2,
  XCircle,
  FileX,
  Loader2,
  Bot,
  Users,
  Link,
  Unlink,
  Settings,
  Activity,
  Power,
  PowerOff,
  Eye,
  Trash2,
  RefreshCw,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { apiService } from '../services/api'
import { toast } from 'react-hot-toast'
import { useAuth } from '../contexts/AuthContext'
import Tooltip, { TooltipIcon } from './Tooltip'

interface Product {
  id: string
  name: string
  description: string
  price: number
  currency: string
  category: string
  tags: string[]
  isActive: boolean
  fileUrl: string
  fileName: string
  fileSize: number
  downloadLimit: number
  previewUrl?: string
  thumbnailUrl?: string
  storeId: string
  createdAt: string
  updatedAt: string
  assignedBots?: AssignedBot[]
  store?: {
    id: string
    name: string
  }
}

interface AssignedBot {
  id: string
  name: string
  username: string
  isActive: boolean
  status: 'ACTIVE' | 'INACTIVE' | 'ERROR' | 'SETUP'
  customerCount: number
  assignedAt: string
}

interface BotAssignment {
  productId: string
  botId: string
  assignedAt: string
  assignedBy: string
}

interface Store {
  id: string
  name: string
  description?: string
  isActive: boolean
}

interface CreateProductData {
  name: string
  description: string
  price: number
  currency: string
  category: string
  tags: string[]
  downloadLimit: number
  storeId: string
  file?: File
  previewFile?: File
  thumbnailFile?: File
}

interface CSVProductData {
  name: string
  description: string
  price: number
  currency: string
  category: string
  tags: string
  downloadLimit: number
  storeId: string
  isActive: boolean
}

interface ImportValidationError {
  row: number
  field: string
  message: string
  data: any
}

interface ImportResult {
  success: number
  failed: number
  errors: ImportValidationError[]
  total: number
}

const TeleShopProducts: React.FC = () => {
  const { user } = useAuth()
  const [products, setProducts] = useState<Product[]>([])
  const [stores, setStores] = useState<Store[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All Categories')
  const [selectedStatus, setSelectedStatus] = useState('All Status')
  const [currentPage, setCurrentPage] = useState(1)
  const [showAddModal, setShowAddModal] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const itemsPerPage = 6

  // Bot assignment state
  const [availableBots, setAvailableBots] = useState<AssignedBot[]>([])
  const [showBotAssignmentModal, setShowBotAssignmentModal] = useState(false)
  const [selectedProductForBots, setSelectedProductForBots] = useState<Product | null>(null)
  const [selectedBots, setSelectedBots] = useState<string[]>([])
  const [isAssigningBots, setIsAssigningBots] = useState(false)
  const [botSearchTerm, setBotSearchTerm] = useState('')
  const [expandedBotSections, setExpandedBotSections] = useState<{ [key: string]: boolean }>({})

  const [createProductData, setCreateProductData] = useState<CreateProductData>({
    name: '',
    description: '',
    price: 0,
    currency: 'USD',
    category: '',
    tags: [],
    downloadLimit: 3,
    storeId: '',
    file: undefined,
    previewFile: undefined,
    thumbnailFile: undefined
  })

  const [tagInput, setTagInput] = useState('')
  const [dragActive, setDragActive] = useState(false)

  // CSV Import/Export state
  const [showImportModal, setShowImportModal] = useState(false)
  const [csvFile, setCsvFile] = useState<File | null>(null)
  const [csvData, setCsvData] = useState<CSVProductData[]>([])
  const [importPreview, setImportPreview] = useState<CSVProductData[]>([])
  const [validationErrors, setValidationErrors] = useState<ImportValidationError[]>([])
  const [isImporting, setIsImporting] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [showImportPreview, setShowImportPreview] = useState(false)

  useEffect(() => {
    fetchProducts()
    fetchStores()
    fetchBots()
  }, [])

  const fetchStores = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/stores`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setStores(data.data || [])

        // Set default store for user
        if (user && user.storeId && data.data) {
          const userStore = data.data.find((store: Store) => store.id === user.storeId)
          if (userStore) {
            setCreateProductData(prev => ({ ...prev, storeId: userStore.id }))
          }
        }
      }
    } catch (error) {
      console.error('Error fetching stores:', error)
    }
  }

  const fetchBots = async () => {
    try {
      // Try to fetch from API first, fallback to mock data
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/bots`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })

        if (response.ok) {
          const data = await response.json()
          const bots = data.data?.bots || []
          setAvailableBots(bots.map((bot: any) => ({
            id: bot.id,
            name: bot.name,
            username: bot.username,
            isActive: bot.isActive,
            status: bot.status,
            customerCount: bot.analytics?.totalCustomers || 0,
            assignedAt: new Date().toISOString()
          })))
        } else {
          throw new Error('API request failed')
        }
      } catch (apiError) {
        console.log('Using mock bot data due to API error:', apiError)

        // Mock bot data fallback
        setAvailableBots([
          {
            id: 'bot_001',
            name: 'Digital Store Bot',
            username: 'digitalstore_bot',
            isActive: true,
            status: 'ACTIVE',
            customerCount: 1247,
            assignedAt: new Date().toISOString()
          },
          {
            id: 'bot_002',
            name: 'Crypto Tools Bot',
            username: 'cryptotools_bot',
            isActive: true,
            status: 'ACTIVE',
            customerCount: 856,
            assignedAt: new Date().toISOString()
          },
          {
            id: 'bot_003',
            name: 'GameDev Assets Bot',
            username: 'gamedev_assets_bot',
            isActive: false,
            status: 'INACTIVE',
            customerCount: 423,
            assignedAt: new Date().toISOString()
          }
        ])
      }
    } catch (error) {
      console.error('Error fetching bots:', error)
    }
  }

  const fetchProducts = async () => {
    try {
      setLoading(true)

      // Try to fetch from API first, fallback to mock data
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/products`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })

        if (response.ok) {
          const data = await response.json()
          setProducts(data.data.products || [])
        } else {
          throw new Error('API request failed')
        }
      } catch (apiError) {
        console.log('Using mock data due to API error:', apiError)

        // Mock data fallback with proper structure
        setProducts([
          {
            id: '1',
            name: 'Premium WordPress Theme',
            description: 'A beautiful and responsive WordPress theme',
            price: 49.99,
            currency: 'USD',
            category: 'Templates',
            tags: ['wordpress', 'theme'],
            isActive: true,
            fileUrl: '',
            fileName: 'theme.zip',
            fileSize: 15728640,
            downloadLimit: 3,
            storeId: 'default-store',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            assignedBots: [
              {
                id: 'bot_001',
                name: 'Digital Store Bot',
                username: 'digitalstore_bot',
                isActive: true,
                status: 'ACTIVE',
                customerCount: 1247,
                assignedAt: new Date(Date.now() - 86400000).toISOString()
              },
              {
                id: 'bot_002',
                name: 'Crypto Tools Bot',
                username: 'cryptotools_bot',
                isActive: true,
                status: 'ACTIVE',
                customerCount: 856,
                assignedAt: new Date(Date.now() - 172800000).toISOString()
              }
            ]
          },
          {
            id: '2',
            name: 'JavaScript Course',
            description: 'Complete JavaScript mastery course',
            price: 199.99,
            currency: 'USD',
            category: 'Education',
            tags: ['javascript', 'course'],
            isActive: true,
            fileUrl: '',
            fileName: 'course.zip',
            fileSize: 524288000,
            downloadLimit: 5,
            storeId: 'default-store',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            assignedBots: [
              {
                id: 'bot_001',
                name: 'Digital Store Bot',
                username: 'digitalstore_bot',
                isActive: true,
                status: 'ACTIVE',
                customerCount: 1247,
                assignedAt: new Date(Date.now() - 259200000).toISOString()
              }
            ]
          },
          {
            id: '3',
            name: 'React Component Library',
            description: 'Professional React components for modern web apps',
            price: 79.99,
            currency: 'USD',
            category: 'Software',
            tags: ['react', 'components', 'ui'],
            isActive: true,
            fileUrl: '',
            fileName: 'components.zip',
            fileSize: 25165824,
            downloadLimit: 5,
            storeId: 'default-store',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            assignedBots: []
          }
        ])
      }

      setLoading(false)
    } catch (error) {
      console.error('Error fetching products:', error)
      toast.error('Failed to fetch products')
      setLoading(false)
    }
  }

  // Product creation functions
  const handleCreateProduct = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsCreating(true)

    try {
      const formData = new FormData()

      // Add product data
      formData.append('name', createProductData.name)
      formData.append('description', createProductData.description)
      formData.append('price', createProductData.price.toString())
      formData.append('currency', createProductData.currency)
      formData.append('category', createProductData.category)
      formData.append('tags', JSON.stringify(createProductData.tags))
      formData.append('downloadLimit', createProductData.downloadLimit.toString())
      formData.append('storeId', createProductData.storeId)

      // Add files if present
      if (createProductData.file) {
        formData.append('file', createProductData.file)
      }
      if (createProductData.previewFile) {
        formData.append('previewFile', createProductData.previewFile)
      }
      if (createProductData.thumbnailFile) {
        formData.append('thumbnailFile', createProductData.thumbnailFile)
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/products`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      })

      if (response.ok) {
        toast.success('Product created successfully!')
        setShowAddModal(false)
        resetCreateForm()
        fetchProducts()
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || 'Failed to create product')
      }
    } catch (error) {
      console.error('Error creating product:', error)
      toast.error('Error creating product')
    } finally {
      setIsCreating(false)
    }
  }

  const resetCreateForm = () => {
    setCreateProductData({
      name: '',
      description: '',
      price: 0,
      currency: 'USD',
      category: '',
      tags: [],
      downloadLimit: 3,
      storeId: user?.storeId || '',
      file: undefined,
      previewFile: undefined,
      thumbnailFile: undefined
    })
    setTagInput('')
  }

  const addTag = () => {
    if (tagInput.trim() && !createProductData.tags.includes(tagInput.trim())) {
      setCreateProductData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }))
      setTagInput('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setCreateProductData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleFileUpload = (file: File, type: 'file' | 'previewFile' | 'thumbnailFile') => {
    setCreateProductData(prev => ({
      ...prev,
      [type]: file
    }))
  }

  // CSV Export functionality
  const handleExportCSV = async () => {
    setIsExporting(true)
    try {
      // Use filtered products for export
      const productsToExport = filteredProducts.length > 0 ? filteredProducts : products

      if (productsToExport.length === 0) {
        toast.error('No products to export')
        return
      }

      // CSV headers
      const headers = [
        'name',
        'description',
        'price',
        'currency',
        'category',
        'tags',
        'downloadLimit',
        'storeId',
        'isActive',
        'createdAt',
        'updatedAt'
      ]

      // Convert products to CSV format
      const csvRows = [
        headers.join(','), // Header row
        ...productsToExport.map(product => [
          `"${product.name.replace(/"/g, '""')}"`, // Escape quotes
          `"${product.description.replace(/"/g, '""')}"`,
          product.price.toString(),
          product.currency,
          product.category,
          `"${product.tags.join(',')}"`, // Tags as comma-separated string
          product.downloadLimit.toString(),
          product.storeId,
          product.isActive.toString(),
          product.createdAt,
          product.updatedAt
        ].join(','))
      ]

      // Create and download CSV file
      const csvContent = csvRows.join('\n')
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')

      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', `teleshop-products-${new Date().toISOString().split('T')[0]}.csv`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }

      toast.success(`Successfully exported ${productsToExport.length} products`)
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Failed to export products')
    } finally {
      setIsExporting(false)
    }
  }

  // CSV Import functionality
  const parseCSV = (csvText: string): CSVProductData[] => {
    const lines = csvText.split('\n').filter(line => line.trim())
    if (lines.length < 2) return []

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
    const data: CSVProductData[] = []

    for (let i = 1; i < lines.length; i++) {
      const values = parseCSVLine(lines[i])
      if (values.length === headers.length) {
        const row: any = {}
        headers.forEach((header, index) => {
          row[header] = values[index]?.trim().replace(/"/g, '') || ''
        })

        // Convert data types
        const productData: CSVProductData = {
          name: row.name || '',
          description: row.description || '',
          price: parseFloat(row.price) || 0,
          currency: row.currency || 'USD',
          category: row.category || '',
          tags: row.tags || '',
          downloadLimit: parseInt(row.downloadLimit) || 3,
          storeId: row.storeId || '',
          isActive: row.isActive === 'true' || row.isActive === '1' || row.isActive === 'yes'
        }

        data.push(productData)
      }
    }

    return data
  }

  const parseCSVLine = (line: string): string[] => {
    const result = []
    let current = ''
    let inQuotes = false

    for (let i = 0; i < line.length; i++) {
      const char = line[i]

      if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === ',' && !inQuotes) {
        result.push(current)
        current = ''
      } else {
        current += char
      }
    }

    result.push(current)
    return result
  }

  const validateCSVData = (data: CSVProductData[]): ImportValidationError[] => {
    const errors: ImportValidationError[] = []
    const validCurrencies = ['USD', 'BITCOIN', 'ETHEREUM', 'USDT', 'LITECOIN']
    const validCategories = ['Templates', 'Education', 'Software', 'Graphics', 'Audio', 'Video', 'Documents', 'Other']

    data.forEach((row, index) => {
      const rowNumber = index + 2 // +2 because index starts at 0 and we skip header

      // Required fields validation
      if (!row.name?.trim()) {
        errors.push({
          row: rowNumber,
          field: 'name',
          message: 'Product name is required',
          data: row
        })
      }

      if (!row.description?.trim()) {
        errors.push({
          row: rowNumber,
          field: 'description',
          message: 'Product description is required',
          data: row
        })
      }

      if (!row.category?.trim()) {
        errors.push({
          row: rowNumber,
          field: 'category',
          message: 'Product category is required',
          data: row
        })
      } else if (!validCategories.includes(row.category)) {
        errors.push({
          row: rowNumber,
          field: 'category',
          message: `Invalid category. Must be one of: ${validCategories.join(', ')}`,
          data: row
        })
      }

      if (!row.storeId?.trim()) {
        errors.push({
          row: rowNumber,
          field: 'storeId',
          message: 'Store ID is required',
          data: row
        })
      } else {
        // Check if store exists
        const storeExists = stores.some(store => store.id === row.storeId)
        if (!storeExists) {
          errors.push({
            row: rowNumber,
            field: 'storeId',
            message: `Store with ID "${row.storeId}" does not exist`,
            data: row
          })
        }
      }

      // Data type validation
      if (isNaN(row.price) || row.price < 0) {
        errors.push({
          row: rowNumber,
          field: 'price',
          message: 'Price must be a valid positive number',
          data: row
        })
      }

      if (!validCurrencies.includes(row.currency)) {
        errors.push({
          row: rowNumber,
          field: 'currency',
          message: `Invalid currency. Must be one of: ${validCurrencies.join(', ')}`,
          data: row
        })
      }

      if (isNaN(row.downloadLimit) || row.downloadLimit < 1) {
        errors.push({
          row: rowNumber,
          field: 'downloadLimit',
          message: 'Download limit must be a positive integer',
          data: row
        })
      }
    })

    return errors
  }

  const handleCSVFileUpload = async (file: File) => {
    setCsvFile(file)

    try {
      const text = await file.text()
      const parsedData = parseCSV(text)
      setCsvData(parsedData)

      const errors = validateCSVData(parsedData)
      setValidationErrors(errors)

      // Filter out invalid rows for preview
      const validRows = parsedData.filter((_, index) => {
        const rowNumber = index + 2
        return !errors.some(error => error.row === rowNumber)
      })

      setImportPreview(validRows)
      setShowImportPreview(true)

      if (errors.length > 0) {
        toast.error(`Found ${errors.length} validation errors. Check the preview for details.`)
      } else {
        toast.success(`Successfully parsed ${parsedData.length} products. Ready to import!`)
      }
    } catch (error) {
      console.error('CSV parsing error:', error)
      toast.error('Failed to parse CSV file. Please check the format.')
    }
  }

  const handleImportProducts = async () => {
    if (importPreview.length === 0) {
      toast.error('No valid products to import')
      return
    }

    setIsImporting(true)
    const results: ImportResult = {
      success: 0,
      failed: 0,
      errors: [],
      total: importPreview.length
    }

    try {
      for (let i = 0; i < importPreview.length; i++) {
        const productData = importPreview[i]

        try {
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/products`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
              ...productData,
              tags: productData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
              fileUrl: '', // Will be updated when file is uploaded
              fileName: '',
              fileSize: 0
            })
          })

          if (response.ok) {
            results.success++
          } else {
            const errorData = await response.json()
            results.failed++
            results.errors.push({
              row: i + 2,
              field: 'general',
              message: errorData.message || 'Failed to create product',
              data: productData
            })
          }
        } catch (error) {
          results.failed++
          results.errors.push({
            row: i + 2,
            field: 'general',
            message: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
            data: productData
          })
        }
      }

      setImportResult(results)

      if (results.success > 0) {
        toast.success(`Successfully imported ${results.success} products!`)
        fetchProducts() // Refresh the products list
      }

      if (results.failed > 0) {
        toast.error(`Failed to import ${results.failed} products. Check the results for details.`)
      }

    } catch (error) {
      console.error('Import error:', error)
      toast.error('Import process failed')
    } finally {
      setIsImporting(false)
    }
  }

  const resetImportState = () => {
    setCsvFile(null)
    setCsvData([])
    setImportPreview([])
    setValidationErrors([])
    setImportResult(null)
    setShowImportPreview(false)
  }

  // Bot Assignment Functions
  const handleBotAssignment = (product: Product) => {
    setSelectedProductForBots(product)
    setSelectedBots(product.assignedBots?.map(bot => bot.id) || [])
    setShowBotAssignmentModal(true)
  }

  const toggleBotSelection = (botId: string) => {
    setSelectedBots(prev =>
      prev.includes(botId)
        ? prev.filter(id => id !== botId)
        : [...prev, botId]
    )
  }

  const handleBulkBotSelection = (selectAll: boolean) => {
    if (selectAll) {
      setSelectedBots(filteredBots.map(bot => bot.id))
    } else {
      setSelectedBots([])
    }
  }

  const assignBotsToProduct = async () => {
    if (!selectedProductForBots) return

    setIsAssigningBots(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Update the product with new bot assignments
      const updatedProducts = products.map(product => {
        if (product.id === selectedProductForBots.id) {
          const newAssignedBots = selectedBots.map(botId => {
            const bot = availableBots.find(b => b.id === botId)
            return bot ? {
              ...bot,
              assignedAt: new Date().toISOString()
            } : null
          }).filter(Boolean) as AssignedBot[]

          return {
            ...product,
            assignedBots: newAssignedBots
          }
        }
        return product
      })

      setProducts(updatedProducts)
      toast.success(`Successfully assigned ${selectedBots.length} bots to ${selectedProductForBots.name}`)
      setShowBotAssignmentModal(false)
      setSelectedProductForBots(null)
      setSelectedBots([])
    } catch (error) {
      console.error('Error assigning bots:', error)
      toast.error('Failed to assign bots to product')
    } finally {
      setIsAssigningBots(false)
    }
  }

  const removeBotFromProduct = async (productId: string, botId: string) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))

      const updatedProducts = products.map(product => {
        if (product.id === productId) {
          return {
            ...product,
            assignedBots: product.assignedBots?.filter(bot => bot.id !== botId) || []
          }
        }
        return product
      })

      setProducts(updatedProducts)

      const product = products.find(p => p.id === productId)
      const bot = availableBots.find(b => b.id === botId)
      if (product && bot) {
        toast.success(`Removed ${bot.name} from ${product.name}`)
      }
    } catch (error) {
      console.error('Error removing bot assignment:', error)
      toast.error('Failed to remove bot assignment')
    }
  }

  const toggleBotSection = (productId: string) => {
    setExpandedBotSections(prev => ({
      ...prev,
      [productId]: !prev[productId]
    }))
  }

  const filteredBots = availableBots.filter(bot =>
    bot.name.toLowerCase().includes(botSearchTerm.toLowerCase()) ||
    bot.username.toLowerCase().includes(botSearchTerm.toLowerCase())
  )

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'All Categories' || product.category === selectedCategory
    const matchesStatus = selectedStatus === 'All Status' ||
      (selectedStatus === 'Available' && product.isActive) ||
      (selectedStatus === 'Unavailable' && !product.isActive)
    return matchesSearch && matchesCategory && matchesStatus
  })

  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedProducts = filteredProducts.slice(startIndex, startIndex + itemsPerPage)

  const StatusBadge: React.FC<{ isActive: boolean }> = ({ isActive }) => (
    <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold ${
      isActive
        ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800'
        : 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800'
    }`}>
      {isActive ? (
        <>
          <CheckCircle className="h-3 w-3 mr-1" />
          Available
        </>
      ) : (
        <>
          <AlertCircle className="h-3 w-3 mr-1" />
          Unavailable
        </>
      )}
    </span>
  )

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-700 rounded w-64 mb-6"></div>
          <div className="grid grid-cols-1 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
            Product Management
          </h1>
          <p className="text-sm mt-1" style={{ color: 'var(--text-secondary)' }}>
            Manage your product inventory and listings
          </p>
        </div>
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <button
            onClick={handleExportCSV}
            disabled={isExporting}
            className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isExporting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <DownloadCloud className="w-4 h-4 mr-2" />
                Export CSV
              </>
            )}
          </button>
          <button
            onClick={() => setShowImportModal(true)}
            className="px-4 py-2 bg-gradient-to-r from-orange-500 to-red-600 text-white rounded-xl hover:from-orange-600 hover:to-red-700 transition-all duration-200 font-medium shadow-lg flex items-center"
          >
            <UploadCloud className="w-4 h-4 mr-2" />
            Import CSV
          </button>
          <button
            className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg flex items-center"
            onClick={() => setShowAddModal(true)}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Product
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4" style={{ color: 'var(--text-secondary)' }} />
            <input
              type="text"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input pl-10 pr-4 py-2 w-full"
            />
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-3">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="select"
            >
              <option>All Categories</option>
              <option>Electronics</option>
              <option>Accessories</option>
            </select>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="select"
            >
              <option>All Status</option>
              <option>Available</option>
              <option>Unavailable</option>
            </select>
          </div>
        </div>
      </div>

      {/* Products Table */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Product
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Price
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center gap-2">
                    <Tag className="h-4 w-4" />
                    Category
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center gap-2">
                    <Paperclip className="h-4 w-4" />
                    File Size
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Status
                  </div>
                </th>
                <th className="px-6 py-4 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center justify-end gap-2">
                    <Tooltip content="Toggle bot assignment sections for all products">
                      <button
                        onClick={() => {
                          const allExpanded = Object.values(expandedBotSections).every(Boolean)
                          const newState: { [key: string]: boolean } = {}
                          products.forEach(product => {
                            newState[product.id] = !allExpanded
                          })
                          setExpandedBotSections(newState)
                        }}
                        className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded"
                      >
                        <Bot className="h-4 w-4" />
                      </button>
                    </Tooltip>
                    <MoreVertical className="h-4 w-4" />
                    Actions
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {paginatedProducts.map((product) => (
                <tr key={product.id} className="hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-200">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-12 w-12">
                        <div className="h-12 w-12 rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                          <Package className="h-6 w-6 text-white" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-semibold text-gray-900">
                          {product.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {product.description.length > 50
                            ? `${product.description.substring(0, 50)}...`
                            : product.description}
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          {product.tags.slice(0, 2).map((tag, index) => (
                            <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                              {tag}
                            </span>
                          ))}
                          {product.tags.length > 2 && (
                            <span className="text-xs text-gray-500">+{product.tags.length - 2} more</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-semibold text-gray-900">
                      ${product.price.toFixed(2)}
                    </div>
                    <div className="text-sm text-gray-500">
                      {product.currency}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800">
                      {product.category}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center gap-1">
                      <Paperclip className="h-4 w-4 text-gray-400" />
                      {formatFileSize(product.fileSize)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {product.downloadLimit} downloads
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <StatusBadge isActive={product.isActive} />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <Tooltip content="Manage bot assignments for this product">
                        <button
                          onClick={() => handleBotAssignment(product)}
                          className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-all duration-200"
                        >
                          <Bot className="w-4 h-4" />
                        </button>
                      </Tooltip>
                      <button className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200">
                        <MoreVertical className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
                {/* Bot Assignment Section */}
                {expandedBotSections[product.id] && (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 bg-gray-50">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                            <Bot className="w-4 h-4" />
                            Assigned Bots ({product.assignedBots?.length || 0})
                            <TooltipIcon
                              content="Bots assigned to this product can sell it through their Telegram interfaces. Customers can purchase this product through any assigned bot."
                              variant="info"
                            />
                          </h4>
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => handleBotAssignment(product)}
                              className="px-3 py-1 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 text-sm flex items-center gap-1"
                            >
                              <Link className="w-3 h-3" />
                              Assign Bots
                            </button>
                            <button
                              onClick={() => toggleBotSection(product.id)}
                              className="p-1 text-gray-500 hover:text-gray-700"
                            >
                              <ChevronUp className="w-4 h-4" />
                            </button>
                          </div>
                        </div>

                        {product.assignedBots && product.assignedBots.length > 0 ? (
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            {product.assignedBots.map((bot) => (
                              <div key={bot.id} className="bg-white p-3 rounded-lg border border-gray-200 flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <div className={`p-2 rounded-lg ${bot.isActive ? 'bg-green-100' : 'bg-gray-100'}`}>
                                    {bot.isActive ? (
                                      <Power className="w-4 h-4 text-green-600" />
                                    ) : (
                                      <PowerOff className="w-4 h-4 text-gray-600" />
                                    )}
                                  </div>
                                  <div>
                                    <div className="font-medium text-gray-900 text-sm">{bot.name}</div>
                                    <div className="text-xs text-gray-500">@{bot.username}</div>
                                    <div className="text-xs text-gray-500">{bot.customerCount} customers</div>
                                  </div>
                                </div>
                                <div className="flex items-center gap-1">
                                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                    bot.status === 'ACTIVE' ? 'bg-green-100 text-green-800' :
                                    bot.status === 'INACTIVE' ? 'bg-gray-100 text-gray-800' :
                                    'bg-red-100 text-red-800'
                                  }`}>
                                    {bot.status}
                                  </span>
                                  <Tooltip content="Remove bot assignment">
                                    <button
                                      onClick={() => removeBotFromProduct(product.id, bot.id)}
                                      className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded"
                                    >
                                      <Unlink className="w-3 h-3" />
                                    </button>
                                  </Tooltip>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-6 text-gray-500">
                            <Bot className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                            <p className="text-sm">No bots assigned to this product</p>
                            <p className="text-xs">Click "Assign Bots" to start selling this product</p>
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                )}
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
          Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredProducts.length)} of {filteredProducts.length} entries
        </p>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 rounded-lg border border-gray-600 text-sm disabled:opacity-50"
            style={{ color: 'var(--text-secondary)' }}
          >
            Previous
          </button>
          <span className="px-3 py-1 bg-blue-500 text-white rounded-lg text-sm">
            {currentPage}
          </span>
          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 rounded-lg border border-gray-600 text-sm disabled:opacity-50"
            style={{ color: 'var(--text-secondary)' }}
          >
            Next
          </button>
        </div>
      </div>

      {/* Add Product Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="relative mx-auto border w-full max-w-4xl shadow-2xl rounded-2xl bg-white max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-white border-b border-gray-200 px-8 py-6 rounded-t-2xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                    <Plus className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Add New Product</h3>
                </div>
                <button
                  onClick={() => {
                    setShowAddModal(false)
                    resetCreateForm()
                  }}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                >
                  <X className="h-6 w-6 text-gray-500" />
                </button>
              </div>
            </div>

            <form onSubmit={handleCreateProduct} className="p-8 space-y-8">
              {/* Basic Information */}
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-4">
                  <Package className="h-5 w-5 text-blue-500" />
                  <h4 className="text-lg font-semibold text-gray-800">Basic Information</h4>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      <Package className="h-4 w-4 inline mr-1" />
                      Product Name *
                    </label>
                    <input
                      type="text"
                      required
                      value={createProductData.name}
                      onChange={(e) => setCreateProductData(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      placeholder="Enter product name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      <Tag className="h-4 w-4 inline mr-1" />
                      Category *
                    </label>
                    <select
                      required
                      value={createProductData.category}
                      onChange={(e) => setCreateProductData(prev => ({ ...prev, category: e.target.value }))}
                      className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    >
                      <option value="">Select category</option>
                      <option value="Templates">Templates</option>
                      <option value="Education">Education</option>
                      <option value="Software">Software</option>
                      <option value="Graphics">Graphics</option>
                      <option value="Audio">Audio</option>
                      <option value="Video">Video</option>
                      <option value="Documents">Documents</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <FileText className="h-4 w-4 inline mr-1" />
                    Description *
                  </label>
                  <textarea
                    required
                    rows={4}
                    value={createProductData.description}
                    onChange={(e) => setCreateProductData(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="Describe your product in detail..."
                  />
                </div>
              </div>

              {/* Pricing & Settings */}
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-4">
                  <DollarSign className="h-5 w-5 text-green-500" />
                  <h4 className="text-lg font-semibold text-gray-800">Pricing & Settings</h4>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      <DollarSign className="h-4 w-4 inline mr-1" />
                      Price *
                    </label>
                    <input
                      type="number"
                      required
                      min="0"
                      step="0.01"
                      value={createProductData.price}
                      onChange={(e) => setCreateProductData(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                      className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Currency
                    </label>
                    <select
                      value={createProductData.currency}
                      onChange={(e) => setCreateProductData(prev => ({ ...prev, currency: e.target.value }))}
                      className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    >
                      <option value="USD">USD</option>
                      <option value="BITCOIN">Bitcoin</option>
                      <option value="ETHEREUM">Ethereum</option>
                      <option value="USDT">USDT</option>
                      <option value="LITECOIN">Litecoin</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Download Limit
                    </label>
                    <input
                      type="number"
                      min="1"
                      value={createProductData.downloadLimit}
                      onChange={(e) => setCreateProductData(prev => ({ ...prev, downloadLimit: parseInt(e.target.value) || 3 }))}
                      className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      placeholder="3"
                    />
                  </div>
                </div>

                {/* Store Selection */}
                {(user?.role === 'ADMIN' || user?.role === 'MANAGER') && stores.length > 0 && (
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      <StoreIcon className="h-4 w-4 inline mr-1" />
                      Store *
                    </label>
                    <select
                      required
                      value={createProductData.storeId}
                      onChange={(e) => setCreateProductData(prev => ({ ...prev, storeId: e.target.value }))}
                      className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    >
                      <option value="">Select store</option>
                      {stores.map((store) => (
                        <option key={store.id} value={store.id}>
                          {store.name}
                        </option>
                      ))}
                    </select>
                  </div>
                )}
              </div>

              {/* Tags */}
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-4">
                  <Tag className="h-5 w-5 text-purple-500" />
                  <h4 className="text-lg font-semibold text-gray-800">Tags</h4>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Product Tags
                  </label>
                  <div className="flex gap-2 mb-3">
                    <input
                      type="text"
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                      className="flex-1 border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      placeholder="Enter tag and press Enter"
                    />
                    <button
                      type="button"
                      onClick={addTag}
                      className="px-4 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors duration-200"
                    >
                      Add
                    </button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {createProductData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                      >
                        {tag}
                        <button
                          type="button"
                          onClick={() => removeTag(tag)}
                          className="ml-2 text-blue-600 hover:text-blue-800"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {/* File Upload */}
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-4">
                  <Paperclip className="h-5 w-5 text-orange-500" />
                  <h4 className="text-lg font-semibold text-gray-800">File Upload</h4>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Main Product File */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Product File *
                    </label>
                    <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-500 transition-colors duration-200">
                      <input
                        type="file"
                        onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'file')}
                        className="hidden"
                        id="product-file"
                        accept=".zip,.rar,.pdf,.doc,.docx,.mp4,.mp3,.jpg,.png,.gif"
                      />
                      <label htmlFor="product-file" className="cursor-pointer">
                        <Paperclip className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-600">
                          {createProductData.file ? createProductData.file.name : 'Click to upload main file'}
                        </p>
                      </label>
                    </div>
                  </div>

                  {/* Preview File */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Preview File (Optional)
                    </label>
                    <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-500 transition-colors duration-200">
                      <input
                        type="file"
                        onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'previewFile')}
                        className="hidden"
                        id="preview-file"
                        accept=".jpg,.png,.gif,.pdf,.mp4"
                      />
                      <label htmlFor="preview-file" className="cursor-pointer">
                        <Image className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-600">
                          {createProductData.previewFile ? createProductData.previewFile.name : 'Click to upload preview'}
                        </p>
                      </label>
                    </div>
                  </div>

                  {/* Thumbnail */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Thumbnail (Optional)
                    </label>
                    <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-500 transition-colors duration-200">
                      <input
                        type="file"
                        onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'thumbnailFile')}
                        className="hidden"
                        id="thumbnail-file"
                        accept=".jpg,.png,.gif"
                      />
                      <label htmlFor="thumbnail-file" className="cursor-pointer">
                        <Image className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-600">
                          {createProductData.thumbnailFile ? createProductData.thumbnailFile.name : 'Click to upload thumbnail'}
                        </p>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              {/* Submit Buttons */}
              <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => {
                    setShowAddModal(false)
                    resetCreateForm()
                  }}
                  className="px-6 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isCreating}
                  className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isCreating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white inline mr-2"></div>
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 inline mr-2" />
                      Create Product
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* CSV Import Modal */}
      {showImportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="relative mx-auto border w-full max-w-6xl shadow-2xl rounded-2xl bg-white max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-white border-b border-gray-200 px-8 py-6 rounded-t-2xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg">
                    <UploadCloud className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Import Products from CSV</h3>
                </div>
                <button
                  onClick={() => {
                    setShowImportModal(false)
                    resetImportState()
                  }}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                >
                  <X className="h-6 w-6 text-gray-500" />
                </button>
              </div>
            </div>

            <div className="p-8 space-y-8">
              {!showImportPreview && !importResult && (
                <>
                  {/* File Upload Section */}
                  <div className="space-y-6">
                    <div className="flex items-center gap-2 mb-4">
                      <FileSpreadsheet className="h-5 w-5 text-orange-500" />
                      <h4 className="text-lg font-semibold text-gray-800">Upload CSV File</h4>
                    </div>

                    <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-orange-500 transition-colors duration-200">
                      <input
                        type="file"
                        accept=".csv"
                        onChange={(e) => e.target.files?.[0] && handleCSVFileUpload(e.target.files[0])}
                        className="hidden"
                        id="csv-file-upload"
                      />
                      <label htmlFor="csv-file-upload" className="cursor-pointer">
                        <FileSpreadsheet className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                        <p className="text-lg font-medium text-gray-700 mb-2">
                          {csvFile ? csvFile.name : 'Click to upload CSV file or drag and drop'}
                        </p>
                        <p className="text-sm text-gray-500">
                          Supports CSV files with product data
                        </p>
                      </label>
                    </div>

                    {/* CSV Format Guide */}
                    <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <FileCheck className="h-5 w-5 text-blue-500" />
                          <h5 className="font-semibold text-blue-800">CSV Format Requirements</h5>
                        </div>
                        <a
                          href="/sample-products.csv"
                          download="sample-products.csv"
                          className="px-3 py-1 bg-blue-500 text-white rounded-lg text-sm hover:bg-blue-600 transition-colors duration-200 flex items-center gap-1"
                        >
                          <DownloadCloud className="h-3 w-3" />
                          Download Sample
                        </a>
                      </div>
                      <div className="text-sm text-blue-700 space-y-2">
                        <p><strong>Required Headers:</strong> name, description, price, currency, category, tags, downloadLimit, storeId, isActive</p>
                        <p><strong>Valid Categories:</strong> Templates, Education, Software, Graphics, Audio, Video, Documents, Other</p>
                        <p><strong>Valid Currencies:</strong> USD, BITCOIN, ETHEREUM, USDT, LITECOIN</p>
                        <p><strong>Tags Format:</strong> Comma-separated values within quotes (e.g., "tag1,tag2,tag3")</p>
                        <p><strong>Boolean Fields:</strong> Use true/false, 1/0, or yes/no for isActive</p>
                        <p><strong>Store ID:</strong> Use "default-store" or get valid store IDs from your stores list</p>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* Import Preview */}
              {showImportPreview && !importResult && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <FileCheck className="h-5 w-5 text-green-500" />
                      <h4 className="text-lg font-semibold text-gray-800">Import Preview</h4>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-sm text-gray-600">
                        {importPreview.length} valid products ready to import
                      </span>
                      {validationErrors.length > 0 && (
                        <span className="text-sm text-red-600">
                          {validationErrors.length} errors found
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Validation Errors */}
                  {validationErrors.length > 0 && (
                    <div className="bg-red-50 border border-red-200 rounded-xl p-6">
                      <div className="flex items-center gap-2 mb-3">
                        <AlertTriangle className="h-5 w-5 text-red-500" />
                        <h5 className="font-semibold text-red-800">Validation Errors</h5>
                      </div>
                      <div className="max-h-40 overflow-y-auto space-y-2">
                        {validationErrors.map((error, index) => (
                          <div key={index} className="text-sm text-red-700 bg-red-100 p-2 rounded">
                            <strong>Row {error.row}:</strong> {error.message} (Field: {error.field})
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Preview Table */}
                  {importPreview.length > 0 && (
                    <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
                      <div className="overflow-x-auto max-h-96">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Price</th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Category</th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Store</th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {importPreview.slice(0, 10).map((product, index) => (
                              <tr key={index} className="hover:bg-gray-50">
                                <td className="px-4 py-3 text-sm text-gray-900">{product.name}</td>
                                <td className="px-4 py-3 text-sm text-gray-900">{product.price} {product.currency}</td>
                                <td className="px-4 py-3 text-sm text-gray-900">{product.category}</td>
                                <td className="px-4 py-3 text-sm text-gray-900">{product.storeId}</td>
                                <td className="px-4 py-3 text-sm">
                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                    product.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                  }`}>
                                    {product.isActive ? 'Active' : 'Inactive'}
                                  </span>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                      {importPreview.length > 10 && (
                        <div className="px-4 py-3 bg-gray-50 text-sm text-gray-600">
                          Showing first 10 of {importPreview.length} products
                        </div>
                      )}
                    </div>
                  )}

                  {/* Import Actions */}
                  <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <button
                      onClick={() => {
                        setShowImportPreview(false)
                        resetImportState()
                      }}
                      className="px-6 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleImportProducts}
                      disabled={isImporting || importPreview.length === 0}
                      className="px-6 py-3 bg-gradient-to-r from-orange-500 to-red-600 text-white rounded-xl hover:from-orange-600 hover:to-red-700 transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isImporting ? (
                        <>
                          <Loader2 className="h-4 w-4 inline mr-2 animate-spin" />
                          Importing...
                        </>
                      ) : (
                        <>
                          <UploadCloud className="h-4 w-4 inline mr-2" />
                          Import {importPreview.length} Products
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}

              {/* Import Results */}
              {importResult && (
                <div className="space-y-6">
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="h-5 w-5 text-green-500" />
                    <h4 className="text-lg font-semibold text-gray-800">Import Results</h4>
                  </div>

                  {/* Results Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="h-5 w-5 text-green-500" />
                        <span className="font-semibold text-green-800">Successful</span>
                      </div>
                      <p className="text-2xl font-bold text-green-900">{importResult.success}</p>
                    </div>
                    <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                      <div className="flex items-center gap-2">
                        <XCircle className="h-5 w-5 text-red-500" />
                        <span className="font-semibold text-red-800">Failed</span>
                      </div>
                      <p className="text-2xl font-bold text-red-900">{importResult.failed}</p>
                    </div>
                    <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                      <div className="flex items-center gap-2">
                        <FileSpreadsheet className="h-5 w-5 text-blue-500" />
                        <span className="font-semibold text-blue-800">Total</span>
                      </div>
                      <p className="text-2xl font-bold text-blue-900">{importResult.total}</p>
                    </div>
                  </div>

                  {/* Error Details */}
                  {importResult.errors.length > 0 && (
                    <div className="bg-red-50 border border-red-200 rounded-xl p-6">
                      <div className="flex items-center gap-2 mb-3">
                        <FileX className="h-5 w-5 text-red-500" />
                        <h5 className="font-semibold text-red-800">Import Errors</h5>
                      </div>
                      <div className="max-h-40 overflow-y-auto space-y-2">
                        {importResult.errors.map((error, index) => (
                          <div key={index} className="text-sm text-red-700 bg-red-100 p-2 rounded">
                            <strong>Row {error.row}:</strong> {error.message}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Close Button */}
                  <div className="flex justify-end pt-6 border-t border-gray-200">
                    <button
                      onClick={() => {
                        setShowImportModal(false)
                        resetImportState()
                      }}
                      className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg"
                    >
                      <CheckCircle2 className="h-4 w-4 inline mr-2" />
                      Done
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Bot Assignment Modal */}
      {showBotAssignmentModal && selectedProductForBots && (
        <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="relative mx-auto border w-full max-w-4xl shadow-2xl rounded-2xl bg-white max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-white border-b border-gray-200 px-8 py-6 rounded-t-2xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                    <Bot className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900">Bot Assignment</h3>
                    <p className="text-sm text-gray-600">Assign bots to "{selectedProductForBots.name}"</p>
                  </div>
                </div>
                <button
                  onClick={() => {
                    setShowBotAssignmentModal(false)
                    setSelectedProductForBots(null)
                    setSelectedBots([])
                  }}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                >
                  <X className="h-6 w-6 text-gray-500" />
                </button>
              </div>
            </div>

            <div className="p-8 space-y-6">
              {/* Product Info */}
              <div className="bg-gray-50 p-4 rounded-xl">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                    <Package className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{selectedProductForBots.name}</h4>
                    <p className="text-sm text-gray-600">{selectedProductForBots.description}</p>
                    <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                      <span>${selectedProductForBots.price} {selectedProductForBots.currency}</span>
                      <span>•</span>
                      <span>{selectedProductForBots.category}</span>
                      <span>•</span>
                      <span>Currently assigned to {selectedProductForBots.assignedBots?.length || 0} bots</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Bot Search */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Search Bots
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search by bot name or username..."
                    value={botSearchTerm}
                    onChange={(e) => setBotSearchTerm(e.target.value)}
                    className="w-full border border-gray-300 rounded-xl px-10 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  />
                </div>
              </div>

              {/* Bulk Actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <button
                    onClick={() => handleBulkBotSelection(true)}
                    className="px-3 py-1 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 text-sm"
                  >
                    Select All
                  </button>
                  <button
                    onClick={() => handleBulkBotSelection(false)}
                    className="px-3 py-1 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200 text-sm"
                  >
                    Deselect All
                  </button>
                </div>
                <div className="text-sm text-gray-600">
                  {selectedBots.length} of {filteredBots.length} bots selected
                </div>
              </div>

              {/* Available Bots */}
              <div>
                <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Bot className="w-5 h-5" />
                  Available Bots ({filteredBots.length})
                  <TooltipIcon
                    content="Select which bots should be able to sell this product. Active bots will immediately start offering this product to their customers."
                    variant="info"
                  />
                </h4>

                {filteredBots.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                    {filteredBots.map((bot) => (
                      <div
                        key={bot.id}
                        onClick={() => toggleBotSelection(bot.id)}
                        className={`p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                          selectedBots.includes(bot.id)
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className={`p-2 rounded-lg ${bot.isActive ? 'bg-green-100' : 'bg-gray-100'}`}>
                              {bot.isActive ? (
                                <Power className="w-5 h-5 text-green-600" />
                              ) : (
                                <PowerOff className="w-5 h-5 text-gray-600" />
                              )}
                            </div>
                            <div>
                              <div className="font-semibold text-gray-900">{bot.name}</div>
                              <div className="text-sm text-gray-500">@{bot.username}</div>
                              <div className="flex items-center gap-2 mt-1">
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                  bot.status === 'ACTIVE' ? 'bg-green-100 text-green-800' :
                                  bot.status === 'INACTIVE' ? 'bg-gray-100 text-gray-800' :
                                  'bg-red-100 text-red-800'
                                }`}>
                                  {bot.status}
                                </span>
                                <span className="text-xs text-gray-500">
                                  <Users className="w-3 h-3 inline mr-1" />
                                  {bot.customerCount} customers
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center">
                            {selectedBots.includes(bot.id) && (
                              <CheckCircle className="w-6 h-6 text-blue-500" />
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Bot className="w-12 h-12 mx-auto mb-3 text-gray-400" />
                    <p className="text-lg font-medium">No bots found</p>
                    <p className="text-sm">Try adjusting your search criteria</p>
                  </div>
                )}
              </div>

              {/* Assignment Summary */}
              {selectedBots.length > 0 && (
                <div className="bg-blue-50 p-4 rounded-xl">
                  <h5 className="font-semibold text-blue-900 mb-2 flex items-center gap-2">
                    <CheckCircle className="w-4 h-4" />
                    Assignment Summary
                  </h5>
                  <div className="text-sm text-blue-800">
                    <p>• {selectedBots.length} bots will be assigned to this product</p>
                    <p>• Active bots will immediately start selling this product</p>
                    <p>• Customers can purchase through any assigned bot</p>
                    <p>• You can modify assignments at any time</p>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button
                  onClick={() => {
                    setShowBotAssignmentModal(false)
                    setSelectedProductForBots(null)
                    setSelectedBots([])
                  }}
                  className="px-6 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium"
                >
                  Cancel
                </button>
                <button
                  onClick={assignBotsToProduct}
                  disabled={isAssigningBots || selectedBots.length === 0}
                  className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isAssigningBots ? (
                    <>
                      <Loader2 className="h-4 w-4 inline mr-2 animate-spin" />
                      Assigning...
                    </>
                  ) : (
                    <>
                      <Link className="h-4 w-4 inline mr-2" />
                      Assign {selectedBots.length} Bot{selectedBots.length !== 1 ? 's' : ''}
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default TeleShopProducts
