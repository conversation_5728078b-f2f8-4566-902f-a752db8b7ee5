import React, { useState } from 'react'
import {
  Trash2,
  AlertTriangle,
  Shield,
  Download,
  Database,
  Users,
  ShoppingCart,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  Loader2,
  Bot,
  Archive,
  RotateCcw,
  Calendar,
  HardDrive,
  Zap,
  Activity,
  DollarSign,
  Package
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface BotDeletionManagerProps {
  botId: string
  botName: string
  botData: {
    totalCustomers: number
    totalOrders: number
    totalRevenue: number
    createdAt: string
    lastActivity?: string
  }
  onDelete: (deletionType: 'soft' | 'permanent') => void
  onCancel: () => void
}

interface DeletionOption {
  type: 'soft' | 'permanent'
  title: string
  description: string
  recoverable: boolean
  dataRetention: string
  icon: React.ComponentType<any>
  color: string
}

interface DataExportOption {
  type: string
  name: string
  description: string
  size: string
  selected: boolean
}

interface ImpactAnalysis {
  customers: {
    total: number
    active: number
    withOrders: number
  }
  orders: {
    total: number
    pending: number
    completed: number
    revenue: number
  }
  data: {
    messages: number
    files: number
    logs: number
    totalSize: string
  }
}

const BotDeletionManager: React.FC<BotDeletionManagerProps> = ({
  botId,
  botName,
  botData,
  onDelete,
  onCancel
}) => {
  const [currentStep, setCurrentStep] = useState(1)
  const [selectedDeletionType, setSelectedDeletionType] = useState<'soft' | 'permanent' | null>(null)
  const [exportOptions, setExportOptions] = useState<DataExportOption[]>([
    { type: 'customers', name: 'Customer Data', description: 'User profiles and contact information', size: '2.3 MB', selected: true },
    { type: 'orders', name: 'Order History', description: 'Complete order records and transactions', size: '5.7 MB', selected: true },
    { type: 'messages', name: 'Message Logs', description: 'Chat history and interactions', size: '12.1 MB', selected: false },
    { type: 'analytics', name: 'Analytics Data', description: 'Performance metrics and reports', size: '1.8 MB', selected: true },
    { type: 'settings', name: 'Bot Configuration', description: 'Settings and customizations', size: '0.5 MB', selected: true }
  ])
  const [confirmationText, setConfirmationText] = useState('')
  const [processing, setProcessing] = useState(false)
  const [exportingData, setExportingData] = useState(false)
  const [recoveryPeriod, setRecoveryPeriod] = useState(30)

  const deletionOptions: DeletionOption[] = [
    {
      type: 'soft',
      title: 'Soft Deletion (Recommended)',
      description: 'Deactivate the bot and preserve all data for potential recovery',
      recoverable: true,
      dataRetention: `${recoveryPeriod} days`,
      icon: Archive,
      color: 'orange'
    },
    {
      type: 'permanent',
      title: 'Permanent Deletion',
      description: 'Completely remove the bot and all associated data',
      recoverable: false,
      dataRetention: 'Immediate',
      icon: Trash2,
      color: 'red'
    }
  ]

  const impactAnalysis: ImpactAnalysis = {
    customers: {
      total: botData.totalCustomers,
      active: Math.floor(botData.totalCustomers * 0.3),
      withOrders: Math.floor(botData.totalCustomers * 0.68)
    },
    orders: {
      total: botData.totalOrders,
      pending: Math.floor(botData.totalOrders * 0.05),
      completed: Math.floor(botData.totalOrders * 0.95),
      revenue: botData.totalRevenue
    },
    data: {
      messages: Math.floor(botData.totalOrders * 15),
      files: Math.floor(botData.totalOrders * 2.3),
      logs: Math.floor(botData.totalOrders * 8.7),
      totalSize: '22.4 MB'
    }
  }

  const handleExportData = async () => {
    setExportingData(true)
    try {
      const selectedData = exportOptions.filter(option => option.selected)
      
      for (const option of selectedData) {
        await new Promise(resolve => setTimeout(resolve, 1000))
        toast.loading(`Exporting ${option.name}...`, { id: 'export' })
      }
      
      // Simulate file download
      const exportData = {
        botId,
        botName,
        exportDate: new Date().toISOString(),
        data: selectedData.map(option => ({
          type: option.type,
          name: option.name,
          size: option.size
        }))
      }
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${botName}_backup_${new Date().toISOString().split('T')[0]}.json`
      a.click()
      window.URL.revokeObjectURL(url)
      
      toast.success('Data exported successfully!', { id: 'export' })
      setCurrentStep(3)
    } catch (error) {
      toast.error('Export failed', { id: 'export' })
    } finally {
      setExportingData(false)
    }
  }

  const handleConfirmDeletion = async () => {
    if (!selectedDeletionType) return
    
    const expectedText = selectedDeletionType === 'permanent' 
      ? `DELETE ${botName}` 
      : `DEACTIVATE ${botName}`
    
    if (confirmationText !== expectedText) {
      toast.error(`Please type "${expectedText}" to confirm`)
      return
    }

    setProcessing(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      toast.success(
        selectedDeletionType === 'permanent' 
          ? 'Bot permanently deleted' 
          : 'Bot deactivated and archived'
      )
      
      onDelete(selectedDeletionType)
    } catch (error) {
      toast.error('Deletion failed')
    } finally {
      setProcessing(false)
    }
  }

  const toggleExportOption = (type: string) => {
    setExportOptions(options =>
      options.map(option =>
        option.type === type ? { ...option, selected: !option.selected } : option
      )
    )
  }

  const steps = [
    { id: 1, title: 'Impact Analysis', description: 'Review what will be affected' },
    { id: 2, title: 'Data Export', description: 'Backup important data' },
    { id: 3, title: 'Deletion Type', description: 'Choose deletion method' },
    { id: 4, title: 'Confirmation', description: 'Confirm your decision' }
  ]

  if (processing) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
        <div className="relative mx-auto border w-full max-w-md shadow-2xl rounded-2xl bg-white">
          <div className="p-8 text-center">
            <div className="mb-6">
              <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <Loader2 className="w-8 h-8 text-red-600 animate-spin" />
              </div>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">
              {selectedDeletionType === 'permanent' ? 'Deleting' : 'Deactivating'} Bot
            </h3>
            <p className="text-gray-600 mb-4">
              Please wait while we process your request. This may take a few moments.
            </p>
            <div className="bg-gray-50 p-4 rounded-xl">
              <div className="flex items-center gap-3">
                <Bot className="w-5 h-5 text-gray-600" />
                <div className="text-left">
                  <div className="font-semibold text-gray-900">{botName}</div>
                  <div className="text-sm text-gray-500">
                    {selectedDeletionType === 'permanent' ? 'Permanently deleting...' : 'Archiving...'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
      <div className="relative mx-auto border w-full max-w-4xl shadow-2xl rounded-2xl bg-white">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-red-100 rounded-lg">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900">Bot Deletion Manager</h3>
              <p className="text-sm text-gray-600">{botName} - Data Impact & Recovery Options</p>
            </div>
          </div>
          <button
            onClick={onCancel}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          >
            <XCircle className="h-6 w-6 text-gray-500" />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <React.Fragment key={step.id}>
                <div className="flex items-center">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                    currentStep >= step.id
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {currentStep > step.id ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : (
                      step.id
                    )}
                  </div>
                  <div className="ml-3">
                    <div className="text-sm font-medium text-gray-900">{step.title}</div>
                    <div className="text-xs text-gray-500">{step.description}</div>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`flex-1 h-1 mx-4 rounded ${
                    currentStep > step.id ? 'bg-blue-500' : 'bg-gray-200'
                  }`} />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>

        <div className="p-6 max-h-96 overflow-y-auto">
          {/* Step 1: Impact Analysis */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="bg-red-50 p-6 rounded-xl border border-red-200">
                <h4 className="font-bold text-red-900 mb-2 flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5" />
                  Deletion Impact Analysis
                </h4>
                <p className="text-red-800 text-sm">
                  Deleting this bot will affect the following data and users. Please review carefully.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-blue-50 p-6 rounded-xl">
                  <div className="flex items-center gap-3 mb-4">
                    <Users className="w-6 h-6 text-blue-600" />
                    <h5 className="font-semibold text-blue-900">Customer Impact</h5>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-blue-700">Total Customers:</span>
                      <span className="font-semibold text-blue-900">{impactAnalysis.customers.total.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-blue-700">Active Users:</span>
                      <span className="font-semibold text-blue-900">{impactAnalysis.customers.active.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-blue-700">With Orders:</span>
                      <span className="font-semibold text-blue-900">{impactAnalysis.customers.withOrders.toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 p-6 rounded-xl">
                  <div className="flex items-center gap-3 mb-4">
                    <ShoppingCart className="w-6 h-6 text-green-600" />
                    <h5 className="font-semibold text-green-900">Order Impact</h5>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-green-700">Total Orders:</span>
                      <span className="font-semibold text-green-900">{impactAnalysis.orders.total.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-green-700">Pending:</span>
                      <span className="font-semibold text-green-900">{impactAnalysis.orders.pending.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-green-700">Revenue:</span>
                      <span className="font-semibold text-green-900">${impactAnalysis.orders.revenue.toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-purple-50 p-6 rounded-xl">
                  <div className="flex items-center gap-3 mb-4">
                    <Database className="w-6 h-6 text-purple-600" />
                    <h5 className="font-semibold text-purple-900">Data Impact</h5>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-purple-700">Messages:</span>
                      <span className="font-semibold text-purple-900">{impactAnalysis.data.messages.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-purple-700">Files:</span>
                      <span className="font-semibold text-purple-900">{impactAnalysis.data.files.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-purple-700">Total Size:</span>
                      <span className="font-semibold text-purple-900">{impactAnalysis.data.totalSize}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 p-4 rounded-xl border border-yellow-200">
                <h5 className="font-semibold text-yellow-900 mb-2 flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  Important Considerations
                </h5>
                <ul className="text-sm text-yellow-800 space-y-1">
                  <li>• Active customers will lose access to the bot immediately</li>
                  <li>• Pending orders may need manual processing</li>
                  <li>• Customer support requests will need alternative handling</li>
                  <li>• Revenue tracking and analytics will be affected</li>
                </ul>
              </div>
            </div>
          )}

          {/* Step 2: Data Export */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div className="bg-blue-50 p-6 rounded-xl">
                <h4 className="font-bold text-blue-900 mb-2 flex items-center gap-2">
                  <Download className="w-5 h-5" />
                  Data Export & Backup
                </h4>
                <p className="text-blue-800 text-sm">
                  Select the data you want to export before deletion. This creates a backup for your records.
                </p>
              </div>

              <div className="space-y-3">
                {exportOptions.map((option) => (
                  <div
                    key={option.type}
                    onClick={() => toggleExportOption(option.type)}
                    className={`p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                      option.selected
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                          option.selected
                            ? 'border-blue-500 bg-blue-500'
                            : 'border-gray-300'
                        }`}>
                          {option.selected && <CheckCircle className="w-3 h-3 text-white" />}
                        </div>
                        <div>
                          <div className="font-semibold text-gray-900">{option.name}</div>
                          <div className="text-sm text-gray-600">{option.description}</div>
                        </div>
                      </div>
                      <div className="text-sm text-gray-500">{option.size}</div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600">
                  Selected: {exportOptions.filter(o => o.selected).length} of {exportOptions.length} items
                </div>
                <button
                  onClick={handleExportData}
                  disabled={exportingData || !exportOptions.some(o => o.selected)}
                  className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  {exportingData ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Exporting...
                    </>
                  ) : (
                    <>
                      <Download className="w-4 h-4" />
                      Export Selected Data
                    </>
                  )}
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Navigation */}
        <div className="flex justify-between items-center p-6 border-t border-gray-200">
          <button
            onClick={currentStep === 1 ? onCancel : () => setCurrentStep(currentStep - 1)}
            className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200 font-medium"
          >
            {currentStep === 1 ? 'Cancel' : 'Previous'}
          </button>

          {currentStep < 4 && (
            <button
              onClick={() => setCurrentStep(currentStep + 1)}
              disabled={currentStep === 2 && exportingData}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

export default BotDeletionManager
