import React, { useState, useEffect } from 'react'
import {
  Edit,
  Save,
  <PERSON><PERSON>ef<PERSON>,
  <PERSON>t,
  MessageSquare,
  CreditCard,
  Palette,
  Package,
  Shield,
  Settings,
  Globe,
  Users,
  Zap,
  Eye,
  EyeOff,
  Copy,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Loader2,
  History,
  RotateCcw,
  Smartphone,
  Languages,
  DollarSign,
  Info,
  Sparkles,
  HelpCircle,
  Hash,
  MapPin,
  Calendar,
  Timer,
  Percent,
  Upload,
  Download,
  Mail,
  Bell,
  Wifi,
  Lock,
  Unlock,
  UserCheck,
  Activity,
  BarChart3,
  Search
} from 'lucide-react'
import { toast } from 'react-hot-toast'
import Tooltip, { TooltipIcon } from './Tooltip'

interface BotEditorProps {
  botId: string
  onBack: () => void
  onSave: (botData: any) => void
}

interface BotData {
  id: string
  name: string
  username: string
  token: string
  chatId?: string
  description?: string
  isActive: boolean
  status: 'ACTIVE' | 'INACTIVE' | 'ERROR' | 'SETUP'
  webhookUrl?: string
  webhookStatus: 'CONNECTED' | 'DISCONNECTED' | 'ERROR'
  settings: {
    welcomeMessage: string
    language: string
    currency: string
    timezone: string
    dateFormat: string
    numberFormat: string
    paymentMethods: string[]
    paymentLimits: {
      [key: string]: { min: number; max: number; fee: number }
    }
    confirmationTimeout: number
    supportContact?: string
    storeInfo?: string
    theme: {
      primaryColor: string
      accentColor: string
      template: string
      customCSS?: string
      logoUrl?: string
      logoPosition: string
    }
    features: {
      categories: boolean
      userProfiles: boolean
      balanceTopUp: boolean
      orderHistory: boolean
      customerSupport: boolean
      analytics: boolean
    }
    notifications: {
      orderReceived: boolean
      paymentConfirmed: boolean
      lowStock: boolean
      emailNotifications: boolean
      telegramNotifications: boolean
    }
    security: {
      rateLimiting: boolean
      rateLimitRpm: number
      rateLimitRph: number
      spamProtection: boolean
      spamSensitivity: string
      adminNotifications: boolean
      ipWhitelist: string[]
      ipBlacklist: string[]
      twoFactorAuth: boolean
      sessionTimeout: number
    }
  }
  versions?: Array<{
    id: string
    timestamp: string
    changes: string[]
    author: string
  }>
}

interface Product {
  id: string
  name: string
  description: string
  price: number
  currency: string
  category: string
  isActive: boolean
  isAssigned?: boolean
  assignedAt?: string
}

const BotEditor: React.FC<BotEditorProps> = ({ botId, onBack, onSave }) => {
  const [botData, setBotData] = useState<BotData | null>(null)
  const [originalData, setOriginalData] = useState<BotData | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('general')
  const [showToken, setShowToken] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [showVersionHistory, setShowVersionHistory] = useState(false)
  const [previewMode, setPreviewMode] = useState(false)

  // Product assignment state
  const [availableProducts, setAvailableProducts] = useState<Product[]>([])
  const [assignedProducts, setAssignedProducts] = useState<Product[]>([])
  const [productSearchTerm, setProductSearchTerm] = useState('')
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [isAssigningProducts, setIsAssigningProducts] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState('All Categories')

  useEffect(() => {
    fetchBotData()
    fetchProducts()
  }, [botId])

  useEffect(() => {
    if (botData && originalData) {
      setHasChanges(JSON.stringify(botData) !== JSON.stringify(originalData))
    }
  }, [botData, originalData])

  const fetchBotData = async () => {
    try {
      setLoading(true)
      
      // Mock bot data for editing
      const mockBotData: BotData = {
        id: botId,
        name: 'Digital Store Bot',
        username: 'digitalstore_bot',
        token: '1234567890:AAEhBOweik6ad6PsVQF6XJhiknbhd6k-Ks',
        chatId: '123456789',
        description: 'Premium digital products and software marketplace',
        isActive: true,
        status: 'ACTIVE',
        webhookUrl: 'https://api.teleshop.com/webhook/bot_001',
        webhookStatus: 'CONNECTED',
        settings: {
          welcomeMessage: 'Welcome to Digital Store! 🛍️ Browse our premium digital products and find exactly what you need.',
          language: 'en',
          currency: 'USD',
          timezone: 'UTC',
          dateFormat: 'MM/DD/YYYY',
          numberFormat: 'en-US',
          paymentMethods: ['BITCOIN', 'ETHEREUM', 'USDT'],
          paymentLimits: {
            BITCOIN: { min: 0.001, max: 10, fee: 0.0005 },
            ETHEREUM: { min: 0.01, max: 100, fee: 0.005 },
            USDT: { min: 10, max: 10000, fee: 1 }
          },
          confirmationTimeout: 30,
          supportContact: '@digitalsupport',
          storeInfo: 'Premium digital marketplace with instant delivery and 24/7 support.',
          theme: {
            primaryColor: '#3B82F6',
            accentColor: '#8B5CF6',
            template: 'professional',
            customCSS: '',
            logoUrl: '',
            logoPosition: 'center'
          },
          features: {
            categories: true,
            userProfiles: true,
            balanceTopUp: true,
            orderHistory: true,
            customerSupport: true,
            analytics: true
          },
          notifications: {
            orderReceived: true,
            paymentConfirmed: true,
            lowStock: false,
            emailNotifications: true,
            telegramNotifications: true
          },
          security: {
            rateLimiting: true,
            rateLimitRpm: 60,
            rateLimitRph: 1000,
            spamProtection: true,
            spamSensitivity: 'medium',
            adminNotifications: true,
            ipWhitelist: [],
            ipBlacklist: [],
            twoFactorAuth: false,
            sessionTimeout: 3600
          }
        },
        versions: [
          {
            id: 'v1.3',
            timestamp: new Date(Date.now() - 86400000).toISOString(),
            changes: ['Updated welcome message', 'Added USDT payment method'],
            author: 'John Seller'
          },
          {
            id: 'v1.2',
            timestamp: new Date(Date.now() - 172800000).toISOString(),
            changes: ['Changed primary color', 'Enabled user profiles'],
            author: 'John Seller'
          },
          {
            id: 'v1.1',
            timestamp: new Date(Date.now() - 259200000).toISOString(),
            changes: ['Initial bot setup', 'Basic configuration'],
            author: 'John Seller'
          }
        ]
      }
      
      setBotData(mockBotData)
      setOriginalData(JSON.parse(JSON.stringify(mockBotData)))
      setLoading(false)
    } catch (error) {
      console.error('Error fetching bot data:', error)
      toast.error('Failed to load bot data')
      setLoading(false)
    }
  }

  const handleSave = async () => {
    if (!botData) return

    setSaving(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      toast.success('Bot configuration saved successfully!')
      setOriginalData(JSON.parse(JSON.stringify(botData)))
      setHasChanges(false)
      onSave(botData)
    } catch (error) {
      console.error('Error saving bot:', error)
      toast.error('Failed to save bot configuration')
    } finally {
      setSaving(false)
    }
  }

  const handleReset = () => {
    if (originalData) {
      setBotData(JSON.parse(JSON.stringify(originalData)))
      setHasChanges(false)
      toast.success('Changes reset to last saved version')
    }
  }

  const handleVersionRestore = (version: any) => {
    // In a real implementation, this would fetch the specific version data
    toast.success(`Restored to version ${version.id}`)
    setShowVersionHistory(false)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied to clipboard!')
  }

  const togglePaymentMethod = (methodId: string) => {
    const currentMethods = botData.settings.paymentMethods
    const newMethods = currentMethods.includes(methodId)
      ? currentMethods.filter(id => id !== methodId)
      : [...currentMethods, methodId]

    setBotData({
      ...botData,
      settings: { ...botData.settings, paymentMethods: newMethods }
    })
  }

  const toggleFeature = (featureKey: string) => {
    setBotData({
      ...botData,
      settings: {
        ...botData.settings,
        features: {
          ...botData.settings.features,
          [featureKey]: !botData.settings.features[featureKey as keyof typeof botData.settings.features]
        }
      }
    })
  }

  const updateThemeColor = (colorType: 'primaryColor' | 'accentColor', color: string) => {
    setBotData({
      ...botData,
      settings: {
        ...botData.settings,
        theme: {
          ...botData.settings.theme,
          [colorType]: color
        }
      }
    })
  }

  const fetchProducts = async () => {
    try {
      // Try to fetch from API first, fallback to mock data
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/products`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })

        if (response.ok) {
          const data = await response.json()
          const products = data.data || []
          setAvailableProducts(products)

          // Set assigned products based on bot assignments
          const assigned = products.filter((product: any) =>
            product.assignedBots?.some((bot: any) => bot.id === botId)
          )
          setAssignedProducts(assigned)
        } else {
          throw new Error('API request failed')
        }
      } catch (apiError) {
        console.log('Using mock product data due to API error:', apiError)

        // Mock product data fallback
        const mockProducts: Product[] = [
          {
            id: '1',
            name: 'Premium WordPress Theme',
            description: 'A beautiful and responsive WordPress theme',
            price: 49.99,
            currency: 'USD',
            category: 'Templates',
            isActive: true,
            isAssigned: botId === 'bot_001',
            assignedAt: botId === 'bot_001' ? new Date(Date.now() - 86400000).toISOString() : undefined
          },
          {
            id: '2',
            name: 'JavaScript Course',
            description: 'Complete JavaScript mastery course',
            price: 199.99,
            currency: 'USD',
            category: 'Education',
            isActive: true,
            isAssigned: botId === 'bot_001',
            assignedAt: botId === 'bot_001' ? new Date(Date.now() - 259200000).toISOString() : undefined
          },
          {
            id: '3',
            name: 'React Component Library',
            description: 'Professional React components for modern web apps',
            price: 79.99,
            currency: 'USD',
            category: 'Software',
            isActive: true,
            isAssigned: false
          },
          {
            id: '4',
            name: 'Crypto Trading Bot',
            description: 'Automated cryptocurrency trading bot',
            price: 299.99,
            currency: 'USD',
            category: 'Software',
            isActive: true,
            isAssigned: botId === 'bot_002',
            assignedAt: botId === 'bot_002' ? new Date(Date.now() - 172800000).toISOString() : undefined
          },
          {
            id: '5',
            name: 'Game Assets Pack',
            description: '2D game sprites and animations',
            price: 39.99,
            currency: 'USD',
            category: 'Graphics',
            isActive: true,
            isAssigned: false
          }
        ]

        setAvailableProducts(mockProducts)
        setAssignedProducts(mockProducts.filter(p => p.isAssigned))
      }
    } catch (error) {
      console.error('Error fetching products:', error)
    }
  }

  const toggleProductSelection = (productId: string) => {
    setSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    )
  }

  const handleBulkProductSelection = (selectAll: boolean) => {
    if (selectAll) {
      setSelectedProducts(filteredAvailableProducts.map(product => product.id))
    } else {
      setSelectedProducts([])
    }
  }

  const assignProductsToBot = async () => {
    setIsAssigningProducts(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Update assigned products
      const newAssignedProducts = selectedProducts.map(productId => {
        const product = availableProducts.find(p => p.id === productId)
        return product ? {
          ...product,
          isAssigned: true,
          assignedAt: new Date().toISOString()
        } : null
      }).filter(Boolean) as Product[]

      setAssignedProducts(prev => {
        const existingIds = prev.map(p => p.id)
        const uniqueNew = newAssignedProducts.filter(p => !existingIds.includes(p.id))
        return [...prev, ...uniqueNew]
      })

      // Update available products to reflect assignments
      setAvailableProducts(prev =>
        prev.map(product =>
          selectedProducts.includes(product.id)
            ? { ...product, isAssigned: true, assignedAt: new Date().toISOString() }
            : product
        )
      )

      toast.success(`Successfully assigned ${selectedProducts.length} products to this bot`)
      setSelectedProducts([])
    } catch (error) {
      console.error('Error assigning products:', error)
      toast.error('Failed to assign products to bot')
    } finally {
      setIsAssigningProducts(false)
    }
  }

  const removeProductFromBot = async (productId: string) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))

      // Remove from assigned products
      setAssignedProducts(prev => prev.filter(p => p.id !== productId))

      // Update available products
      setAvailableProducts(prev =>
        prev.map(product =>
          product.id === productId
            ? { ...product, isAssigned: false, assignedAt: undefined }
            : product
        )
      )

      const product = availableProducts.find(p => p.id === productId)
      if (product) {
        toast.success(`Removed "${product.name}" from this bot`)
      }
    } catch (error) {
      console.error('Error removing product assignment:', error)
      toast.error('Failed to remove product assignment')
    }
  }

  const filteredAvailableProducts = availableProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(productSearchTerm.toLowerCase()) ||
                         product.description.toLowerCase().includes(productSearchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'All Categories' || product.category === selectedCategory
    const notAssigned = !product.isAssigned
    return matchesSearch && matchesCategory && notAssigned
  })

  const categories = ['All Categories', ...Array.from(new Set(availableProducts.map(p => p.category)))]



  const tabs = [
    { id: 'general', name: 'General', icon: Settings },
    { id: 'messages', name: 'Messages', icon: MessageSquare },
    { id: 'localization', name: 'Localization', icon: Globe },
    { id: 'payments', name: 'Payments', icon: CreditCard },
    { id: 'features', name: 'Features', icon: Package },
    { id: 'products', name: 'Products', icon: Package },
    { id: 'theme', name: 'Theme', icon: Palette },
    { id: 'security', name: 'Security', icon: Shield }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-3">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <span className="text-lg font-medium text-gray-600">Loading bot configuration...</span>
        </div>
      </div>
    )
  }

  if (!botData) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-16 w-16 mx-auto text-red-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Bot Not Found</h3>
        <p className="text-gray-600 mb-6">Unable to load bot configuration.</p>
        <button
          onClick={onBack}
          className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg flex items-center gap-2 mx-auto"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Bots
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div className="flex items-center gap-4">
          <button
            onClick={onBack}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Edit Bot Configuration
            </h1>
            <p className="text-gray-600 mt-1 flex items-center gap-2">
              <Bot className="h-4 w-4" />
              {botData.name} (@{botData.username})
              {hasChanges && (
                <span className="px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">
                  Unsaved Changes
                </span>
              )}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3 mt-4 sm:mt-0">
          <button
            onClick={() => setShowVersionHistory(true)}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium flex items-center gap-2"
          >
            <History className="w-4 h-4" />
            Version History
          </button>
          <button
            onClick={handleReset}
            disabled={!hasChanges}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            <RotateCcw className="w-4 h-4" />
            Reset
          </button>
          <button
            onClick={() => setPreviewMode(!previewMode)}
            className="px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-lg hover:from-purple-600 hover:to-pink-700 transition-all duration-200 font-medium flex items-center gap-2"
          >
            <Eye className="w-4 h-4" />
            {previewMode ? 'Exit Preview' : 'Preview'}
          </button>
          <button
            onClick={handleSave}
            disabled={saving || !hasChanges}
            className="px-6 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg hover:from-green-600 hover:to-emerald-700 transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {saving ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                Save Changes
              </>
            )}
          </button>
        </div>
      </div>

      {/* Preview Mode */}
      {previewMode && (
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-xl border border-purple-200">
          <h3 className="font-bold text-purple-900 mb-4 flex items-center gap-2">
            <Eye className="w-5 h-5" />
            Bot Preview
          </h3>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center gap-3 mb-4">
              <div
                className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold"
                style={{ backgroundColor: botData.settings.theme.primaryColor }}
              >
                {botData.name.charAt(0)}
              </div>
              <div>
                <div className="font-semibold text-gray-900">{botData.name}</div>
                <div className="text-sm text-gray-500">@{botData.username}</div>
              </div>
            </div>
            <div
              className="px-4 py-3 rounded-lg text-white text-sm mb-4"
              style={{ backgroundColor: botData.settings.theme.primaryColor }}
            >
              {botData.settings.welcomeMessage}
            </div>
            <div className="flex gap-2">
              <button
                className="px-4 py-2 rounded-lg text-white text-sm"
                style={{ backgroundColor: botData.settings.theme.accentColor }}
              >
                🛍️ Browse Products
              </button>
              <button
                className="px-4 py-2 rounded-lg border text-sm"
                style={{ borderColor: botData.settings.theme.primaryColor, color: botData.settings.theme.primaryColor }}
              >
                👤 My Profile
              </button>
              <button
                className="px-4 py-2 rounded-lg border text-sm"
                style={{ borderColor: botData.settings.theme.primaryColor, color: botData.settings.theme.primaryColor }}
              >
                ℹ️ Store Info
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* General Tab */}
          {activeTab === 'general' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                    <Bot className="h-4 w-4" />
                    Bot Name
                    <TooltipIcon
                      content="The display name for your bot that users will see in Telegram. Choose a descriptive name that represents your store."
                      variant="info"
                    />
                  </label>
                  <input
                    type="text"
                    value={botData.name}
                    onChange={(e) => setBotData({...botData, name: e.target.value})}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="My Digital Store Bot"
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                    <Smartphone className="h-4 w-4" />
                    Bot Username
                    <TooltipIcon
                      content="The unique username for your bot (without @). This is how users will find and start conversations with your bot."
                      variant="info"
                    />
                  </label>
                  <input
                    type="text"
                    value={botData.username}
                    onChange={(e) => setBotData({...botData, username: e.target.value})}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="mystore_bot"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  Bot Chat ID
                  <TooltipIcon
                    content={
                      <div className="space-y-2">
                        <p><strong>What is a Chat ID?</strong></p>
                        <p>A unique identifier for bot conversations in Telegram.</p>
                        <p><strong>How to get it:</strong></p>
                        <ul className="list-disc list-inside space-y-1">
                          <li>Message @userinfobot on Telegram</li>
                          <li>Use Telegram Bot API methods</li>
                          <li>Check bot logs for incoming messages</li>
                        </ul>
                        <p><strong>Format:</strong></p>
                        <ul className="list-disc list-inside space-y-1">
                          <li>Positive numbers: Individual users</li>
                          <li>Negative numbers: Groups/Channels</li>
                        </ul>
                      </div>
                    }
                    variant="info"
                  />
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={botData.chatId || ''}
                    onChange={(e) => {
                      const value = e.target.value
                      if (value === '' || /^-?\d+$/.test(value)) {
                        setBotData({...botData, chatId: value})
                      }
                    }}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 pr-12 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="123456789 or -1001234567890"
                  />
                  {botData.chatId && (
                    <button
                      type="button"
                      onClick={() => copyToClipboard(botData.chatId!)}
                      className="absolute inset-y-0 right-0 flex items-center pr-3"
                    >
                      <Copy className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                    </button>
                  )}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Unique identifier for bot conversations. Positive for users, negative for groups/channels.
                </p>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  Description
                  <TooltipIcon
                    content="A brief description of your bot store. This helps users understand what your bot offers and can be used in bot directories."
                    variant="info"
                  />
                </label>
                <textarea
                  value={botData.description || ''}
                  onChange={(e) => setBotData({...botData, description: e.target.value})}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  rows={3}
                  placeholder="Brief description of your bot store..."
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Bot Token
                  <TooltipIcon
                    content={
                      <div className="space-y-2">
                        <p><strong>Bot Token Security:</strong></p>
                        <p>Your bot token is like a password - keep it secure!</p>
                        <p><strong>How to get a token:</strong></p>
                        <ol className="list-decimal list-inside space-y-1">
                          <li>Message @BotFather on Telegram</li>
                          <li>Send /newbot command</li>
                          <li>Follow the setup process</li>
                          <li>Copy the token provided</li>
                        </ol>
                        <p><strong>Security Tips:</strong></p>
                        <ul className="list-disc list-inside space-y-1">
                          <li>Never share your token publicly</li>
                          <li>Regenerate if compromised</li>
                          <li>Use HTTPS for webhooks</li>
                        </ul>
                      </div>
                    }
                    variant="warning"
                  />
                </label>
                <div className="relative">
                  <input
                    type={showToken ? 'text' : 'password'}
                    value={botData.token}
                    onChange={(e) => setBotData({...botData, token: e.target.value})}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 pr-20 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="1234567890:AAEhBOweik6ad6PsVQF6XJhiknbhd6k-Ks"
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center gap-2 pr-3">
                    <Tooltip content={showToken ? "Hide token" : "Show token"}>
                      <button
                        type="button"
                        onClick={() => setShowToken(!showToken)}
                        className="p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                      >
                        {showToken ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </Tooltip>
                    <Tooltip content="Copy token to clipboard">
                      <button
                        type="button"
                        onClick={() => copyToClipboard(botData.token)}
                        className="p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                    </Tooltip>
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Get this from @BotFather on Telegram. Keep it secure and never share publicly.
                </p>
              </div>
            </div>
          )}

          {/* Messages Tab */}
          {activeTab === 'messages' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  Welcome Message
                  <TooltipIcon
                    content="The first message users see when they start your bot. Make it engaging and informative to encourage interaction."
                    variant="info"
                  />
                </label>
                <textarea
                  value={botData.settings.welcomeMessage}
                  onChange={(e) => setBotData({
                    ...botData,
                    settings: { ...botData.settings, welcomeMessage: e.target.value }
                  })}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  rows={4}
                  placeholder="Welcome message shown to new users..."
                />
                <p className="text-xs text-gray-500 mt-1">This message will be sent when users first start your bot</p>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                  <Info className="h-4 w-4" />
                  Store Information
                  <TooltipIcon
                    content="Detailed information about your store including policies, return procedures, shipping info, and any other important details customers should know."
                    variant="info"
                  />
                </label>
                <textarea
                  value={botData.settings.storeInfo || ''}
                  onChange={(e) => setBotData({
                    ...botData,
                    settings: { ...botData.settings, storeInfo: e.target.value }
                  })}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  rows={3}
                  placeholder="Information about your store, policies, etc..."
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Support Contact
                  <TooltipIcon
                    content="How customers can reach you for support. Can be a Telegram username (@username) or email address. This will be shown to customers who need help."
                    variant="info"
                  />
                </label>
                <input
                  type="text"
                  value={botData.settings.supportContact || ''}
                  onChange={(e) => setBotData({
                    ...botData,
                    settings: { ...botData.settings, supportContact: e.target.value }
                  })}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  placeholder="@support_<NAME_EMAIL>"
                />
                <p className="text-xs text-gray-500 mt-1">Telegram username or email for customer support</p>
              </div>
            </div>
          )}

          {/* Localization Tab */}
          {activeTab === 'localization' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                    <Languages className="h-4 w-4" />
                    Language
                    <TooltipIcon
                      content="The primary language for your bot interface. This affects button labels, system messages, and default text formatting."
                      variant="info"
                    />
                  </label>
                  <select
                    value={botData.settings.language}
                    onChange={(e) => setBotData({
                      ...botData,
                      settings: { ...botData.settings, language: e.target.value }
                    })}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  >
                    <option value="en">🇺🇸 English</option>
                    <option value="es">🇪🇸 Spanish</option>
                    <option value="fr">🇫🇷 French</option>
                    <option value="de">🇩🇪 German</option>
                    <option value="it">🇮🇹 Italian</option>
                    <option value="pt">🇵🇹 Portuguese</option>
                    <option value="ru">🇷🇺 Russian</option>
                    <option value="zh">🇨🇳 Chinese</option>
                    <option value="ja">🇯🇵 Japanese</option>
                    <option value="ko">🇰🇷 Korean</option>
                    <option value="ar">🇸🇦 Arabic</option>
                    <option value="hi">🇮🇳 Hindi</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Currency
                    <TooltipIcon
                      content="The primary currency for displaying prices. This affects how amounts are formatted and which payment methods are prioritized."
                      variant="info"
                    />
                  </label>
                  <select
                    value={botData.settings.currency}
                    onChange={(e) => setBotData({
                      ...botData,
                      settings: { ...botData.settings, currency: e.target.value }
                    })}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  >
                    <optgroup label="Fiat Currencies">
                      <option value="USD">💵 USD - US Dollar</option>
                      <option value="EUR">💶 EUR - Euro</option>
                      <option value="GBP">💷 GBP - British Pound</option>
                      <option value="JPY">💴 JPY - Japanese Yen</option>
                      <option value="CAD">🍁 CAD - Canadian Dollar</option>
                      <option value="AUD">🇦🇺 AUD - Australian Dollar</option>
                    </optgroup>
                    <optgroup label="Cryptocurrencies">
                      <option value="BTC">₿ BTC - Bitcoin</option>
                      <option value="ETH">Ξ ETH - Ethereum</option>
                      <option value="USDT">₮ USDT - Tether</option>
                      <option value="BNB">🔶 BNB - Binance Coin</option>
                    </optgroup>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Timezone
                    <TooltipIcon
                      content="The timezone for bot operations, order timestamps, and scheduled messages. Choose the timezone where most of your customers are located."
                      variant="info"
                    />
                  </label>
                  <select
                    value={botData.settings.timezone}
                    onChange={(e) => setBotData({
                      ...botData,
                      settings: { ...botData.settings, timezone: e.target.value }
                    })}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  >
                    <option value="UTC">🌍 UTC - Coordinated Universal Time</option>
                    <option value="America/New_York">🇺🇸 Eastern Time (ET)</option>
                    <option value="America/Chicago">🇺🇸 Central Time (CT)</option>
                    <option value="America/Denver">🇺🇸 Mountain Time (MT)</option>
                    <option value="America/Los_Angeles">🇺🇸 Pacific Time (PT)</option>
                    <option value="Europe/London">🇬🇧 London (GMT/BST)</option>
                    <option value="Europe/Paris">🇫🇷 Paris (CET/CEST)</option>
                    <option value="Europe/Berlin">🇩🇪 Berlin (CET/CEST)</option>
                    <option value="Asia/Tokyo">🇯🇵 Tokyo (JST)</option>
                    <option value="Asia/Shanghai">🇨🇳 Shanghai (CST)</option>
                    <option value="Asia/Dubai">🇦🇪 Dubai (GST)</option>
                    <option value="Australia/Sydney">🇦🇺 Sydney (AEST/AEDT)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Date Format
                    <TooltipIcon
                      content="How dates are displayed to customers in order confirmations, receipts, and other messages."
                      variant="info"
                    />
                  </label>
                  <select
                    value={botData.settings.dateFormat}
                    onChange={(e) => setBotData({
                      ...botData,
                      settings: { ...botData.settings, dateFormat: e.target.value }
                    })}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  >
                    <option value="MM/DD/YYYY">MM/DD/YYYY (US Format)</option>
                    <option value="DD/MM/YYYY">DD/MM/YYYY (European Format)</option>
                    <option value="YYYY-MM-DD">YYYY-MM-DD (ISO Format)</option>
                    <option value="DD.MM.YYYY">DD.MM.YYYY (German Format)</option>
                    <option value="DD-MM-YYYY">DD-MM-YYYY (Alternative)</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  Number Format
                  <TooltipIcon
                    content="How numbers, prices, and quantities are formatted. This affects decimal separators, thousand separators, and currency symbols."
                    variant="info"
                  />
                </label>
                <select
                  value={botData.settings.numberFormat}
                  onChange={(e) => setBotData({
                    ...botData,
                    settings: { ...botData.settings, numberFormat: e.target.value }
                  })}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                >
                  <option value="en-US">English (US) - 1,234.56</option>
                  <option value="en-GB">English (UK) - 1,234.56</option>
                  <option value="de-DE">German - 1.234,56</option>
                  <option value="fr-FR">French - 1 234,56</option>
                  <option value="es-ES">Spanish - 1.234,56</option>
                  <option value="it-IT">Italian - 1.234,56</option>
                  <option value="pt-BR">Portuguese (Brazil) - 1.234,56</option>
                  <option value="ru-RU">Russian - 1 234,56</option>
                  <option value="zh-CN">Chinese - 1,234.56</option>
                  <option value="ja-JP">Japanese - 1,234.56</option>
                </select>
              </div>

              <div className="bg-blue-50 p-4 rounded-xl">
                <h4 className="font-semibold text-blue-900 mb-2 flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  Localization Preview
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-blue-700">Date Example:</span>
                    <span className="ml-2 font-mono bg-white px-2 py-1 rounded">
                      {new Date().toLocaleDateString(botData.settings.numberFormat, {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit'
                      })}
                    </span>
                  </div>
                  <div>
                    <span className="text-blue-700">Number Example:</span>
                    <span className="ml-2 font-mono bg-white px-2 py-1 rounded">
                      {(1234.56).toLocaleString(botData.settings.numberFormat, {
                        style: 'currency',
                        currency: botData.settings.currency
                      })}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Payments Tab */}
          {activeTab === 'payments' && (
            <div className="space-y-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  Payment Methods
                  <TooltipIcon
                    content="Select which cryptocurrency payment methods to accept in your store. Each method can be individually configured with limits and fees."
                    variant="info"
                  />
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[
                    {
                      id: 'BITCOIN',
                      name: 'Bitcoin',
                      symbol: '₿',
                      color: 'orange',
                      description: 'The original cryptocurrency with highest security',
                      fees: 'Network fees: Variable',
                      confirmations: '1-6 confirmations'
                    },
                    {
                      id: 'ETHEREUM',
                      name: 'Ethereum',
                      symbol: 'Ξ',
                      color: 'blue',
                      description: 'Smart contract platform with fast transactions',
                      fees: 'Gas fees: Variable',
                      confirmations: '12-35 confirmations'
                    },
                    {
                      id: 'USDT',
                      name: 'Tether USDT',
                      symbol: '₮',
                      color: 'green',
                      description: 'Stable coin pegged to USD value',
                      fees: 'Network fees: Low',
                      confirmations: '1-12 confirmations'
                    },
                    {
                      id: 'LITECOIN',
                      name: 'Litecoin',
                      symbol: 'Ł',
                      color: 'gray',
                      description: 'Fast and low-cost Bitcoin alternative',
                      fees: 'Network fees: Very low',
                      confirmations: '2-6 confirmations'
                    },
                    {
                      id: 'DOGECOIN',
                      name: 'Dogecoin',
                      symbol: 'Ð',
                      color: 'yellow',
                      description: 'Popular meme coin with low fees',
                      fees: 'Network fees: Minimal',
                      confirmations: '1-6 confirmations'
                    },
                    {
                      id: 'MONERO',
                      name: 'Monero',
                      symbol: 'ɱ',
                      color: 'orange',
                      description: 'Privacy-focused cryptocurrency',
                      fees: 'Network fees: Low',
                      confirmations: '10-20 confirmations'
                    }
                  ].map((method) => (
                    <div
                      key={method.id}
                      onClick={() => togglePaymentMethod(method.id)}
                      className={`p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                        botData.settings.paymentMethods.includes(method.id)
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <div className={`w-10 h-10 rounded-full bg-${method.color}-500 flex items-center justify-center text-white font-bold text-lg`}>
                            {method.symbol}
                          </div>
                          <div>
                            <div className="font-semibold text-gray-900">{method.name}</div>
                            <div className="text-sm text-gray-500">{method.id}</div>
                          </div>
                        </div>
                        {botData.settings.paymentMethods.includes(method.id) && (
                          <CheckCircle className="w-5 h-5 text-blue-500" />
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{method.description}</p>
                      <div className="text-xs text-gray-500 space-y-1">
                        <div>{method.fees}</div>
                        <div>{method.confirmations}</div>
                      </div>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-2">Select at least one payment method for your store</p>
              </div>

              {/* Payment Limits Configuration */}
              {botData.settings.paymentMethods.length > 0 && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <Percent className="h-4 w-4" />
                    Payment Limits & Fees
                    <TooltipIcon
                      content="Configure minimum and maximum transaction amounts, plus processing fees for each payment method."
                      variant="info"
                    />
                  </h4>
                  <div className="space-y-4">
                    {botData.settings.paymentMethods.map((methodId) => (
                      <div key={methodId} className="bg-gray-50 p-4 rounded-xl">
                        <h5 className="font-medium text-gray-900 mb-3">{methodId} Configuration</h5>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Minimum Amount
                            </label>
                            <input
                              type="number"
                              step="0.00001"
                              value={botData.settings.paymentLimits[methodId]?.min || 0}
                              onChange={(e) => setBotData({
                                ...botData,
                                settings: {
                                  ...botData.settings,
                                  paymentLimits: {
                                    ...botData.settings.paymentLimits,
                                    [methodId]: {
                                      ...botData.settings.paymentLimits[methodId],
                                      min: parseFloat(e.target.value) || 0
                                    }
                                  }
                                }
                              })}
                              className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Maximum Amount
                            </label>
                            <input
                              type="number"
                              step="0.00001"
                              value={botData.settings.paymentLimits[methodId]?.max || 0}
                              onChange={(e) => setBotData({
                                ...botData,
                                settings: {
                                  ...botData.settings,
                                  paymentLimits: {
                                    ...botData.settings.paymentLimits,
                                    [methodId]: {
                                      ...botData.settings.paymentLimits[methodId],
                                      max: parseFloat(e.target.value) || 0
                                    }
                                  }
                                }
                              })}
                              className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Processing Fee
                            </label>
                            <input
                              type="number"
                              step="0.00001"
                              value={botData.settings.paymentLimits[methodId]?.fee || 0}
                              onChange={(e) => setBotData({
                                ...botData,
                                settings: {
                                  ...botData.settings,
                                  paymentLimits: {
                                    ...botData.settings.paymentLimits,
                                    [methodId]: {
                                      ...botData.settings.paymentLimits[methodId],
                                      fee: parseFloat(e.target.value) || 0
                                    }
                                  }
                                }
                              })}
                              className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Payment Confirmation Settings */}
              <div>
                <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Timer className="h-4 w-4" />
                  Confirmation Settings
                  <TooltipIcon
                    content="Configure how long customers have to complete payments and other payment-related timeouts."
                    variant="info"
                  />
                </h4>
                <div className="bg-gray-50 p-4 rounded-xl">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Payment Timeout (minutes)
                      </label>
                      <input
                        type="number"
                        min="5"
                        max="120"
                        value={botData.settings.confirmationTimeout}
                        onChange={(e) => setBotData({
                          ...botData,
                          settings: { ...botData.settings, confirmationTimeout: parseInt(e.target.value) || 30 }
                        })}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        How long customers have to complete payment (5-120 minutes)
                      </p>
                    </div>
                    <div className="flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{botData.settings.confirmationTimeout}</div>
                        <div className="text-sm text-gray-600">Minutes</div>
                        <div className="text-xs text-gray-500 mt-1">
                          {botData.settings.confirmationTimeout < 15 ? 'Fast checkout' :
                           botData.settings.confirmationTimeout < 45 ? 'Standard timing' :
                           'Extended window'}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 p-4 rounded-xl">
                <h4 className="font-semibold text-green-900 mb-2 flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Payment Security
                </h4>
                <ul className="text-sm text-green-800 space-y-1">
                  <li>• All payments are processed securely through blockchain networks</li>
                  <li>• Automatic payment verification and confirmation</li>
                  <li>• Real-time payment status updates for customers</li>
                  <li>• Built-in fraud protection and monitoring</li>
                  <li>• Secure wallet integration with multi-signature support</li>
                </ul>
              </div>
            </div>
          )}

          {/* Features Tab */}
          {activeTab === 'features' && (
            <div className="space-y-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  Bot Features
                  <TooltipIcon
                    content="Enable or disable specific features for your bot. Some features may depend on others to function properly."
                    variant="info"
                  />
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    {
                      key: 'categories',
                      name: 'Product Categories',
                      description: 'Organize products into categories for easier browsing',
                      icon: Package,
                      dependencies: [],
                      benefits: ['Better organization', 'Improved navigation', 'Easier product discovery']
                    },
                    {
                      key: 'userProfiles',
                      name: 'User Profiles',
                      description: 'Allow customers to create and manage their profiles',
                      icon: Users,
                      dependencies: [],
                      benefits: ['Personalized experience', 'Order history tracking', 'Faster checkout']
                    },
                    {
                      key: 'balanceTopUp',
                      name: 'Balance Top-up',
                      description: 'Let customers add funds to their account balance',
                      icon: DollarSign,
                      dependencies: ['userProfiles'],
                      benefits: ['Faster payments', 'Customer retention', 'Bulk purchases']
                    },
                    {
                      key: 'orderHistory',
                      name: 'Order History',
                      description: 'Customers can view their past orders and downloads',
                      icon: Clock,
                      dependencies: ['userProfiles'],
                      benefits: ['Customer satisfaction', 'Re-download capability', 'Order tracking']
                    },
                    {
                      key: 'customerSupport',
                      name: 'Customer Support',
                      description: 'Built-in support ticket system and live chat',
                      icon: Users,
                      dependencies: [],
                      benefits: ['Better customer service', 'Issue resolution', 'Customer satisfaction']
                    },
                    {
                      key: 'analytics',
                      name: 'Analytics & Reporting',
                      description: 'Detailed analytics and performance reports',
                      icon: BarChart3,
                      dependencies: [],
                      benefits: ['Business insights', 'Performance tracking', 'Data-driven decisions']
                    }
                  ].map((feature) => (
                    <div
                      key={feature.key}
                      className={`p-4 border-2 rounded-xl transition-all duration-200 ${
                        botData.settings.features[feature.key as keyof typeof botData.settings.features]
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg ${
                            botData.settings.features[feature.key as keyof typeof botData.settings.features]
                              ? 'bg-blue-500 text-white'
                              : 'bg-gray-200 text-gray-600'
                          }`}>
                            <feature.icon className="w-4 h-4" />
                          </div>
                          <div>
                            <div className="font-semibold text-gray-900">{feature.name}</div>
                            <div className="text-sm text-gray-600">{feature.description}</div>
                          </div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={botData.settings.features[feature.key as keyof typeof botData.settings.features]}
                            onChange={() => toggleFeature(feature.key)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>

                      {/* Dependencies Warning */}
                      {feature.dependencies.length > 0 && (
                        <div className="mb-3">
                          <div className="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded">
                            <AlertTriangle className="w-3 h-3 inline mr-1" />
                            Requires: {feature.dependencies.map(dep =>
                              dep.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
                            ).join(', ')}
                          </div>
                        </div>
                      )}

                      {/* Benefits */}
                      <div className="text-xs text-gray-500">
                        <div className="font-medium mb-1">Benefits:</div>
                        <ul className="list-disc list-inside space-y-0.5">
                          {feature.benefits.map((benefit, index) => (
                            <li key={index}>{benefit}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Feature Dependencies Info */}
              <div className="bg-yellow-50 p-4 rounded-xl">
                <h4 className="font-semibold text-yellow-900 mb-2 flex items-center gap-2">
                  <Info className="h-4 w-4" />
                  Feature Dependencies
                </h4>
                <div className="text-sm text-yellow-800 space-y-1">
                  <p>• <strong>Balance Top-up</strong> requires User Profiles to track customer balances</p>
                  <p>• <strong>Order History</strong> requires User Profiles to associate orders with customers</p>
                  <p>• Disabling a required feature will automatically disable dependent features</p>
                  <p>• All features can be enabled/disabled at any time without data loss</p>
                </div>
              </div>

              {/* Feature Summary */}
              <div className="bg-blue-50 p-4 rounded-xl">
                <h4 className="font-semibold text-blue-900 mb-2 flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Active Features Summary
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
                  {Object.entries(botData.settings.features).map(([key, enabled]) => (
                    <div key={key} className={`flex items-center gap-2 ${enabled ? 'text-blue-800' : 'text-gray-500'}`}>
                      {enabled ? (
                        <CheckCircle className="w-3 h-3 text-green-500" />
                      ) : (
                        <XCircle className="w-3 h-3 text-gray-400" />
                      )}
                      <span>{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Products Tab */}
          {activeTab === 'products' && (
            <div className="space-y-6">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Product Assignment
                    <TooltipIcon
                      content="Assign products to this bot so customers can purchase them through the Telegram interface. Only assigned products will be available for sale."
                      variant="info"
                    />
                  </h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Manage which products are available for sale through this bot
                  </p>
                </div>
                <div className="text-sm text-gray-500">
                  {assignedProducts.length} products assigned
                </div>
              </div>

              {/* Currently Assigned Products */}
              <div>
                <h5 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  Currently Assigned Products ({assignedProducts.length})
                </h5>

                {assignedProducts.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {assignedProducts.map((product) => (
                      <div key={product.id} className="bg-green-50 border border-green-200 p-4 rounded-xl">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="font-semibold text-gray-900">{product.name}</div>
                            <div className="text-sm text-gray-600 mt-1">{product.description}</div>
                            <div className="flex items-center gap-4 mt-2 text-sm">
                              <span className="font-medium text-green-700">
                                ${product.price} {product.currency}
                              </span>
                              <span className="text-gray-500">{product.category}</span>
                              {product.assignedAt && (
                                <span className="text-xs text-gray-500">
                                  Assigned {new Date(product.assignedAt).toLocaleDateString()}
                                </span>
                              )}
                            </div>
                          </div>
                          <Tooltip content="Remove product from this bot">
                            <button
                              onClick={() => removeProductFromBot(product.id)}
                              className="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-200"
                            >
                              <XCircle className="w-4 h-4" />
                            </button>
                          </Tooltip>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 bg-gray-50 rounded-xl">
                    <Package className="w-12 h-12 mx-auto mb-3 text-gray-400" />
                    <p className="text-gray-600 font-medium">No products assigned yet</p>
                    <p className="text-sm text-gray-500">Assign products below to start selling through this bot</p>
                  </div>
                )}
              </div>

              {/* Available Products for Assignment */}
              <div>
                <h5 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <Package className="h-4 w-4 text-blue-500" />
                  Available Products ({filteredAvailableProducts.length})
                </h5>

                {/* Search and Filters */}
                <div className="flex flex-col sm:flex-row gap-4 mb-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search products..."
                      value={productSearchTerm}
                      onChange={(e) => setProductSearchTerm(e.target.value)}
                      className="w-full border border-gray-300 rounded-xl px-10 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    />
                  </div>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="border border-gray-300 rounded-xl px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  >
                    {categories.map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Bulk Actions */}
                {filteredAvailableProducts.length > 0 && (
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <button
                        onClick={() => handleBulkProductSelection(true)}
                        className="px-3 py-1 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 text-sm"
                      >
                        Select All
                      </button>
                      <button
                        onClick={() => handleBulkProductSelection(false)}
                        className="px-3 py-1 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200 text-sm"
                      >
                        Deselect All
                      </button>
                    </div>
                    <div className="text-sm text-gray-600">
                      {selectedProducts.length} of {filteredAvailableProducts.length} selected
                    </div>
                  </div>
                )}

                {/* Product List */}
                {filteredAvailableProducts.length > 0 ? (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {filteredAvailableProducts.map((product) => (
                      <div
                        key={product.id}
                        onClick={() => toggleProductSelection(product.id)}
                        className={`p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                          selectedProducts.includes(product.id)
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className={`p-2 rounded-lg ${product.isActive ? 'bg-green-100' : 'bg-gray-100'}`}>
                              <Package className={`w-4 h-4 ${product.isActive ? 'text-green-600' : 'text-gray-600'}`} />
                            </div>
                            <div>
                              <div className="font-semibold text-gray-900">{product.name}</div>
                              <div className="text-sm text-gray-600">{product.description}</div>
                              <div className="flex items-center gap-4 mt-1 text-sm">
                                <span className="font-medium text-blue-700">
                                  ${product.price} {product.currency}
                                </span>
                                <span className="text-gray-500">{product.category}</span>
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                  product.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {product.isActive ? 'Active' : 'Inactive'}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center">
                            {selectedProducts.includes(product.id) && (
                              <CheckCircle className="w-6 h-6 text-blue-500" />
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 bg-gray-50 rounded-xl">
                    <Search className="w-12 h-12 mx-auto mb-3 text-gray-400" />
                    <p className="text-gray-600 font-medium">No available products found</p>
                    <p className="text-sm text-gray-500">
                      {productSearchTerm || selectedCategory !== 'All Categories'
                        ? 'Try adjusting your search or filter criteria'
                        : 'All products are already assigned to this bot'
                      }
                    </p>
                  </div>
                )}

                {/* Assign Button */}
                {selectedProducts.length > 0 && (
                  <div className="mt-4 flex justify-end">
                    <button
                      onClick={assignProductsToBot}
                      disabled={isAssigningProducts}
                      className="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                    >
                      {isAssigningProducts ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Assigning...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="h-4 w-4" />
                          Assign {selectedProducts.length} Product{selectedProducts.length !== 1 ? 's' : ''}
                        </>
                      )}
                    </button>
                  </div>
                )}
              </div>

              {/* Assignment Benefits */}
              <div className="bg-blue-50 p-4 rounded-xl">
                <h5 className="font-semibold text-blue-900 mb-2 flex items-center gap-2">
                  <Info className="w-4 h-4" />
                  Product Assignment Benefits
                </h5>
                <div className="text-sm text-blue-800 space-y-1">
                  <p>• <strong>Targeted Sales:</strong> Each bot can sell specific products to its audience</p>
                  <p>• <strong>Brand Separation:</strong> Different bots can represent different product lines</p>
                  <p>• <strong>Customer Experience:</strong> Customers see only relevant products for each bot</p>
                  <p>• <strong>Analytics:</strong> Track which products perform best on each bot</p>
                  <p>• <strong>Flexibility:</strong> Easily add or remove products without affecting other bots</p>
                </div>
              </div>
            </div>
          )}

          {/* Theme Tab */}
          {activeTab === 'theme' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Color Configuration */}
                <div className="space-y-6">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <Palette className="h-4 w-4" />
                      Color Scheme
                      <TooltipIcon
                        content="Customize your bot's color scheme to match your brand. Colors affect buttons, headers, and accent elements in the bot interface."
                        variant="info"
                      />
                    </h4>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Primary Color
                        </label>
                        <div className="flex items-center gap-3">
                          <input
                            type="color"
                            value={botData.settings.theme.primaryColor}
                            onChange={(e) => updateThemeColor('primaryColor', e.target.value)}
                            className="w-12 h-12 border border-gray-300 rounded-lg cursor-pointer"
                          />
                          <input
                            type="text"
                            value={botData.settings.theme.primaryColor}
                            onChange={(e) => updateThemeColor('primaryColor', e.target.value)}
                            className="flex-1 border border-gray-300 rounded-lg px-3 py-2 font-mono text-sm"
                            placeholder="#3B82F6"
                          />
                        </div>
                        <p className="text-xs text-gray-500 mt-1">Main color for buttons and headers</p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Accent Color
                        </label>
                        <div className="flex items-center gap-3">
                          <input
                            type="color"
                            value={botData.settings.theme.accentColor}
                            onChange={(e) => updateThemeColor('accentColor', e.target.value)}
                            className="w-12 h-12 border border-gray-300 rounded-lg cursor-pointer"
                          />
                          <input
                            type="text"
                            value={botData.settings.theme.accentColor}
                            onChange={(e) => updateThemeColor('accentColor', e.target.value)}
                            className="flex-1 border border-gray-300 rounded-lg px-3 py-2 font-mono text-sm"
                            placeholder="#8B5CF6"
                          />
                        </div>
                        <p className="text-xs text-gray-500 mt-1">Secondary color for highlights and accents</p>
                      </div>
                    </div>
                  </div>

                  {/* Theme Templates */}
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <Sparkles className="h-4 w-4" />
                      Theme Templates
                      <TooltipIcon
                        content="Pre-designed color combinations that work well together. Click to apply a template and customize further if needed."
                        variant="info"
                      />
                    </h4>

                    <div className="grid grid-cols-2 gap-3">
                      {[
                        { name: 'Professional', primary: '#3B82F6', accent: '#1E40AF', description: 'Clean and trustworthy' },
                        { name: 'Vibrant', primary: '#EF4444', accent: '#F97316', description: 'Bold and energetic' },
                        { name: 'Nature', primary: '#10B981', accent: '#059669', description: 'Fresh and organic' },
                        { name: 'Minimal', primary: '#6B7280', accent: '#374151', description: 'Simple and elegant' },
                        { name: 'Purple', primary: '#8B5CF6', accent: '#7C3AED', description: 'Creative and modern' },
                        { name: 'Ocean', primary: '#0EA5E9', accent: '#0284C7', description: 'Calm and reliable' }
                      ].map((template) => (
                        <button
                          key={template.name}
                          onClick={() => {
                            updateThemeColor('primaryColor', template.primary)
                            updateThemeColor('accentColor', template.accent)
                            setBotData({
                              ...botData,
                              settings: {
                                ...botData.settings,
                                theme: {
                                  ...botData.settings.theme,
                                  template: template.name.toLowerCase()
                                }
                              }
                            })
                          }}
                          className={`p-3 border-2 rounded-lg text-left transition-all duration-200 ${
                            botData.settings.theme.template === template.name.toLowerCase()
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <div className="flex items-center gap-2 mb-1">
                            <div
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: template.primary }}
                            />
                            <div
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: template.accent }}
                            />
                            <span className="font-medium text-sm">{template.name}</span>
                          </div>
                          <p className="text-xs text-gray-500">{template.description}</p>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Logo Upload */}
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <Upload className="h-4 w-4" />
                      Brand Logo
                      <TooltipIcon
                        content="Upload your brand logo to display in the bot interface. Recommended size: 200x200px, PNG or JPG format."
                        variant="info"
                      />
                    </h4>

                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-600 mb-2">Click to upload logo or drag and drop</p>
                      <p className="text-xs text-gray-500">PNG, JPG up to 2MB</p>
                      <input
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={(e) => {
                          // Handle file upload
                          const file = e.target.files?.[0]
                          if (file) {
                            toast.success('Logo uploaded successfully!')
                          }
                        }}
                      />
                    </div>

                    <div className="mt-3">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Logo Position
                      </label>
                      <select
                        value={botData.settings.theme.logoPosition}
                        onChange={(e) => setBotData({
                          ...botData,
                          settings: {
                            ...botData.settings,
                            theme: {
                              ...botData.settings.theme,
                              logoPosition: e.target.value
                            }
                          }
                        })}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2"
                      >
                        <option value="center">Center</option>
                        <option value="left">Left</option>
                        <option value="right">Right</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Live Preview */}
                <div>
                  <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    Live Preview
                    <TooltipIcon
                      content="See how your bot will look with the current theme settings. Changes are applied in real-time."
                      variant="info"
                    />
                  </h4>

                  <div className="bg-gray-100 p-4 rounded-xl">
                    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                      {/* Bot Header */}
                      <div
                        className="p-4 text-white"
                        style={{
                          background: `linear-gradient(135deg, ${botData.settings.theme.primaryColor}, ${botData.settings.theme.accentColor})`
                        }}
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                            <Bot className="w-5 h-5" />
                          </div>
                          <div>
                            <div className="font-semibold">{botData.name}</div>
                            <div className="text-sm opacity-90">@{botData.username}</div>
                          </div>
                        </div>
                      </div>

                      {/* Bot Content */}
                      <div className="p-4 space-y-3">
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <p className="text-sm text-gray-700">{botData.settings.welcomeMessage}</p>
                        </div>

                        <div className="grid grid-cols-2 gap-2">
                          <button
                            className="p-2 rounded-lg text-white text-sm font-medium"
                            style={{ backgroundColor: botData.settings.theme.primaryColor }}
                          >
                            🛍️ Browse Products
                          </button>
                          <button
                            className="p-2 rounded-lg text-white text-sm font-medium"
                            style={{ backgroundColor: botData.settings.theme.accentColor }}
                          >
                            👤 My Profile
                          </button>
                        </div>

                        <div className="text-xs text-gray-500 text-center">
                          Powered by TeleShop
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 bg-blue-50 p-3 rounded-lg">
                    <h5 className="font-medium text-blue-900 mb-2">Color Theory Tips</h5>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• Use contrasting colors for better readability</li>
                      <li>• Stick to 2-3 main colors for consistency</li>
                      <li>• Consider your target audience and brand personality</li>
                      <li>• Test colors on different devices and lighting</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Custom CSS */}
              <div>
                <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Advanced Customization
                  <TooltipIcon
                    content="Add custom CSS for advanced styling. Only use this if you're familiar with CSS. Invalid CSS may break the bot interface."
                    variant="warning"
                  />
                </h4>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Custom CSS (Advanced)
                  </label>
                  <textarea
                    value={botData.settings.theme.customCSS || ''}
                    onChange={(e) => setBotData({
                      ...botData,
                      settings: {
                        ...botData.settings,
                        theme: {
                          ...botData.settings.theme,
                          customCSS: e.target.value
                        }
                      }
                    })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 font-mono text-sm"
                    rows={6}
                    placeholder="/* Add your custom CSS here */
.bot-button {
  border-radius: 8px;
  transition: all 0.2s;
}

.bot-button:hover {
  transform: translateY(-1px);
}"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Advanced users only. Custom CSS will be applied to the bot interface.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Security Tab */}
          {activeTab === 'security' && (
            <div className="space-y-6">
              {/* Rate Limiting */}
              <div>
                <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Activity className="h-4 w-4" />
                  Rate Limiting
                  <TooltipIcon
                    content="Control how many requests users can make to prevent spam and abuse. Set limits per minute and per hour."
                    variant="info"
                  />
                </h4>

                <div className="bg-gray-50 p-4 rounded-xl space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900">Enable Rate Limiting</div>
                      <div className="text-sm text-gray-600">Protect your bot from spam and abuse</div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={botData.settings.security.rateLimiting}
                        onChange={(e) => setBotData({
                          ...botData,
                          settings: {
                            ...botData.settings,
                            security: {
                              ...botData.settings.security,
                              rateLimiting: e.target.checked
                            }
                          }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  {botData.settings.security.rateLimiting && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Requests per Minute
                        </label>
                        <input
                          type="number"
                          min="1"
                          max="1000"
                          value={botData.settings.security.rateLimitRpm}
                          onChange={(e) => setBotData({
                            ...botData,
                            settings: {
                              ...botData.settings,
                              security: {
                                ...botData.settings.security,
                                rateLimitRpm: parseInt(e.target.value) || 60
                              }
                            }
                          })}
                          className="w-full border border-gray-300 rounded-lg px-3 py-2"
                        />
                        <p className="text-xs text-gray-500 mt-1">Recommended: 60-120 for normal use</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Requests per Hour
                        </label>
                        <input
                          type="number"
                          min="1"
                          max="10000"
                          value={botData.settings.security.rateLimitRph}
                          onChange={(e) => setBotData({
                            ...botData,
                            settings: {
                              ...botData.settings,
                              security: {
                                ...botData.settings.security,
                                rateLimitRph: parseInt(e.target.value) || 1000
                              }
                            }
                          })}
                          className="w-full border border-gray-300 rounded-lg px-3 py-2"
                        />
                        <p className="text-xs text-gray-500 mt-1">Recommended: 1000-3000 for normal use</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Spam Protection */}
              <div>
                <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Spam Protection
                  <TooltipIcon
                    content="Automatically detect and block spam messages, repeated content, and suspicious behavior patterns."
                    variant="info"
                  />
                </h4>

                <div className="bg-gray-50 p-4 rounded-xl space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900">Enable Spam Protection</div>
                      <div className="text-sm text-gray-600">Automatically detect and block spam</div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={botData.settings.security.spamProtection}
                        onChange={(e) => setBotData({
                          ...botData,
                          settings: {
                            ...botData.settings,
                            security: {
                              ...botData.settings.security,
                              spamProtection: e.target.checked
                            }
                          }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  {botData.settings.security.spamProtection && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Sensitivity Level
                      </label>
                      <select
                        value={botData.settings.security.spamSensitivity}
                        onChange={(e) => setBotData({
                          ...botData,
                          settings: {
                            ...botData.settings,
                            security: {
                              ...botData.settings.security,
                              spamSensitivity: e.target.value
                            }
                          }
                        })}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2"
                      >
                        <option value="low">Low - Only obvious spam</option>
                        <option value="medium">Medium - Balanced protection</option>
                        <option value="high">High - Strict filtering</option>
                      </select>
                      <p className="text-xs text-gray-500 mt-1">
                        Higher sensitivity may block legitimate messages
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Admin Notifications */}
              <div>
                <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Bell className="h-4 w-4" />
                  Admin Notifications
                  <TooltipIcon
                    content="Get notified about important events, security issues, and bot activity via email or Telegram."
                    variant="info"
                  />
                </h4>

                <div className="bg-gray-50 p-4 rounded-xl space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900">Enable Admin Notifications</div>
                      <div className="text-sm text-gray-600">Get alerts for important events</div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={botData.settings.security.adminNotifications}
                        onChange={(e) => setBotData({
                          ...botData,
                          settings: {
                            ...botData.settings,
                            security: {
                              ...botData.settings.security,
                              adminNotifications: e.target.checked
                            }
                          }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  {botData.settings.security.adminNotifications && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div className="flex items-center gap-3">
                          <Mail className="w-4 h-4 text-blue-500" />
                          <span className="text-sm font-medium">Email Notifications</span>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={botData.settings.notifications.emailNotifications}
                            onChange={(e) => setBotData({
                              ...botData,
                              settings: {
                                ...botData.settings,
                                notifications: {
                                  ...botData.settings.notifications,
                                  emailNotifications: e.target.checked
                                }
                              }
                            })}
                            className="sr-only peer"
                          />
                          <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                      <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div className="flex items-center gap-3">
                          <MessageSquare className="w-4 h-4 text-green-500" />
                          <span className="text-sm font-medium">Telegram Notifications</span>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={botData.settings.notifications.telegramNotifications}
                            onChange={(e) => setBotData({
                              ...botData,
                              settings: {
                                ...botData.settings,
                                notifications: {
                                  ...botData.settings.notifications,
                                  telegramNotifications: e.target.checked
                                }
                              }
                            })}
                            className="sr-only peer"
                          />
                          <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* IP Access Control */}
              <div>
                <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Wifi className="h-4 w-4" />
                  IP Access Control
                  <TooltipIcon
                    content="Control which IP addresses can access your bot. Use whitelist for maximum security or blacklist to block specific IPs."
                    variant="warning"
                  />
                </h4>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-green-50 p-4 rounded-xl">
                    <h5 className="font-medium text-green-900 mb-2 flex items-center gap-2">
                      <CheckCircle className="w-4 h-4" />
                      IP Whitelist
                    </h5>
                    <p className="text-sm text-green-800 mb-3">Allow only these IP addresses</p>
                    <textarea
                      value={botData.settings.security.ipWhitelist.join('\n')}
                      onChange={(e) => setBotData({
                        ...botData,
                        settings: {
                          ...botData.settings,
                          security: {
                            ...botData.settings.security,
                            ipWhitelist: e.target.value.split('\n').filter(ip => ip.trim())
                          }
                        }
                      })}
                      className="w-full border border-green-300 rounded-lg px-3 py-2 text-sm font-mono"
                      rows={4}
                      placeholder="***********&#10;10.0.0.0/8&#10;***********/24"
                    />
                    <p className="text-xs text-green-700 mt-1">One IP or CIDR range per line</p>
                  </div>

                  <div className="bg-red-50 p-4 rounded-xl">
                    <h5 className="font-medium text-red-900 mb-2 flex items-center gap-2">
                      <XCircle className="w-4 h-4" />
                      IP Blacklist
                    </h5>
                    <p className="text-sm text-red-800 mb-3">Block these IP addresses</p>
                    <textarea
                      value={botData.settings.security.ipBlacklist.join('\n')}
                      onChange={(e) => setBotData({
                        ...botData,
                        settings: {
                          ...botData.settings,
                          security: {
                            ...botData.settings.security,
                            ipBlacklist: e.target.value.split('\n').filter(ip => ip.trim())
                          }
                        }
                      })}
                      className="w-full border border-red-300 rounded-lg px-3 py-2 text-sm font-mono"
                      rows={4}
                      placeholder="***********00&#10;***********&#10;************/24"
                    />
                    <p className="text-xs text-red-700 mt-1">One IP or CIDR range per line</p>
                  </div>
                </div>
              </div>

              {/* Advanced Security */}
              <div>
                <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Lock className="h-4 w-4" />
                  Advanced Security
                  <TooltipIcon
                    content="Additional security features including two-factor authentication and session management."
                    variant="info"
                  />
                </h4>

                <div className="space-y-4">
                  <div className="bg-gray-50 p-4 rounded-xl">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <div className="font-medium text-gray-900">Two-Factor Authentication</div>
                        <div className="text-sm text-gray-600">Require 2FA for admin access</div>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={botData.settings.security.twoFactorAuth}
                          onChange={(e) => setBotData({
                            ...botData,
                            settings: {
                              ...botData.settings,
                              security: {
                                ...botData.settings.security,
                                twoFactorAuth: e.target.checked
                              }
                            }
                          })}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                    {botData.settings.security.twoFactorAuth && (
                      <div className="bg-blue-50 p-3 rounded-lg">
                        <p className="text-sm text-blue-800">
                          <UserCheck className="w-4 h-4 inline mr-1" />
                          2FA will be required for all admin panel access
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="bg-gray-50 p-4 rounded-xl">
                    <div className="mb-3">
                      <div className="font-medium text-gray-900">Session Timeout</div>
                      <div className="text-sm text-gray-600">Automatically log out inactive users</div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Timeout (seconds)
                        </label>
                        <input
                          type="number"
                          min="300"
                          max="86400"
                          value={botData.settings.security.sessionTimeout}
                          onChange={(e) => setBotData({
                            ...botData,
                            settings: {
                              ...botData.settings,
                              security: {
                                ...botData.settings.security,
                                sessionTimeout: parseInt(e.target.value) || 3600
                              }
                            }
                          })}
                          className="w-full border border-gray-300 rounded-lg px-3 py-2"
                        />
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-600">
                          {Math.floor(botData.settings.security.sessionTimeout / 3600)}h {Math.floor((botData.settings.security.sessionTimeout % 3600) / 60)}m
                        </div>
                        <div className="text-xs text-gray-500">
                          {botData.settings.security.sessionTimeout < 1800 ? 'Very short' :
                           botData.settings.security.sessionTimeout < 7200 ? 'Standard' :
                           'Extended'}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Security Summary */}
              <div className="bg-blue-50 p-4 rounded-xl">
                <h4 className="font-semibold text-blue-900 mb-2 flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Security Status
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="space-y-2">
                    <div className={`flex items-center gap-2 ${botData.settings.security.rateLimiting ? 'text-green-800' : 'text-red-800'}`}>
                      {botData.settings.security.rateLimiting ? (
                        <CheckCircle className="w-3 h-3" />
                      ) : (
                        <XCircle className="w-3 h-3" />
                      )}
                      <span>Rate Limiting</span>
                    </div>
                    <div className={`flex items-center gap-2 ${botData.settings.security.spamProtection ? 'text-green-800' : 'text-red-800'}`}>
                      {botData.settings.security.spamProtection ? (
                        <CheckCircle className="w-3 h-3" />
                      ) : (
                        <XCircle className="w-3 h-3" />
                      )}
                      <span>Spam Protection</span>
                    </div>
                    <div className={`flex items-center gap-2 ${botData.settings.security.adminNotifications ? 'text-green-800' : 'text-red-800'}`}>
                      {botData.settings.security.adminNotifications ? (
                        <CheckCircle className="w-3 h-3" />
                      ) : (
                        <XCircle className="w-3 h-3" />
                      )}
                      <span>Admin Notifications</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className={`flex items-center gap-2 ${botData.settings.security.twoFactorAuth ? 'text-green-800' : 'text-yellow-800'}`}>
                      {botData.settings.security.twoFactorAuth ? (
                        <CheckCircle className="w-3 h-3" />
                      ) : (
                        <AlertTriangle className="w-3 h-3" />
                      )}
                      <span>Two-Factor Auth</span>
                    </div>
                    <div className={`flex items-center gap-2 ${botData.settings.security.ipWhitelist.length > 0 || botData.settings.security.ipBlacklist.length > 0 ? 'text-green-800' : 'text-yellow-800'}`}>
                      {botData.settings.security.ipWhitelist.length > 0 || botData.settings.security.ipBlacklist.length > 0 ? (
                        <CheckCircle className="w-3 h-3" />
                      ) : (
                        <AlertTriangle className="w-3 h-3" />
                      )}
                      <span>IP Access Control</span>
                    </div>
                    <div className="flex items-center gap-2 text-blue-800">
                      <Info className="w-3 h-3" />
                      <span>Session Timeout: {Math.floor(botData.settings.security.sessionTimeout / 3600)}h</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default BotEditor
