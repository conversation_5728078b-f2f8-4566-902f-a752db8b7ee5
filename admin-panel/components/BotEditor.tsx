import React, { useState, useEffect } from 'react'
import {
  Edit,
  Save,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  MessageSquare,
  CreditCard,
  Palette,
  Package,
  Shield,
  Settings,
  Globe,
  Users,
  Zap,
  Eye,
  EyeOff,
  Copy,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Loader2,
  History,
  RotateCcw,
  Smartphone,
  Languages,
  DollarSign,
  Info,
  Sparkles,
  HelpCircle
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface BotEditorProps {
  botId: string
  onBack: () => void
  onSave: (botData: any) => void
}

interface BotData {
  id: string
  name: string
  username: string
  token: string
  description?: string
  isActive: boolean
  status: 'ACTIVE' | 'INACTIVE' | 'ERROR' | 'SETUP'
  webhookUrl?: string
  webhookStatus: 'CONNECTED' | 'DISCONNECTED' | 'ERROR'
  settings: {
    welcomeMessage: string
    language: string
    currency: string
    paymentMethods: string[]
    supportContact?: string
    storeInfo?: string
    theme: {
      primaryColor: string
      accentColor: string
    }
    features: {
      categories: boolean
      userProfiles: boolean
      balanceTopUp: boolean
      orderHistory: boolean
    }
    notifications: {
      orderReceived: boolean
      paymentConfirmed: boolean
      lowStock: boolean
    }
    security: {
      rateLimiting: boolean
      spamProtection: boolean
      adminNotifications: boolean
    }
  }
  versions?: Array<{
    id: string
    timestamp: string
    changes: string[]
    author: string
  }>
}

const BotEditor: React.FC<BotEditorProps> = ({ botId, onBack, onSave }) => {
  const [botData, setBotData] = useState<BotData | null>(null)
  const [originalData, setOriginalData] = useState<BotData | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('general')
  const [showToken, setShowToken] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [showVersionHistory, setShowVersionHistory] = useState(false)
  const [previewMode, setPreviewMode] = useState(false)

  useEffect(() => {
    fetchBotData()
  }, [botId])

  useEffect(() => {
    if (botData && originalData) {
      setHasChanges(JSON.stringify(botData) !== JSON.stringify(originalData))
    }
  }, [botData, originalData])

  const fetchBotData = async () => {
    try {
      setLoading(true)
      
      // Mock bot data for editing
      const mockBotData: BotData = {
        id: botId,
        name: 'Digital Store Bot',
        username: 'digitalstore_bot',
        token: '1234567890:AAEhBOweik6ad6PsVQF6XJhiknbhd6k-Ks',
        description: 'Premium digital products and software marketplace',
        isActive: true,
        status: 'ACTIVE',
        webhookUrl: 'https://api.teleshop.com/webhook/bot_001',
        webhookStatus: 'CONNECTED',
        settings: {
          welcomeMessage: 'Welcome to Digital Store! 🛍️ Browse our premium digital products and find exactly what you need.',
          language: 'en',
          currency: 'USD',
          paymentMethods: ['BITCOIN', 'ETHEREUM', 'USDT'],
          supportContact: '@digitalsupport',
          storeInfo: 'Premium digital marketplace with instant delivery and 24/7 support.',
          theme: {
            primaryColor: '#3B82F6',
            accentColor: '#8B5CF6'
          },
          features: {
            categories: true,
            userProfiles: true,
            balanceTopUp: true,
            orderHistory: true
          },
          notifications: {
            orderReceived: true,
            paymentConfirmed: true,
            lowStock: false
          },
          security: {
            rateLimiting: true,
            spamProtection: true,
            adminNotifications: true
          }
        },
        versions: [
          {
            id: 'v1.3',
            timestamp: new Date(Date.now() - 86400000).toISOString(),
            changes: ['Updated welcome message', 'Added USDT payment method'],
            author: 'John Seller'
          },
          {
            id: 'v1.2',
            timestamp: new Date(Date.now() - 172800000).toISOString(),
            changes: ['Changed primary color', 'Enabled user profiles'],
            author: 'John Seller'
          },
          {
            id: 'v1.1',
            timestamp: new Date(Date.now() - 259200000).toISOString(),
            changes: ['Initial bot setup', 'Basic configuration'],
            author: 'John Seller'
          }
        ]
      }
      
      setBotData(mockBotData)
      setOriginalData(JSON.parse(JSON.stringify(mockBotData)))
      setLoading(false)
    } catch (error) {
      console.error('Error fetching bot data:', error)
      toast.error('Failed to load bot data')
      setLoading(false)
    }
  }

  const handleSave = async () => {
    if (!botData) return

    setSaving(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      toast.success('Bot configuration saved successfully!')
      setOriginalData(JSON.parse(JSON.stringify(botData)))
      setHasChanges(false)
      onSave(botData)
    } catch (error) {
      console.error('Error saving bot:', error)
      toast.error('Failed to save bot configuration')
    } finally {
      setSaving(false)
    }
  }

  const handleReset = () => {
    if (originalData) {
      setBotData(JSON.parse(JSON.stringify(originalData)))
      setHasChanges(false)
      toast.success('Changes reset to last saved version')
    }
  }

  const handleVersionRestore = (version: any) => {
    // In a real implementation, this would fetch the specific version data
    toast.success(`Restored to version ${version.id}`)
    setShowVersionHistory(false)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied to clipboard!')
  }

  const togglePaymentMethod = (method: string) => {
    if (!botData) return
    
    const methods = botData.settings.paymentMethods.includes(method)
      ? botData.settings.paymentMethods.filter(m => m !== method)
      : [...botData.settings.paymentMethods, method]
    
    setBotData({
      ...botData,
      settings: {
        ...botData.settings,
        paymentMethods: methods
      }
    })
  }

  const tabs = [
    { id: 'general', name: 'General', icon: Settings },
    { id: 'messages', name: 'Messages', icon: MessageSquare },
    { id: 'localization', name: 'Localization', icon: Globe },
    { id: 'payments', name: 'Payments', icon: CreditCard },
    { id: 'features', name: 'Features', icon: Package },
    { id: 'theme', name: 'Theme', icon: Palette },
    { id: 'security', name: 'Security', icon: Shield }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-3">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <span className="text-lg font-medium text-gray-600">Loading bot configuration...</span>
        </div>
      </div>
    )
  }

  if (!botData) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-16 w-16 mx-auto text-red-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Bot Not Found</h3>
        <p className="text-gray-600 mb-6">Unable to load bot configuration.</p>
        <button
          onClick={onBack}
          className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg flex items-center gap-2 mx-auto"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Bots
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div className="flex items-center gap-4">
          <button
            onClick={onBack}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Edit Bot Configuration
            </h1>
            <p className="text-gray-600 mt-1 flex items-center gap-2">
              <Bot className="h-4 w-4" />
              {botData.name} (@{botData.username})
              {hasChanges && (
                <span className="px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">
                  Unsaved Changes
                </span>
              )}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3 mt-4 sm:mt-0">
          <button
            onClick={() => setShowVersionHistory(true)}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium flex items-center gap-2"
          >
            <History className="w-4 h-4" />
            Version History
          </button>
          <button
            onClick={handleReset}
            disabled={!hasChanges}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            <RotateCcw className="w-4 h-4" />
            Reset
          </button>
          <button
            onClick={() => setPreviewMode(!previewMode)}
            className="px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-lg hover:from-purple-600 hover:to-pink-700 transition-all duration-200 font-medium flex items-center gap-2"
          >
            <Eye className="w-4 h-4" />
            {previewMode ? 'Exit Preview' : 'Preview'}
          </button>
          <button
            onClick={handleSave}
            disabled={saving || !hasChanges}
            className="px-6 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg hover:from-green-600 hover:to-emerald-700 transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {saving ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                Save Changes
              </>
            )}
          </button>
        </div>
      </div>

      {/* Preview Mode */}
      {previewMode && (
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-xl border border-purple-200">
          <h3 className="font-bold text-purple-900 mb-4 flex items-center gap-2">
            <Eye className="w-5 h-5" />
            Bot Preview
          </h3>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center gap-3 mb-4">
              <div
                className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold"
                style={{ backgroundColor: botData.settings.theme.primaryColor }}
              >
                {botData.name.charAt(0)}
              </div>
              <div>
                <div className="font-semibold text-gray-900">{botData.name}</div>
                <div className="text-sm text-gray-500">@{botData.username}</div>
              </div>
            </div>
            <div
              className="px-4 py-3 rounded-lg text-white text-sm mb-4"
              style={{ backgroundColor: botData.settings.theme.primaryColor }}
            >
              {botData.settings.welcomeMessage}
            </div>
            <div className="flex gap-2">
              <button
                className="px-4 py-2 rounded-lg text-white text-sm"
                style={{ backgroundColor: botData.settings.theme.accentColor }}
              >
                🛍️ Browse Products
              </button>
              <button
                className="px-4 py-2 rounded-lg border text-sm"
                style={{ borderColor: botData.settings.theme.primaryColor, color: botData.settings.theme.primaryColor }}
              >
                👤 My Profile
              </button>
              <button
                className="px-4 py-2 rounded-lg border text-sm"
                style={{ borderColor: botData.settings.theme.primaryColor, color: botData.settings.theme.primaryColor }}
              >
                ℹ️ Store Info
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* General Tab */}
          {activeTab === 'general' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Bot className="h-4 w-4 inline mr-1" />
                    Bot Name
                  </label>
                  <input
                    type="text"
                    value={botData.name}
                    onChange={(e) => setBotData({...botData, name: e.target.value})}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Smartphone className="h-4 w-4 inline mr-1" />
                    Bot Username
                  </label>
                  <input
                    type="text"
                    value={botData.username}
                    onChange={(e) => setBotData({...botData, username: e.target.value})}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <MessageSquare className="h-4 w-4 inline mr-1" />
                  Description
                </label>
                <textarea
                  value={botData.description || ''}
                  onChange={(e) => setBotData({...botData, description: e.target.value})}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  rows={3}
                  placeholder="Brief description of your bot store..."
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Zap className="h-4 w-4 inline mr-1" />
                  Bot Token
                </label>
                <div className="relative">
                  <input
                    type={showToken ? 'text' : 'password'}
                    value={botData.token}
                    onChange={(e) => setBotData({...botData, token: e.target.value})}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 pr-20 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center gap-2 pr-3">
                    <button
                      type="button"
                      onClick={() => setShowToken(!showToken)}
                      className="p-1 text-gray-400 hover:text-gray-600"
                    >
                      {showToken ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                    <button
                      type="button"
                      onClick={() => copyToClipboard(botData.token)}
                      className="p-1 text-gray-400 hover:text-gray-600"
                    >
                      <Copy className="h-4 w-4" />
                    </button>
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-1">Get this from @BotFather on Telegram</p>
              </div>
            </div>
          )}

          {/* Messages Tab */}
          {activeTab === 'messages' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <MessageSquare className="h-4 w-4 inline mr-1" />
                  Welcome Message
                </label>
                <textarea
                  value={botData.settings.welcomeMessage}
                  onChange={(e) => setBotData({
                    ...botData,
                    settings: { ...botData.settings, welcomeMessage: e.target.value }
                  })}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  rows={4}
                  placeholder="Welcome message shown to new users..."
                />
                <p className="text-xs text-gray-500 mt-1">This message will be sent when users first start your bot</p>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Info className="h-4 w-4 inline mr-1" />
                  Store Information
                </label>
                <textarea
                  value={botData.settings.storeInfo || ''}
                  onChange={(e) => setBotData({
                    ...botData,
                    settings: { ...botData.settings, storeInfo: e.target.value }
                  })}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  rows={3}
                  placeholder="Information about your store, policies, etc..."
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Users className="h-4 w-4 inline mr-1" />
                  Support Contact
                </label>
                <input
                  type="text"
                  value={botData.settings.supportContact || ''}
                  onChange={(e) => setBotData({
                    ...botData,
                    settings: { ...botData.settings, supportContact: e.target.value }
                  })}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  placeholder="@support_<NAME_EMAIL>"
                />
                <p className="text-xs text-gray-500 mt-1">Telegram username or email for customer support</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default BotEditor
