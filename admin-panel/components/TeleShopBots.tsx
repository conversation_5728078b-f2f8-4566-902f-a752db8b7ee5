import React, { useState, useEffect } from 'react'
import {
  Plus,
  Search,
  Bot,
  Activity,
  Users,
  ShoppingCart,
  Settings,
  Eye,
  Edit,
  Trash2,
  Power,
  PowerOff,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Calendar,
  TrendingUp,
  Zap,
  Globe,
  MessageSquare,
  CreditCard,
  Loader2,
  ExternalLink,
  Copy,
  RefreshCw,
  BarChart3,
  Store,
  Smartphone,
  Wifi,
  WifiOff,
  Clock,
  DollarSign,
  Package,
  Filter,
  ChevronDown,
  X
} from 'lucide-react'
import { toast } from 'react-hot-toast'
import { useAuth } from '../contexts/AuthContext'

// Bot interfaces
interface TelegramBot {
  id: string
  name: string
  username: string
  token: string
  description?: string
  isActive: boolean
  status: 'ACTIVE' | 'INACTIVE' | 'ERROR' | 'SETUP'
  webhookUrl?: string
  webhookStatus: 'CONNECTED' | 'DISCONNECTED' | 'ERROR'
  ownerId: string
  storeId?: string
  createdAt: string
  updatedAt: string
  lastActivity?: string
  settings: BotSettings
  analytics: BotAnalytics
  store?: {
    id: string
    name: string
    description?: string
  }
}

interface BotSettings {
  welcomeMessage: string
  language: string
  currency: string
  paymentMethods: string[]
  supportContact?: string
  storeInfo?: string
  theme: {
    primaryColor: string
    accentColor: string
  }
  features: {
    categories: boolean
    userProfiles: boolean
    balanceTopUp: boolean
    orderHistory: boolean
  }
}

interface BotAnalytics {
  totalCustomers: number
  totalOrders: number
  totalRevenue: number
  activeUsers24h: number
  ordersToday: number
  conversionRate: number
  averageOrderValue: number
}

interface CreateBotData {
  name: string
  username: string
  token: string
  description: string
  storeId?: string
}

const TeleShopBots: React.FC = () => {
  const { user } = useAuth()
  const [bots, setBots] = useState<TelegramBot[]>([])
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState<string | null>(null)
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('All Status')
  const [activityFilter, setActivityFilter] = useState<string>('All Activity')
  
  // Table states
  const [currentPage, setCurrentPage] = useState(1)
  const [sortBy, setSortBy] = useState<string>('createdAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const itemsPerPage = 8
  
  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showAnalyticsModal, setShowAnalyticsModal] = useState(false)
  const [selectedBot, setSelectedBot] = useState<TelegramBot | null>(null)
  const [createBotData, setCreateBotData] = useState<CreateBotData>({
    name: '',
    username: '',
    token: '',
    description: '',
    storeId: ''
  })

  useEffect(() => {
    fetchBots()
  }, [])

  const fetchBots = async () => {
    try {
      setLoading(true)

      // Try to fetch from API first, fallback to mock data
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/bots?limit=100`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })
        
        if (response.ok) {
          const data = await response.json()
          setBots(data.data.bots || [])
        } else {
          throw new Error('API request failed')
        }
      } catch (apiError) {
        console.log('Using mock data due to API error:', apiError)

        // Mock Telegram bots data
        setBots([
          {
            id: 'bot_001',
            name: 'Digital Store Bot',
            username: 'digitalstore_bot',
            token: '1234567890:AAEhBOweik6ad6PsVQF6XJhiknbhd6k-Ks',
            description: 'Premium digital products and software marketplace',
            isActive: true,
            status: 'ACTIVE',
            webhookUrl: 'https://api.teleshop.com/webhook/bot_001',
            webhookStatus: 'CONNECTED',
            ownerId: 'seller_001',
            storeId: 'store_001',
            createdAt: new Date(Date.now() - 2592000000).toISOString(), // 30 days ago
            updatedAt: new Date(Date.now() - 86400000).toISOString(),
            lastActivity: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
            settings: {
              welcomeMessage: 'Welcome to Digital Store! 🛍️ Browse our premium digital products.',
              language: 'en',
              currency: 'USD',
              paymentMethods: ['BITCOIN', 'ETHEREUM', 'USDT'],
              supportContact: '@digitalsupport',
              storeInfo: 'Premium digital marketplace with instant delivery',
              theme: {
                primaryColor: '#3B82F6',
                accentColor: '#8B5CF6'
              },
              features: {
                categories: true,
                userProfiles: true,
                balanceTopUp: true,
                orderHistory: true
              }
            },
            analytics: {
              totalCustomers: 1247,
              totalOrders: 856,
              totalRevenue: 45670.50,
              activeUsers24h: 89,
              ordersToday: 23,
              conversionRate: 68.7,
              averageOrderValue: 53.35
            },
            store: {
              id: 'store_001',
              name: "John's Digital Marketplace",
              description: 'Premium digital products and software'
            }
          },
          {
            id: 'bot_002',
            name: 'Crypto Tools Bot',
            username: 'cryptotools_bot',
            token: '9876543210:BBFhCPxfjl7be7QsWRG7YKijlochd7l-Lt',
            description: 'Cryptocurrency tools and educational content',
            isActive: true,
            status: 'ACTIVE',
            webhookUrl: 'https://api.teleshop.com/webhook/bot_002',
            webhookStatus: 'CONNECTED',
            ownerId: 'seller_002',
            storeId: 'store_002',
            createdAt: new Date(Date.now() - 1728000000).toISOString(), // 20 days ago
            updatedAt: new Date(Date.now() - 172800000).toISOString(),
            lastActivity: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
            settings: {
              welcomeMessage: 'Welcome to Crypto Tools! 🚀 Your gateway to crypto success.',
              language: 'en',
              currency: 'USD',
              paymentMethods: ['BITCOIN', 'ETHEREUM'],
              supportContact: '@cryptosupport',
              storeInfo: 'Professional cryptocurrency tools and education',
              theme: {
                primaryColor: '#10B981',
                accentColor: '#F59E0B'
              },
              features: {
                categories: true,
                userProfiles: true,
                balanceTopUp: true,
                orderHistory: true
              }
            },
            analytics: {
              totalCustomers: 892,
              totalOrders: 634,
              totalRevenue: 28940.75,
              activeUsers24h: 67,
              ordersToday: 18,
              conversionRate: 71.1,
              averageOrderValue: 45.65
            },
            store: {
              id: 'store_002',
              name: 'CryptoGoods Store',
              description: 'Cryptocurrency tools and educational content'
            }
          },
          {
            id: 'bot_003',
            name: 'GameDev Assets Bot',
            username: 'gamedev_assets_bot',
            token: '5555666677:CCGiDQyglm8cf8RtXSH8ZLjklpchd8m-Mu',
            description: 'Game development tools and assets',
            isActive: false,
            status: 'INACTIVE',
            webhookUrl: 'https://api.teleshop.com/webhook/bot_003',
            webhookStatus: 'DISCONNECTED',
            ownerId: 'seller_003',
            storeId: 'store_003',
            createdAt: new Date(Date.now() - 864000000).toISOString(), // 10 days ago
            updatedAt: new Date(Date.now() - 432000000).toISOString(),
            lastActivity: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
            settings: {
              welcomeMessage: 'Welcome to GameDev Assets! 🎮 Create amazing games with our tools.',
              language: 'en',
              currency: 'USD',
              paymentMethods: ['BITCOIN', 'USDT'],
              supportContact: '@gamedevsupport',
              storeInfo: 'Professional game development assets and tools',
              theme: {
                primaryColor: '#8B5CF6',
                accentColor: '#EC4899'
              },
              features: {
                categories: true,
                userProfiles: false,
                balanceTopUp: false,
                orderHistory: true
              }
            },
            analytics: {
              totalCustomers: 234,
              totalOrders: 156,
              totalRevenue: 8750.25,
              activeUsers24h: 12,
              ordersToday: 3,
              conversionRate: 66.7,
              averageOrderValue: 56.09
            },
            store: {
              id: 'store_003',
              name: 'GameDev Assets',
              description: 'Game development tools and assets'
            }
          }
        ])
      }

      setLoading(false)
    } catch (error) {
      console.error('Error fetching bots:', error)
      toast.error('Failed to fetch bots')
      setLoading(false)
    }
  }

  // Bot management functions
  const handleCreateBot = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!createBotData.name || !createBotData.username || !createBotData.token) {
      toast.error('Please fill in all required fields')
      return
    }

    setUpdating('creating')
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/bots`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(createBotData)
      })

      if (response.ok) {
        toast.success('Bot created successfully!')
        setShowCreateModal(false)
        setCreateBotData({ name: '', username: '', token: '', description: '', storeId: '' })
        fetchBots()
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || 'Failed to create bot')
      }
    } catch (error) {
      console.error('Error creating bot:', error)
      toast.error('Error creating bot')
    } finally {
      setUpdating(null)
    }
  }

  const handleToggleBotStatus = async (botId: string, isActive: boolean) => {
    setUpdating(botId)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/bots/${botId}/toggle`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ isActive: !isActive })
      })

      if (response.ok) {
        toast.success(`Bot ${!isActive ? 'activated' : 'deactivated'} successfully!`)
        fetchBots()
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || 'Failed to update bot status')
      }
    } catch (error) {
      console.error('Error updating bot status:', error)
      toast.error('Error updating bot status')
    } finally {
      setUpdating(null)
    }
  }

  const handleDeleteBot = async () => {
    if (!selectedBot) return

    setUpdating(selectedBot.id)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/bots/${selectedBot.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (response.ok) {
        toast.success('Bot deleted successfully!')
        setShowDeleteModal(false)
        setSelectedBot(null)
        fetchBots()
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || 'Failed to delete bot')
      }
    } catch (error) {
      console.error('Error deleting bot:', error)
      toast.error('Error deleting bot')
    } finally {
      setUpdating(null)
    }
  }

  const handleRefreshWebhook = async (botId: string) => {
    setUpdating(botId)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/bots/${botId}/webhook/refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (response.ok) {
        toast.success('Webhook refreshed successfully!')
        fetchBots()
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || 'Failed to refresh webhook')
      }
    } catch (error) {
      console.error('Error refreshing webhook:', error)
      toast.error('Error refreshing webhook')
    } finally {
      setUpdating(null)
    }
  }

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('')
    setStatusFilter('All Status')
    setActivityFilter('All Activity')
    setCurrentPage(1)
  }

  // Handle sorting
  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(column)
      setSortOrder('asc')
    }
  }

  // Filtering and sorting logic
  const filteredBots = bots.filter(bot => {
    const matchesSearch = bot.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bot.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (bot.description || '').toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'All Status' ||
      (statusFilter === 'Active' && bot.isActive) ||
      (statusFilter === 'Inactive' && !bot.isActive) ||
      (statusFilter === 'Error' && bot.status === 'ERROR')

    const matchesActivity = activityFilter === 'All Activity' ||
      (activityFilter === 'Recent' && bot.lastActivity && new Date(bot.lastActivity) > new Date(Date.now() - 86400000)) ||
      (activityFilter === 'Stale' && (!bot.lastActivity || new Date(bot.lastActivity) < new Date(Date.now() - 86400000)))

    return matchesSearch && matchesStatus && matchesActivity
  })

  // Sort bots
  const sortedBots = [...filteredBots].sort((a, b) => {
    let aValue: any, bValue: any

    switch (sortBy) {
      case 'createdAt':
        aValue = new Date(a.createdAt)
        bValue = new Date(b.createdAt)
        break
      case 'name':
        aValue = a.name
        bValue = b.name
        break
      case 'status':
        aValue = a.status
        bValue = b.status
        break
      case 'customers':
        aValue = a.analytics.totalCustomers
        bValue = b.analytics.totalCustomers
        break
      case 'orders':
        aValue = a.analytics.totalOrders
        bValue = b.analytics.totalOrders
        break
      case 'revenue':
        aValue = a.analytics.totalRevenue
        bValue = b.analytics.totalRevenue
        break
      default:
        aValue = a.name
        bValue = b.name
    }

    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1
    return 0
  })

  // Utility functions
  const getStatusColor = (bot: TelegramBot) => {
    switch (bot.status) {
      case 'ACTIVE':
        return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800'
      case 'INACTIVE':
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800'
      case 'ERROR':
        return 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800'
      case 'SETUP':
        return 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (bot: TelegramBot) => {
    switch (bot.status) {
      case 'ACTIVE':
        return <CheckCircle className="h-3 w-3" />
      case 'INACTIVE':
        return <XCircle className="h-3 w-3" />
      case 'ERROR':
        return <AlertTriangle className="h-3 w-3" />
      case 'SETUP':
        return <Clock className="h-3 w-3" />
      default:
        return <XCircle className="h-3 w-3" />
    }
  }

  const getWebhookStatusColor = (status: string) => {
    switch (status) {
      case 'CONNECTED':
        return 'text-green-600'
      case 'DISCONNECTED':
        return 'text-gray-600'
      case 'ERROR':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getWebhookStatusIcon = (status: string) => {
    switch (status) {
      case 'CONNECTED':
        return <Wifi className="h-3 w-3" />
      case 'DISCONNECTED':
        return <WifiOff className="h-3 w-3" />
      case 'ERROR':
        return <AlertTriangle className="h-3 w-3" />
      default:
        return <WifiOff className="h-3 w-3" />
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getTimeSinceLastActivity = (lastActivityDate?: string) => {
    if (!lastActivityDate) return 'Never'

    const now = new Date()
    const lastActivity = new Date(lastActivityDate)
    const diffInHours = Math.floor((now.getTime() - lastActivity.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 30) return `${diffInDays}d ago`
    const diffInMonths = Math.floor(diffInDays / 30)
    return `${diffInMonths}mo ago`
  }

  const totalPages = Math.ceil(sortedBots.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedBots = sortedBots.slice(startIndex, startIndex + itemsPerPage)

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-3">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <span className="text-lg font-medium text-gray-600">Loading bots...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            My Telegram Bots
          </h1>
          <p className="text-gray-600 mt-2 flex items-center gap-2">
            <Bot className="h-4 w-4" />
            Create and manage your Telegram bot stores ({sortedBots.length} total)
          </p>
        </div>
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <button
            onClick={() => setShowCreateModal(true)}
            className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg flex items-center gap-2"
          >
            <Plus className="w-5 h-5" />
            Create New Bot
          </button>
        </div>
      </div>

      {/* Advanced Filters */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative col-span-1 md:col-span-2">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search bots by name, username, or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
            />
          </div>

          {/* Status Filter */}
          <div className="relative">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 appearance-none bg-white"
            >
              <option value="All Status">All Status</option>
              <option value="Active">Active</option>
              <option value="Inactive">Inactive</option>
              <option value="Error">Error</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>

          {/* Activity Filter */}
          <div className="relative">
            <select
              value={activityFilter}
              onChange={(e) => setActivityFilter(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 appearance-none bg-white"
            >
              <option value="All Activity">All Activity</option>
              <option value="Recent">Recent Activity</option>
              <option value="Stale">Stale Bots</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>
        </div>

        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-gray-600">
            Showing {sortedBots.length} of {bots.length} bots
          </div>
          <button
            onClick={clearFilters}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200 flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Clear Filters
          </button>
        </div>
      </div>

      {/* Bots Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {paginatedBots.map((bot) => (
          <div key={bot.id} className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-200">
            {/* Bot Header */}
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl">
                    <Bot className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">{bot.name}</h3>
                    <p className="text-sm text-gray-500 flex items-center gap-1">
                      <Smartphone className="h-3 w-3" />
                      @{bot.username}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold ${getStatusColor(bot)} shadow-sm`}>
                    {getStatusIcon(bot)}
                    <span className="ml-1">{bot.status}</span>
                  </span>
                </div>
              </div>

              {bot.description && (
                <p className="text-sm text-gray-600 mt-3">{bot.description}</p>
              )}
            </div>

            {/* Bot Stats */}
            <div className="p-6">
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{bot.analytics.totalCustomers.toLocaleString()}</div>
                  <div className="text-xs text-gray-500 flex items-center justify-center gap-1">
                    <Users className="h-3 w-3" />
                    Customers
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{bot.analytics.totalOrders.toLocaleString()}</div>
                  <div className="text-xs text-gray-500 flex items-center justify-center gap-1">
                    <ShoppingCart className="h-3 w-3" />
                    Orders
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-2 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Revenue:</span>
                  <span className="font-semibold text-gray-900">${bot.analytics.totalRevenue.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Webhook:</span>
                  <span className={`flex items-center gap-1 ${getWebhookStatusColor(bot.webhookStatus)}`}>
                    {getWebhookStatusIcon(bot.webhookStatus)}
                    <span className="text-xs">{bot.webhookStatus}</span>
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Last Activity:</span>
                  <span className="text-xs text-gray-500">{getTimeSinceLastActivity(bot.lastActivity)}</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-2">
                <button
                  onClick={() => {
                    setSelectedBot(bot)
                    setShowAnalyticsModal(true)
                  }}
                  className="flex-1 px-3 py-2 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-lg hover:from-purple-600 hover:to-pink-700 transition-all duration-200 text-sm font-medium flex items-center justify-center gap-1"
                >
                  <BarChart3 className="h-4 w-4" />
                  Analytics
                </button>
                <button
                  onClick={() => {
                    setSelectedBot(bot)
                    setShowEditModal(true)
                  }}
                  className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-all duration-200"
                  title="Edit Bot"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleToggleBotStatus(bot.id, bot.isActive)}
                  disabled={updating === bot.id}
                  className={`p-2 rounded-lg transition-all duration-200 ${
                    bot.isActive
                      ? "text-red-600 hover:text-red-800 hover:bg-red-50"
                      : "text-green-600 hover:text-green-800 hover:bg-green-50"
                  }`}
                  title={bot.isActive ? "Deactivate Bot" : "Activate Bot"}
                >
                  {updating === bot.id ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : bot.isActive ? (
                    <PowerOff className="h-4 w-4" />
                  ) : (
                    <Power className="h-4 w-4" />
                  )}
                </button>
                <button
                  onClick={() => handleRefreshWebhook(bot.id)}
                  disabled={updating === bot.id}
                  className="p-2 text-purple-600 hover:text-purple-800 hover:bg-purple-50 rounded-lg transition-all duration-200"
                  title="Refresh Webhook"
                >
                  <RefreshCw className="h-4 w-4" />
                </button>
                <button
                  onClick={() => {
                    setSelectedBot(bot)
                    setShowDeleteModal(true)
                  }}
                  className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-all duration-200"
                  title="Delete Bot"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {sortedBots.length === 0 && (
        <div className="text-center py-12">
          <Bot className="h-16 w-16 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No bots found</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || statusFilter !== 'All Status' || activityFilter !== 'All Activity'
              ? 'Try adjusting your filters to see more results.'
              : 'Create your first Telegram bot to get started with your digital store.'}
          </p>
          {(!searchTerm && statusFilter === 'All Status' && activityFilter === 'All Activity') && (
            <button
              onClick={() => setShowCreateModal(true)}
              className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg flex items-center gap-2 mx-auto"
            >
              <Plus className="w-5 h-5" />
              Create Your First Bot
            </button>
          )}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="text-sm text-gray-600">
            Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, sortedBots.length)} of {sortedBots.length} bots
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="px-4 py-2 border border-gray-300 rounded-lg text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors duration-200"
            >
              Previous
            </button>
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1
                return (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`px-3 py-2 text-sm rounded-lg transition-colors duration-200 ${
                      currentPage === page
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    {page}
                  </button>
                )
              })}
            </div>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="px-4 py-2 border border-gray-300 rounded-lg text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors duration-200"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default TeleShopBots
