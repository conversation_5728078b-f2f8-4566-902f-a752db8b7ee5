import React, { useState, useEffect } from 'react'
import {
  Search,
  Download,
  Plus,
  MoreVertical,
  UserPlus,
  User,
  Mail,
  Calendar,
  Shield,
  CheckCircle,
  XCircle,
  Ban,
  Edit,
  Trash2,
  Eye,
  ArrowUpDown,
  ChevronDown,
  X,
  AlertTriangle,
  Loader2,
  Package,
  DollarSign,
  Clock,
  Phone,
  Store as StoreIcon,
  Users,
  Activity
} from 'lucide-react'
import { apiService } from '../services/api'
import { toast } from 'react-hot-toast'
import { useAuth } from '../contexts/AuthContext'

// Telegram Customer interfaces for bot users
interface TelegramCustomer {
  id: string
  telegramUserId: string
  telegramUsername?: string
  telegramFirstName?: string
  telegramLastName?: string
  telegramLanguageCode?: string
  firstInteractionDate: string
  lastActivityDate: string
  registrationDate: string
  lastOrderDate?: string
  totalOrders: number
  totalSpent: number
  averageOrderValue: number
  isActive: boolean
  isBlocked: boolean
  preferredLanguage?: string
  timezone?: string
  orders?: Order[]
}

interface Order {
  id: string
  totalAmount: number
  currency: string
  status: string
  createdAt: string
  items?: OrderItem[]
}

interface OrderItem {
  id: string
  productName: string
  quantity: number
  price: number
}

interface EditTelegramCustomerData {
  telegramUsername?: string
  telegramFirstName?: string
  telegramLastName?: string
  isActive: boolean
  isBlocked: boolean
  preferredLanguage?: string
}

const TeleShopCustomers: React.FC = () => {
  const { user } = useAuth()
  const [customers, setCustomers] = useState<TelegramCustomer[]>([])
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState<string | null>(null)

  // Filter states
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<string>('All Status')
  const [selectedOrderActivity, setSelectedOrderActivity] = useState<string>('All Activity')
  const [selectedLanguage, setSelectedLanguage] = useState<string>('All Languages')
  const [dateFrom, setDateFrom] = useState('')
  const [dateTo, setDateTo] = useState('')

  // Table states
  const [currentPage, setCurrentPage] = useState(1)
  const [sortBy, setSortBy] = useState<string>('lastActivityDate')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const itemsPerPage = 10

  // Modal states
  const [showEditModal, setShowEditModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showOrdersModal, setShowOrdersModal] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState<TelegramCustomer | null>(null)
  const [editCustomerData, setEditCustomerData] = useState<EditTelegramCustomerData>({
    telegramUsername: '',
    telegramFirstName: '',
    telegramLastName: '',
    isActive: true,
    isBlocked: false,
    preferredLanguage: ''
  })

  useEffect(() => {
    fetchCustomers()
  }, [])

  const fetchCustomers = async () => {
    try {
      setLoading(true)

      // Try to fetch from API first, fallback to mock data
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/customers?limit=100`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })

        if (response.ok) {
          const data = await response.json()
          setCustomers(data.data.customers || [])
        } else {
          throw new Error('API request failed')
        }
      } catch (apiError) {
        console.log('Using mock data due to API error:', apiError)

        // Mock Telegram customers data
        setCustomers([
          {
            id: 'tg_customer_1',
            telegramUserId: '123456789',
            telegramUsername: 'johndoe_crypto',
            telegramFirstName: 'John',
            telegramLastName: 'Doe',
            telegramLanguageCode: 'en',
            firstInteractionDate: new Date(Date.now() - 2592000000).toISOString(), // 30 days ago
            lastActivityDate: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
            registrationDate: new Date(Date.now() - 2592000000).toISOString(),
            lastOrderDate: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
            totalOrders: 8,
            totalSpent: 459.97,
            averageOrderValue: 57.50,
            isActive: true,
            isBlocked: false,
            preferredLanguage: 'en',
            timezone: 'UTC-5'
          },
          {
            id: 'tg_customer_2',
            telegramUserId: '987654321',
            telegramUsername: 'crypto_jane',
            telegramFirstName: 'Jane',
            telegramLastName: 'Smith',
            telegramLanguageCode: 'en',
            firstInteractionDate: new Date(Date.now() - 1728000000).toISOString(), // 20 days ago
            lastActivityDate: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
            registrationDate: new Date(Date.now() - 1728000000).toISOString(),
            lastOrderDate: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
            totalOrders: 5,
            totalSpent: 299.95,
            averageOrderValue: 59.99,
            isActive: true,
            isBlocked: false,
            preferredLanguage: 'en',
            timezone: 'UTC-8'
          },
          {
            id: 'tg_customer_3',
            telegramUserId: '456789123',
            telegramUsername: 'digital_bob',
            telegramFirstName: 'Bob',
            telegramLastName: 'Wilson',
            telegramLanguageCode: 'es',
            firstInteractionDate: new Date(Date.now() - 5184000000).toISOString(), // 60 days ago
            lastActivityDate: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
            registrationDate: new Date(Date.now() - 5184000000).toISOString(),
            lastOrderDate: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
            totalOrders: 12,
            totalSpent: 899.88,
            averageOrderValue: 74.99,
            isActive: true,
            isBlocked: false,
            preferredLanguage: 'es',
            timezone: 'UTC+1'
          },
          {
            id: 'tg_customer_4',
            telegramUserId: '789123456',
            telegramUsername: 'alice_tech',
            telegramFirstName: 'Alice',
            telegramLastName: 'Brown',
            telegramLanguageCode: 'fr',
            firstInteractionDate: new Date(Date.now() - 864000000).toISOString(), // 10 days ago
            lastActivityDate: new Date(Date.now() - 604800000).toISOString(), // 7 days ago
            registrationDate: new Date(Date.now() - 864000000).toISOString(),
            lastOrderDate: new Date(Date.now() - 604800000).toISOString(),
            totalOrders: 3,
            totalSpent: 149.97,
            averageOrderValue: 49.99,
            isActive: false,
            isBlocked: false,
            preferredLanguage: 'fr',
            timezone: 'UTC+2'
          },
          {
            id: 'tg_customer_5',
            telegramUserId: '321654987',
            telegramUsername: 'spammer_mike',
            telegramFirstName: 'Mike',
            telegramLastName: 'Jones',
            telegramLanguageCode: 'en',
            firstInteractionDate: new Date(Date.now() - 432000000).toISOString(), // 5 days ago
            lastActivityDate: new Date(Date.now() - 432000000).toISOString(),
            registrationDate: new Date(Date.now() - 432000000).toISOString(),
            totalOrders: 0,
            totalSpent: 0,
            averageOrderValue: 0,
            isActive: true,
            isBlocked: true, // Blocked for spam
            preferredLanguage: 'en',
            timezone: 'UTC-3'
          }
        ])
      }

      setLoading(false)
    } catch (error) {
      console.error('Error fetching customers:', error)
      toast.error('Failed to fetch customers')
      setLoading(false)
    }
  }

  // Telegram Customer management functions
  const handleEditCustomer = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedCustomer) return

    setUpdating(selectedCustomer.id)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/customers/${selectedCustomer.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(editCustomerData)
      })

      if (response.ok) {
        toast.success('Telegram customer updated successfully!')
        setShowEditModal(false)
        setSelectedCustomer(null)
        fetchCustomers()
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || 'Failed to update customer')
      }
    } catch (error) {
      console.error('Error updating customer:', error)
      toast.error('Error updating customer')
    } finally {
      setUpdating(null)
    }
  }

  const handleToggleCustomerStatus = async (customerId: string, isActive: boolean) => {
    setUpdating(customerId)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/customers/${customerId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ isActive: !isActive })
      })

      if (response.ok) {
        toast.success(`Customer ${!isActive ? 'activated' : 'deactivated'} successfully!`)
        fetchCustomers()
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || 'Failed to update customer status')
      }
    } catch (error) {
      console.error('Error updating customer status:', error)
      toast.error('Error updating customer status')
    } finally {
      setUpdating(null)
    }
  }

  const handleToggleCustomerBlock = async (customerId: string, isBlocked: boolean) => {
    setUpdating(customerId)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/customers/${customerId}/block`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ isBlocked: !isBlocked })
      })

      if (response.ok) {
        toast.success(`Customer ${!isBlocked ? 'blocked' : 'unblocked'} successfully!`)
        fetchCustomers()
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || 'Failed to update customer block status')
      }
    } catch (error) {
      console.error('Error updating customer block status:', error)
      toast.error('Error updating customer block status')
    } finally {
      setUpdating(null)
    }
  }

  const handleDeleteCustomer = async () => {
    if (!selectedCustomer) return

    setUpdating(selectedCustomer.id)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/customers/${selectedCustomer.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (response.ok) {
        toast.success('Telegram customer deleted successfully!')
        setShowDeleteModal(false)
        setSelectedCustomer(null)
        fetchCustomers()
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || 'Failed to delete customer')
      }
    } catch (error) {
      console.error('Error deleting customer:', error)
      toast.error('Error deleting customer')
    } finally {
      setUpdating(null)
    }
  }

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('')
    setSelectedStatus('All Status')
    setSelectedOrderActivity('All Activity')
    setSelectedLanguage('All Languages')
    setDateFrom('')
    setDateTo('')
    setCurrentPage(1)
  }

  // Handle sorting
  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(column)
      setSortOrder('asc')
    }
  }

  // Filtering and sorting logic for Telegram customers
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (customer.telegramUsername || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (customer.telegramFirstName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (customer.telegramLastName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.telegramUserId.includes(searchTerm)

    const matchesStatus = selectedStatus === 'All Status' ||
      (selectedStatus === 'Active' && customer.isActive && !customer.isBlocked) ||
      (selectedStatus === 'Inactive' && !customer.isActive) ||
      (selectedStatus === 'Blocked' && customer.isBlocked)

    const matchesOrderActivity = selectedOrderActivity === 'All Activity' ||
      (selectedOrderActivity === 'Has Orders' && customer.totalOrders > 0) ||
      (selectedOrderActivity === 'No Orders' && customer.totalOrders === 0)

    const matchesLanguage = selectedLanguage === 'All Languages' ||
      customer.preferredLanguage === selectedLanguage ||
      customer.telegramLanguageCode === selectedLanguage

    // Date filtering based on registration date
    let matchesDate = true
    if (dateFrom || dateTo) {
      const customerDate = new Date(customer.registrationDate)
      if (dateFrom) {
        const fromDate = new Date(dateFrom)
        matchesDate = matchesDate && customerDate >= fromDate
      }
      if (dateTo) {
        const toDate = new Date(dateTo)
        toDate.setHours(23, 59, 59, 999) // End of day
        matchesDate = matchesDate && customerDate <= toDate
      }
    }

    return matchesSearch && matchesStatus && matchesOrderActivity && matchesLanguage && matchesDate
  })

  // Sort Telegram customers
  const sortedCustomers = [...filteredCustomers].sort((a, b) => {
    let aValue: any, bValue: any

    switch (sortBy) {
      case 'registrationDate':
        aValue = new Date(a.registrationDate)
        bValue = new Date(b.registrationDate)
        break
      case 'lastActivityDate':
        aValue = new Date(a.lastActivityDate)
        bValue = new Date(b.lastActivityDate)
        break
      case 'telegramUsername':
        aValue = a.telegramUsername || ''
        bValue = b.telegramUsername || ''
        break
      case 'totalOrders':
        aValue = a.totalOrders
        bValue = b.totalOrders
        break
      case 'totalSpent':
        aValue = a.totalSpent
        bValue = b.totalSpent
        break
      case 'averageOrderValue':
        aValue = a.averageOrderValue
        bValue = b.averageOrderValue
        break
      default:
        aValue = a.id
        bValue = b.id
    }

    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1
    return 0
  })

  // Utility functions for Telegram customers
  const getStatusColor = (customer: TelegramCustomer) => {
    if (customer.isBlocked) {
      return 'bg-gradient-to-r from-red-500 to-pink-500 text-white'
    } else if (customer.isActive) {
      return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800'
    } else {
      return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800'
    }
  }

  const getStatusIcon = (customer: TelegramCustomer) => {
    if (customer.isBlocked) {
      return <Ban className="h-3 w-3" />
    } else if (customer.isActive) {
      return <CheckCircle className="h-3 w-3" />
    } else {
      return <XCircle className="h-3 w-3" />
    }
  }

  const getStatusText = (customer: TelegramCustomer) => {
    if (customer.isBlocked) return 'Blocked'
    return customer.isActive ? 'Active' : 'Inactive'
  }

  const getLanguageFlag = (languageCode?: string) => {
    switch (languageCode) {
      case 'en': return '🇺🇸'
      case 'es': return '🇪🇸'
      case 'fr': return '🇫🇷'
      case 'de': return '🇩🇪'
      case 'it': return '🇮🇹'
      case 'pt': return '🇵🇹'
      case 'ru': return '🇷🇺'
      case 'zh': return '🇨🇳'
      case 'ja': return '🇯🇵'
      case 'ko': return '🇰🇷'
      default: return '🌐'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getCustomerDisplayName = (customer: TelegramCustomer) => {
    if (customer.telegramFirstName && customer.telegramLastName) {
      return `${customer.telegramFirstName} ${customer.telegramLastName}`
    } else if (customer.telegramFirstName) {
      return customer.telegramFirstName
    } else if (customer.telegramUsername) {
      return `@${customer.telegramUsername}`
    } else {
      return `User ${customer.telegramUserId}`
    }
  }

  const getTimeSinceLastActivity = (lastActivityDate: string) => {
    const now = new Date()
    const lastActivity = new Date(lastActivityDate)
    const diffInHours = Math.floor((now.getTime() - lastActivity.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 30) return `${diffInDays}d ago`
    const diffInMonths = Math.floor(diffInDays / 30)
    return `${diffInMonths}mo ago`
  }

  const totalPages = Math.ceil(sortedCustomers.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedCustomers = sortedCustomers.slice(startIndex, startIndex + itemsPerPage)

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-3">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <span className="text-lg font-medium text-gray-600">Loading customers...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Telegram Bot Customers
          </h1>
          <p className="text-gray-600 mt-2 flex items-center gap-2">
            <Phone className="h-4 w-4" />
            Manage Telegram bot users and their purchase behavior ({sortedCustomers.length} total)
          </p>
        </div>
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <button className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-200 font-medium shadow-lg flex items-center">
            <Download className="w-4 h-4 mr-2" />
            Export Customer Data
          </button>
          <button className="px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-xl hover:from-purple-600 hover:to-pink-700 transition-all duration-200 font-medium shadow-lg flex items-center">
            <Activity className="w-4 h-4 mr-2" />
            Analytics Dashboard
          </button>
        </div>
      </div>

      {/* Advanced Filters */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
          {/* Search */}
          <div className="relative col-span-1 md:col-span-2">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search by Telegram username, name, or user ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
            />
          </div>

          {/* Status Filter */}
          <div className="relative">
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 appearance-none bg-white"
            >
              <option value="All Status">All Status</option>
              <option value="Active">Active</option>
              <option value="Inactive">Inactive</option>
              <option value="Blocked">Blocked</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>

          {/* Order Activity Filter */}
          <div className="relative">
            <select
              value={selectedOrderActivity}
              onChange={(e) => setSelectedOrderActivity(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 appearance-none bg-white"
            >
              <option value="All Activity">All Activity</option>
              <option value="Has Orders">Has Orders</option>
              <option value="No Orders">No Orders</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>

          {/* Language Filter */}
          <div className="relative">
            <select
              value={selectedLanguage}
              onChange={(e) => setSelectedLanguage(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 appearance-none bg-white"
            >
              <option value="All Languages">All Languages</option>
              <option value="en">🇺🇸 English</option>
              <option value="es">🇪🇸 Spanish</option>
              <option value="fr">🇫🇷 French</option>
              <option value="de">🇩🇪 German</option>
              <option value="it">🇮🇹 Italian</option>
              <option value="pt">🇵🇹 Portuguese</option>
              <option value="ru">🇷🇺 Russian</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>

          {/* Registration Date From */}
          <div className="relative">
            <input
              type="date"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              placeholder="Registration From"
            />
          </div>
        </div>

        {/* Second Row for Date To and Clear Filters */}
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4 mt-4">
          <div className="relative">
            <input
              type="date"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              placeholder="To Date"
            />
          </div>
          <div className="md:col-span-5 flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Showing {sortedCustomers.length} of {customers.length} customers
            </div>
            <button
              onClick={clearFilters}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200 flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Customers Table */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
              <tr>
                <th
                  className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors duration-200"
                  onClick={() => handleSort('telegramUsername')}
                >
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    Telegram User
                    {sortBy === 'telegramUsername' && (
                      <ArrowUpDown className="h-3 w-3 text-blue-500" />
                    )}
                  </div>
                </th>
                <th
                  className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors duration-200"
                  onClick={() => handleSort('totalOrders')}
                >
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Purchase History
                    {sortBy === 'totalOrders' && (
                      <ArrowUpDown className="h-3 w-3 text-blue-500" />
                    )}
                  </div>
                </th>
                <th
                  className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors duration-200"
                  onClick={() => handleSort('totalSpent')}
                >
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Spending
                    {sortBy === 'totalSpent' && (
                      <ArrowUpDown className="h-3 w-3 text-blue-500" />
                    )}
                  </div>
                </th>
                <th
                  className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors duration-200"
                  onClick={() => handleSort('lastActivityDate')}
                >
                  <div className="flex items-center gap-2">
                    <Activity className="h-4 w-4" />
                    Last Activity
                    {sortBy === 'lastActivityDate' && (
                      <ArrowUpDown className="h-3 w-3 text-blue-500" />
                    )}
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Status
                  </div>
                </th>
                <th
                  className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors duration-200"
                  onClick={() => handleSort('registrationDate')}
                >
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Joined
                    {sortBy === 'registrationDate' && (
                      <ArrowUpDown className="h-3 w-3 text-blue-500" />
                    )}
                  </div>
                </th>
                <th className="px-6 py-4 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center justify-end gap-2">
                    <MoreVertical className="h-4 w-4" />
                    Actions
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {paginatedCustomers.map((customer) => (
                <tr key={customer.id} className="hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-200">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-12 w-12">
                        <div className="h-12 w-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                          <Phone className="h-6 w-6 text-white" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-semibold text-gray-900">
                          {getCustomerDisplayName(customer)}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center gap-1">
                          <User className="h-3 w-3" />
                          ID: {customer.telegramUserId}
                        </div>
                        {customer.telegramUsername && (
                          <div className="text-xs text-blue-600 flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            @{customer.telegramUsername}
                          </div>
                        )}
                        <div className="text-xs text-gray-400 flex items-center gap-1">
                          {getLanguageFlag(customer.telegramLanguageCode)}
                          {customer.preferredLanguage || customer.telegramLanguageCode || 'Unknown'}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4 text-blue-500" />
                      <div>
                        <div className="text-sm font-semibold text-gray-900">
                          {customer.totalOrders} orders
                        </div>
                        <div className="text-xs text-gray-500">
                          {customer.lastOrderDate ? `Last: ${formatDate(customer.lastOrderDate)}` : 'No orders yet'}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-green-500" />
                      <div>
                        <div className="text-sm font-semibold text-gray-900">
                          ${customer.totalSpent.toFixed(2)}
                        </div>
                        <div className="text-xs text-gray-500">
                          Avg: ${customer.averageOrderValue.toFixed(2)}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <Activity className="h-4 w-4 text-purple-500" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {getTimeSinceLastActivity(customer.lastActivityDate)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {formatDateTime(customer.lastActivityDate)}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(customer)} shadow-sm`}>
                      {getStatusIcon(customer)}
                      <span className="ml-1">{getStatusText(customer)}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {formatDate(customer.registrationDate)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end items-center gap-2">
                      <button
                        onClick={() => {
                          setSelectedCustomer(customer)
                          setShowOrdersModal(true)
                        }}
                        className="p-2 text-purple-600 hover:text-purple-800 hover:bg-purple-50 rounded-lg transition-all duration-200"
                        title="View Order History"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedCustomer(customer)
                          setEditCustomerData({
                            telegramUsername: customer.telegramUsername || '',
                            telegramFirstName: customer.telegramFirstName || '',
                            telegramLastName: customer.telegramLastName || '',
                            isActive: customer.isActive,
                            isBlocked: customer.isBlocked,
                            preferredLanguage: customer.preferredLanguage || ''
                          })
                          setShowEditModal(true)
                        }}
                        className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-all duration-200"
                        title="Edit Customer"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleToggleCustomerStatus(customer.id, customer.isActive)}
                        disabled={updating === customer.id}
                        className={`p-2 rounded-lg transition-all duration-200 ${
                          customer.isActive
                            ? "text-red-600 hover:text-red-800 hover:bg-red-50"
                            : "text-green-600 hover:text-green-800 hover:bg-green-50"
                        }`}
                        title={customer.isActive ? "Deactivate Customer" : "Activate Customer"}
                      >
                        {updating === customer.id ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : customer.isActive ? (
                          <XCircle className="h-4 w-4" />
                        ) : (
                          <CheckCircle className="h-4 w-4" />
                        )}
                      </button>
                      <button
                        onClick={() => handleToggleCustomerBlock(customer.id, customer.isBlocked)}
                        disabled={updating === customer.id}
                        className={`p-2 rounded-lg transition-all duration-200 ${
                          customer.isBlocked
                            ? "text-green-600 hover:text-green-800 hover:bg-green-50"
                            : "text-yellow-600 hover:text-yellow-800 hover:bg-yellow-50"
                        }`}
                        title={customer.isBlocked ? "Unblock Customer" : "Block Customer"}
                      >
                        <Ban className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedCustomer(customer)
                          setShowDeleteModal(true)
                        }}
                        className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-all duration-200"
                        title="Delete Customer"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
        <div className="text-sm text-gray-600">
          Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, sortedCustomers.length)} of {sortedCustomers.length} customers
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-4 py-2 border border-gray-300 rounded-lg text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors duration-200"
          >
            Previous
          </button>
          <div className="flex items-center space-x-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = i + 1
              return (
                <button
                  key={page}
                  onClick={() => setCurrentPage(page)}
                  className={`px-3 py-2 text-sm rounded-lg transition-colors duration-200 ${
                    currentPage === page
                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {page}
                </button>
              )
            })}
          </div>
          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="px-4 py-2 border border-gray-300 rounded-lg text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors duration-200"
          >
            Next
          </button>
        </div>
      </div>

      {/* Edit Customer Modal */}
      {showEditModal && selectedCustomer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="relative mx-auto border w-full max-w-md shadow-2xl rounded-2xl bg-white">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                  <Edit className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">Edit Telegram Customer</h3>
              </div>
              <button
                onClick={() => {
                  setShowEditModal(false)
                  setSelectedCustomer(null)
                }}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              >
                <X className="h-6 w-6 text-gray-500" />
              </button>
            </div>

            <form onSubmit={handleEditCustomer} className="p-6 space-y-5">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Phone className="h-4 w-4 inline mr-1" />
                  Telegram Username
                </label>
                <input
                  type="text"
                  value={editCustomerData.telegramUsername || ''}
                  onChange={(e) => setEditCustomerData({...editCustomerData, telegramUsername: e.target.value})}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  placeholder="@username"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <User className="h-4 w-4 inline mr-1" />
                    First Name
                  </label>
                  <input
                    type="text"
                    value={editCustomerData.telegramFirstName || ''}
                    onChange={(e) => setEditCustomerData({...editCustomerData, telegramFirstName: e.target.value})}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <User className="h-4 w-4 inline mr-1" />
                    Last Name
                  </label>
                  <input
                    type="text"
                    value={editCustomerData.telegramLastName || ''}
                    onChange={(e) => setEditCustomerData({...editCustomerData, telegramLastName: e.target.value})}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Activity className="h-4 w-4 inline mr-1" />
                  Preferred Language
                </label>
                <select
                  value={editCustomerData.preferredLanguage || ''}
                  onChange={(e) => setEditCustomerData({...editCustomerData, preferredLanguage: e.target.value})}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                >
                  <option value="">Select Language</option>
                  <option value="en">🇺🇸 English</option>
                  <option value="es">🇪🇸 Spanish</option>
                  <option value="fr">🇫🇷 French</option>
                  <option value="de">🇩🇪 German</option>
                  <option value="it">🇮🇹 Italian</option>
                  <option value="pt">🇵🇹 Portuguese</option>
                  <option value="ru">🇷🇺 Russian</option>
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={editCustomerData.isActive}
                    onChange={(e) => setEditCustomerData({...editCustomerData, isActive: e.target.checked})}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                    Active Customer
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isBlocked"
                    checked={editCustomerData.isBlocked}
                    onChange={(e) => setEditCustomerData({...editCustomerData, isBlocked: e.target.checked})}
                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isBlocked" className="ml-2 block text-sm text-gray-900">
                    Blocked Customer
                  </label>
                </div>
              </div>

              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => {
                    setShowEditModal(false)
                    setSelectedCustomer(null)
                  }}
                  className="px-6 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={updating === selectedCustomer?.id}
                  className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {updating === selectedCustomer?.id ? (
                    <>
                      <Loader2 className="h-4 w-4 inline mr-2 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 inline mr-2" />
                      Update Customer
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Customer Modal */}
      {showDeleteModal && selectedCustomer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="relative mx-auto border w-full max-w-md shadow-2xl rounded-2xl bg-white">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-red-500 to-pink-600 rounded-lg">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">Delete Telegram Customer</h3>
              </div>
              <button
                onClick={() => {
                  setShowDeleteModal(false)
                  setSelectedCustomer(null)
                }}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              >
                <X className="h-6 w-6 text-gray-500" />
              </button>
            </div>

            <div className="p-6">
              <p className="text-gray-600 mb-4">
                Are you sure you want to permanently delete this Telegram customer? This action cannot be undone and will remove all associated order history and data.
              </p>
              <div className="bg-gray-50 p-4 rounded-xl mb-6">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                    <Phone className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">
                      {getCustomerDisplayName(selectedCustomer)}
                    </div>
                    <div className="text-sm text-gray-500">ID: {selectedCustomer.telegramUserId}</div>
                    {selectedCustomer.telegramUsername && (
                      <div className="text-xs text-blue-600">@{selectedCustomer.telegramUsername}</div>
                    )}
                    <div className="text-xs text-gray-400">
                      {selectedCustomer.totalOrders} orders • ${selectedCustomer.totalSpent.toFixed(2)} spent
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowDeleteModal(false)
                    setSelectedCustomer(null)
                  }}
                  className="px-6 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteCustomer}
                  disabled={updating === selectedCustomer?.id}
                  className="px-6 py-3 bg-gradient-to-r from-red-500 to-pink-600 text-white rounded-xl hover:from-red-600 hover:to-pink-700 transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {updating === selectedCustomer?.id ? (
                    <>
                      <Loader2 className="h-4 w-4 inline mr-2 animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Trash2 className="h-4 w-4 inline mr-2" />
                      Delete Customer
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Order History Modal */}
      {showOrdersModal && selectedCustomer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="relative mx-auto border w-full max-w-4xl shadow-2xl rounded-2xl bg-white">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg">
                  <Package className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">Order History</h3>
                  <p className="text-sm text-gray-600">{getCustomerDisplayName(selectedCustomer)}</p>
                </div>
              </div>
              <button
                onClick={() => {
                  setShowOrdersModal(false)
                  setSelectedCustomer(null)
                }}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              >
                <X className="h-6 w-6 text-gray-500" />
              </button>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div className="bg-blue-50 p-4 rounded-xl">
                  <div className="flex items-center gap-3">
                    <Package className="h-8 w-8 text-blue-600" />
                    <div>
                      <div className="text-2xl font-bold text-blue-900">{selectedCustomer.totalOrders}</div>
                      <div className="text-sm text-blue-600">Total Orders</div>
                    </div>
                  </div>
                </div>
                <div className="bg-green-50 p-4 rounded-xl">
                  <div className="flex items-center gap-3">
                    <DollarSign className="h-8 w-8 text-green-600" />
                    <div>
                      <div className="text-2xl font-bold text-green-900">${selectedCustomer.totalSpent.toFixed(2)}</div>
                      <div className="text-sm text-green-600">Total Spent</div>
                    </div>
                  </div>
                </div>
                <div className="bg-purple-50 p-4 rounded-xl">
                  <div className="flex items-center gap-3">
                    <Activity className="h-8 w-8 text-purple-600" />
                    <div>
                      <div className="text-2xl font-bold text-purple-900">${selectedCustomer.averageOrderValue.toFixed(2)}</div>
                      <div className="text-sm text-purple-600">Average Order</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="text-center py-8 text-gray-500">
                <Package className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p className="text-lg font-medium">Order History Coming Soon</p>
                <p className="text-sm">Detailed order history will be displayed here when integrated with the orders API.</p>
              </div>

              <div className="flex justify-end pt-6 border-t border-gray-200">
                <button
                  onClick={() => {
                    setShowOrdersModal(false)
                    setSelectedCustomer(null)
                  }}
                  className="px-6 py-3 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl hover:from-gray-600 hover:to-gray-700 transition-all duration-200 font-medium"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default TeleShopCustomers
