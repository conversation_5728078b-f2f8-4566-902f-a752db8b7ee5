import React, { useState, useEffect } from 'react'
import {
  Search,
  Download,
  Plus,
  MoreVertical,
  UserPlus,
  User,
  Mail,
  Calendar,
  Shield,
  CheckCircle,
  XCircle,
  Ban,
  Edit,
  Trash2,
  Eye,
  ArrowUpDown,
  ChevronDown,
  X,
  AlertTriangle,
  Loader2,
  Package,
  DollarSign,
  Clock,
  Phone,
  Store as StoreIcon,
  Users,
  Activity
} from 'lucide-react'
import { apiService } from '../services/api'
import { toast } from 'react-hot-toast'
import { useAuth } from '../contexts/AuthContext'

// Customer interfaces matching backend schema
interface Customer {
  id: string
  email: string
  username?: string
  telegramId?: string
  role: UserRole
  isActive: boolean
  isBanned: boolean
  storeId?: string
  createdAt: string
  updatedAt: string
  ownedStore?: {
    id: string
    name: string
    description?: string
    isActive: boolean
  }
  managedStores?: Array<{
    store: {
      id: string
      name: string
      description?: string
    }
  }>
  _count?: {
    orders: number
    downloads: number
  }
}

enum UserRole {
  ADMIN = 'ADMIN',
  SELLER = 'SELLER',
  MANAGER = 'MANAGER',
  CUSTOMER = 'CUSTOMER'
}

interface EditCustomerData {
  email: string
  username?: string
  role: UserRole
  isActive: boolean
  isBanned: boolean
  storeId?: string
}

const TeleShopCustomers: React.FC = () => {
  const { user } = useAuth()
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState<string | null>(null)

  // Filter states
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<string>('All Status')
  const [selectedRole, setSelectedRole] = useState<string>('All Roles')
  const [selectedOrderActivity, setSelectedOrderActivity] = useState<string>('All Activity')
  const [dateFrom, setDateFrom] = useState('')
  const [dateTo, setDateTo] = useState('')

  // Table states
  const [currentPage, setCurrentPage] = useState(1)
  const [sortBy, setSortBy] = useState<string>('createdAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const itemsPerPage = 10

  // Modal states
  const [showEditModal, setShowEditModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [editCustomerData, setEditCustomerData] = useState<EditCustomerData>({
    email: '',
    username: '',
    role: UserRole.CUSTOMER,
    isActive: true,
    isBanned: false,
    storeId: ''
  })

  useEffect(() => {
    fetchCustomers()
  }, [])

  const fetchCustomers = async () => {
    try {
      setLoading(true)

      // Try to fetch from API first, fallback to mock data
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/admin/users?limit=100`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })

        if (response.ok) {
          const data = await response.json()
          setCustomers(data.data.users || [])
        } else {
          throw new Error('API request failed')
        }
      } catch (apiError) {
        console.log('Using mock data due to API error:', apiError)

        // Mock data fallback with proper structure
        setCustomers([
          {
            id: 'user_1',
            email: '<EMAIL>',
            username: 'johndoe',
            telegramId: '@johndoe',
            role: UserRole.CUSTOMER,
            isActive: true,
            isBanned: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            _count: { orders: 5, downloads: 12 }
          },
          {
            id: 'user_2',
            email: '<EMAIL>',
            username: 'janesmith',
            role: UserRole.CUSTOMER,
            isActive: true,
            isBanned: false,
            createdAt: new Date(Date.now() - 86400000).toISOString(),
            updatedAt: new Date(Date.now() - 86400000).toISOString(),
            _count: { orders: 3, downloads: 8 }
          },
          {
            id: 'user_3',
            email: '<EMAIL>',
            username: 'bobwilson',
            role: UserRole.SELLER,
            isActive: true,
            isBanned: false,
            storeId: 'store_1',
            createdAt: new Date(Date.now() - 172800000).toISOString(),
            updatedAt: new Date(Date.now() - 172800000).toISOString(),
            ownedStore: { id: 'store_1', name: "Bob's Digital Store", isActive: true },
            _count: { orders: 8, downloads: 25 }
          },
          {
            id: 'user_4',
            email: '<EMAIL>',
            username: 'alicebrown',
            role: UserRole.MANAGER,
            isActive: false,
            isBanned: false,
            createdAt: new Date(Date.now() - 259200000).toISOString(),
            updatedAt: new Date(Date.now() - 259200000).toISOString(),
            _count: { orders: 2, downloads: 5 }
          },
          {
            id: 'user_5',
            email: '<EMAIL>',
            username: 'mikejones',
            role: UserRole.CUSTOMER,
            isActive: true,
            isBanned: true,
            createdAt: new Date(Date.now() - 345600000).toISOString(),
            updatedAt: new Date(Date.now() - 345600000).toISOString(),
            _count: { orders: 0, downloads: 0 }
          }
        ])
      }

      setLoading(false)
    } catch (error) {
      console.error('Error fetching customers:', error)
      toast.error('Failed to fetch customers')
      setLoading(false)
    }
  }

  // Customer management functions
  const handleEditCustomer = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedCustomer) return

    setUpdating(selectedCustomer.id)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/admin/users/${selectedCustomer.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(editCustomerData)
      })

      if (response.ok) {
        toast.success('Customer updated successfully!')
        setShowEditModal(false)
        setSelectedCustomer(null)
        fetchCustomers()
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || 'Failed to update customer')
      }
    } catch (error) {
      console.error('Error updating customer:', error)
      toast.error('Error updating customer')
    } finally {
      setUpdating(null)
    }
  }

  const handleToggleCustomerStatus = async (customerId: string, isActive: boolean) => {
    setUpdating(customerId)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/admin/users/${customerId}/activate`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ isActive: !isActive })
      })

      if (response.ok) {
        toast.success(`Customer ${!isActive ? 'activated' : 'deactivated'} successfully!`)
        fetchCustomers()
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || 'Failed to update customer status')
      }
    } catch (error) {
      console.error('Error updating customer status:', error)
      toast.error('Error updating customer status')
    } finally {
      setUpdating(null)
    }
  }

  const handleToggleCustomerBan = async (customerId: string, isBanned: boolean) => {
    setUpdating(customerId)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/admin/users/${customerId}/ban`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ isBanned: !isBanned })
      })

      if (response.ok) {
        toast.success(`Customer ${!isBanned ? 'banned' : 'unbanned'} successfully!`)
        fetchCustomers()
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || 'Failed to update customer ban status')
      }
    } catch (error) {
      console.error('Error updating customer ban status:', error)
      toast.error('Error updating customer ban status')
    } finally {
      setUpdating(null)
    }
  }

  const handleDeleteCustomer = async () => {
    if (!selectedCustomer) return

    setUpdating(selectedCustomer.id)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'}/admin/users/${selectedCustomer.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (response.ok) {
        toast.success('Customer deleted successfully!')
        setShowDeleteModal(false)
        setSelectedCustomer(null)
        fetchCustomers()
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || 'Failed to delete customer')
      }
    } catch (error) {
      console.error('Error deleting customer:', error)
      toast.error('Error deleting customer')
    } finally {
      setUpdating(null)
    }
  }

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('')
    setSelectedStatus('All Status')
    setSelectedRole('All Roles')
    setSelectedOrderActivity('All Activity')
    setDateFrom('')
    setDateTo('')
    setCurrentPage(1)
  }

  // Handle sorting
  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(column)
      setSortOrder('asc')
    }
  }

  // Filtering and sorting logic
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (customer.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (customer.username || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (customer.telegramId || '').toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = selectedStatus === 'All Status' ||
      (selectedStatus === 'Active' && customer.isActive && !customer.isBanned) ||
      (selectedStatus === 'Inactive' && !customer.isActive) ||
      (selectedStatus === 'Banned' && customer.isBanned)

    const matchesRole = selectedRole === 'All Roles' || customer.role === selectedRole

    const matchesOrderActivity = selectedOrderActivity === 'All Activity' ||
      (selectedOrderActivity === 'Has Orders' && (customer._count?.orders || 0) > 0) ||
      (selectedOrderActivity === 'No Orders' && (customer._count?.orders || 0) === 0)

    // Date filtering
    let matchesDate = true
    if (dateFrom || dateTo) {
      const customerDate = new Date(customer.createdAt)
      if (dateFrom) {
        const fromDate = new Date(dateFrom)
        matchesDate = matchesDate && customerDate >= fromDate
      }
      if (dateTo) {
        const toDate = new Date(dateTo)
        toDate.setHours(23, 59, 59, 999) // End of day
        matchesDate = matchesDate && customerDate <= toDate
      }
    }

    return matchesSearch && matchesStatus && matchesRole && matchesOrderActivity && matchesDate
  })

  // Sort customers
  const sortedCustomers = [...filteredCustomers].sort((a, b) => {
    let aValue: any, bValue: any

    switch (sortBy) {
      case 'createdAt':
        aValue = new Date(a.createdAt)
        bValue = new Date(b.createdAt)
        break
      case 'email':
        aValue = a.email || ''
        bValue = b.email || ''
        break
      case 'username':
        aValue = a.username || ''
        bValue = b.username || ''
        break
      case 'role':
        aValue = a.role
        bValue = b.role
        break
      case 'orders':
        aValue = a._count?.orders || 0
        bValue = b._count?.orders || 0
        break
      default:
        aValue = a.id
        bValue = b.id
    }

    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1
    return 0
  })

  // Utility functions
  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800'
      case UserRole.SELLER:
        return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800'
      case UserRole.MANAGER:
        return 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800'
      case UserRole.CUSTOMER:
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return <Shield className="h-3 w-3" />
      case UserRole.SELLER:
        return <StoreIcon className="h-3 w-3" />
      case UserRole.MANAGER:
        return <Users className="h-3 w-3" />
      case UserRole.CUSTOMER:
        return <User className="h-3 w-3" />
      default:
        return <User className="h-3 w-3" />
    }
  }

  const getStatusColor = (customer: Customer) => {
    if (customer.isBanned) {
      return 'bg-gradient-to-r from-red-500 to-pink-500 text-white'
    } else if (customer.isActive) {
      return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800'
    } else {
      return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800'
    }
  }

  const getStatusIcon = (customer: Customer) => {
    if (customer.isBanned) {
      return <Ban className="h-3 w-3" />
    } else if (customer.isActive) {
      return <CheckCircle className="h-3 w-3" />
    } else {
      return <XCircle className="h-3 w-3" />
    }
  }

  const getStatusText = (customer: Customer) => {
    if (customer.isBanned) return 'Banned'
    return customer.isActive ? 'Active' : 'Inactive'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const totalPages = Math.ceil(sortedCustomers.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedCustomers = sortedCustomers.slice(startIndex, startIndex + itemsPerPage)

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-3">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <span className="text-lg font-medium text-gray-600">Loading customers...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Customer Management
          </h1>
          <p className="text-gray-600 mt-2 flex items-center gap-2">
            <Users className="h-4 w-4" />
            Manage customer accounts and information ({sortedCustomers.length} total)
          </p>
        </div>
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <button className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-200 font-medium shadow-lg flex items-center">
            <Download className="w-4 h-4 mr-2" />
            Export Customers
          </button>
          <button className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg flex items-center">
            <UserPlus className="w-4 h-4 mr-2" />
            Add Customer
          </button>
        </div>
      </div>

      {/* Advanced Filters */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
          {/* Search */}
          <div className="relative col-span-1 md:col-span-2">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search customers, email, username, ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
            />
          </div>

          {/* Status Filter */}
          <div className="relative">
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 appearance-none bg-white"
            >
              <option value="All Status">All Status</option>
              <option value="Active">Active</option>
              <option value="Inactive">Inactive</option>
              <option value="Banned">Banned</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>

          {/* Role Filter */}
          <div className="relative">
            <select
              value={selectedRole}
              onChange={(e) => setSelectedRole(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 appearance-none bg-white"
            >
              <option value="All Roles">All Roles</option>
              <option value={UserRole.ADMIN}>Admin</option>
              <option value={UserRole.SELLER}>Seller</option>
              <option value={UserRole.MANAGER}>Manager</option>
              <option value={UserRole.CUSTOMER}>Customer</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>

          {/* Order Activity Filter */}
          <div className="relative">
            <select
              value={selectedOrderActivity}
              onChange={(e) => setSelectedOrderActivity(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 appearance-none bg-white"
            >
              <option value="All Activity">All Activity</option>
              <option value="Has Orders">Has Orders</option>
              <option value="No Orders">No Orders</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>

          {/* Date From */}
          <div className="relative">
            <input
              type="date"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              placeholder="From Date"
            />
          </div>
        </div>

        {/* Second Row for Date To and Clear Filters */}
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4 mt-4">
          <div className="relative">
            <input
              type="date"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              placeholder="To Date"
            />
          </div>
          <div className="md:col-span-5 flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Showing {sortedCustomers.length} of {customers.length} customers
            </div>
            <button
              onClick={clearFilters}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200 flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Customers Table */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
              <tr>
                <th
                  className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors duration-200"
                  onClick={() => handleSort('email')}
                >
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Customer
                    {sortBy === 'email' && (
                      <ArrowUpDown className="h-3 w-3 text-blue-500" />
                    )}
                  </div>
                </th>
                <th
                  className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors duration-200"
                  onClick={() => handleSort('role')}
                >
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Role
                    {sortBy === 'role' && (
                      <ArrowUpDown className="h-3 w-3 text-blue-500" />
                    )}
                  </div>
                </th>
                <th
                  className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors duration-200"
                  onClick={() => handleSort('orders')}
                >
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Orders
                    {sortBy === 'orders' && (
                      <ArrowUpDown className="h-3 w-3 text-blue-500" />
                    )}
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center gap-2">
                    <Activity className="h-4 w-4" />
                    Activity
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Status
                  </div>
                </th>
                <th
                  className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors duration-200"
                  onClick={() => handleSort('createdAt')}
                >
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Joined
                    {sortBy === 'createdAt' && (
                      <ArrowUpDown className="h-3 w-3 text-blue-500" />
                    )}
                  </div>
                </th>
                <th className="px-6 py-4 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center justify-end gap-2">
                    <MoreVertical className="h-4 w-4" />
                    Actions
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {paginatedCustomers.map((customer) => (
                <tr key={customer.id} className="hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-200">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-12 w-12">
                        <div className="h-12 w-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                          <User className="h-6 w-6 text-white" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-semibold text-gray-900">
                          {customer.username || customer.email.split('@')[0]}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center gap-1">
                          <Mail className="h-3 w-3" />
                          {customer.email}
                        </div>
                        {customer.telegramId && (
                          <div className="text-xs text-blue-600 flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            {customer.telegramId}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${getRoleColor(customer.role)} shadow-sm`}>
                      {getRoleIcon(customer.role)}
                      <span className="ml-1">{customer.role}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4 text-blue-500" />
                      <div>
                        <div className="text-sm font-semibold text-gray-900">
                          {customer._count?.orders || 0} orders
                        </div>
                        <div className="text-xs text-gray-500">
                          {customer._count?.downloads || 0} downloads
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      {customer.ownedStore ? (
                        <>
                          <StoreIcon className="h-4 w-4 text-green-500" />
                          <div>
                            <div className="text-sm font-medium text-gray-900">Store Owner</div>
                            <div className="text-xs text-gray-500">{customer.ownedStore.name}</div>
                          </div>
                        </>
                      ) : customer.managedStores && customer.managedStores.length > 0 ? (
                        <>
                          <Users className="h-4 w-4 text-blue-500" />
                          <div>
                            <div className="text-sm font-medium text-gray-900">Manager</div>
                            <div className="text-xs text-gray-500">{customer.managedStores.length} store(s)</div>
                          </div>
                        </>
                      ) : (
                        <>
                          <Activity className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-500">Regular customer</span>
                        </>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(customer)} shadow-sm`}>
                      {getStatusIcon(customer)}
                      <span className="ml-1">{getStatusText(customer)}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {formatDate(customer.createdAt)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end items-center gap-2">
                      <button
                        onClick={() => {
                          setSelectedCustomer(customer)
                          setEditCustomerData({
                            email: customer.email,
                            username: customer.username || '',
                            role: customer.role,
                            isActive: customer.isActive,
                            isBanned: customer.isBanned,
                            storeId: customer.storeId || ''
                          })
                          setShowEditModal(true)
                        }}
                        className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-all duration-200"
                        title="Edit Customer"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleToggleCustomerStatus(customer.id, customer.isActive)}
                        disabled={updating === customer.id}
                        className={`p-2 rounded-lg transition-all duration-200 ${
                          customer.isActive
                            ? "text-red-600 hover:text-red-800 hover:bg-red-50"
                            : "text-green-600 hover:text-green-800 hover:bg-green-50"
                        }`}
                        title={customer.isActive ? "Deactivate Customer" : "Activate Customer"}
                      >
                        {updating === customer.id ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : customer.isActive ? (
                          <XCircle className="h-4 w-4" />
                        ) : (
                          <CheckCircle className="h-4 w-4" />
                        )}
                      </button>
                      <button
                        onClick={() => handleToggleCustomerBan(customer.id, customer.isBanned)}
                        disabled={updating === customer.id}
                        className={`p-2 rounded-lg transition-all duration-200 ${
                          customer.isBanned
                            ? "text-green-600 hover:text-green-800 hover:bg-green-50"
                            : "text-yellow-600 hover:text-yellow-800 hover:bg-yellow-50"
                        }`}
                        title={customer.isBanned ? "Unban Customer" : "Ban Customer"}
                      >
                        <Ban className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedCustomer(customer)
                          setShowDeleteModal(true)
                        }}
                        className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-all duration-200"
                        title="Delete Customer"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
        <div className="text-sm text-gray-600">
          Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, sortedCustomers.length)} of {sortedCustomers.length} customers
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-4 py-2 border border-gray-300 rounded-lg text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors duration-200"
          >
            Previous
          </button>
          <div className="flex items-center space-x-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = i + 1
              return (
                <button
                  key={page}
                  onClick={() => setCurrentPage(page)}
                  className={`px-3 py-2 text-sm rounded-lg transition-colors duration-200 ${
                    currentPage === page
                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {page}
                </button>
              )
            })}
          </div>
          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="px-4 py-2 border border-gray-300 rounded-lg text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors duration-200"
          >
            Next
          </button>
        </div>
      </div>

      {/* Edit Customer Modal */}
      {showEditModal && selectedCustomer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="relative mx-auto border w-full max-w-md shadow-2xl rounded-2xl bg-white">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                  <Edit className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">Edit Customer</h3>
              </div>
              <button
                onClick={() => {
                  setShowEditModal(false)
                  setSelectedCustomer(null)
                }}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              >
                <X className="h-6 w-6 text-gray-500" />
              </button>
            </div>

            <form onSubmit={handleEditCustomer} className="p-6 space-y-5">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Mail className="h-4 w-4 inline mr-1" />
                  Email Address
                </label>
                <input
                  type="email"
                  required
                  value={editCustomerData.email}
                  onChange={(e) => setEditCustomerData({...editCustomerData, email: e.target.value})}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <User className="h-4 w-4 inline mr-1" />
                  Username
                </label>
                <input
                  type="text"
                  value={editCustomerData.username || ''}
                  onChange={(e) => setEditCustomerData({...editCustomerData, username: e.target.value})}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Shield className="h-4 w-4 inline mr-1" />
                  Role
                </label>
                <select
                  value={editCustomerData.role}
                  onChange={(e) => setEditCustomerData({...editCustomerData, role: e.target.value as UserRole})}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                >
                  <option value={UserRole.CUSTOMER}>Customer</option>
                  <option value={UserRole.SELLER}>Seller</option>
                  <option value={UserRole.MANAGER}>Manager</option>
                  <option value={UserRole.ADMIN}>Admin</option>
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={editCustomerData.isActive}
                    onChange={(e) => setEditCustomerData({...editCustomerData, isActive: e.target.checked})}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                    Active Customer
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isBanned"
                    checked={editCustomerData.isBanned}
                    onChange={(e) => setEditCustomerData({...editCustomerData, isBanned: e.target.checked})}
                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isBanned" className="ml-2 block text-sm text-gray-900">
                    Banned Customer
                  </label>
                </div>
              </div>

              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => {
                    setShowEditModal(false)
                    setSelectedCustomer(null)
                  }}
                  className="px-6 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={updating === selectedCustomer?.id}
                  className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {updating === selectedCustomer?.id ? (
                    <>
                      <Loader2 className="h-4 w-4 inline mr-2 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 inline mr-2" />
                      Update Customer
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Customer Modal */}
      {showDeleteModal && selectedCustomer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="relative mx-auto border w-full max-w-md shadow-2xl rounded-2xl bg-white">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-red-500 to-pink-600 rounded-lg">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">Delete Customer</h3>
              </div>
              <button
                onClick={() => {
                  setShowDeleteModal(false)
                  setSelectedCustomer(null)
                }}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              >
                <X className="h-6 w-6 text-gray-500" />
              </button>
            </div>

            <div className="p-6">
              <p className="text-gray-600 mb-4">
                Are you sure you want to permanently delete this customer? This action cannot be undone and will remove all associated data.
              </p>
              <div className="bg-gray-50 p-4 rounded-xl mb-6">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                    <User className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">
                      {selectedCustomer.username || selectedCustomer.email.split('@')[0]}
                    </div>
                    <div className="text-sm text-gray-500">{selectedCustomer.email}</div>
                    <div className="text-xs text-blue-600">{selectedCustomer.role}</div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowDeleteModal(false)
                    setSelectedCustomer(null)
                  }}
                  className="px-6 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteCustomer}
                  disabled={updating === selectedCustomer?.id}
                  className="px-6 py-3 bg-gradient-to-r from-red-500 to-pink-600 text-white rounded-xl hover:from-red-600 hover:to-pink-700 transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {updating === selectedCustomer?.id ? (
                    <>
                      <Loader2 className="h-4 w-4 inline mr-2 animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Trash2 className="h-4 w-4 inline mr-2" />
                      Delete Customer
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default TeleShopCustomers
