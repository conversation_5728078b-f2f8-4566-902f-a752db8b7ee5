import React, { useState } from 'react'
import {
  Power,
  PowerOff,
  Alert<PERSON>riangle,
  CheckCircle,
  XCircle,
  Clock,
  Users,
  MessageSquare,
  Shield,
  Loader2,
  Bot,
  Zap,
  Activity,
  Pause,
  Play,
  StopCircle,
  RefreshCw,
  Bell,
  Info,
  Wifi,
  WifiOff
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface BotStatusManagerProps {
  botId: string
  botName: string
  currentStatus: 'ACTIVE' | 'INACTIVE' | 'ERROR' | 'SETUP'
  isActive: boolean
  webhookStatus: 'CONNECTED' | 'DISCONNECTED' | 'ERROR'
  onStatusChange: (newStatus: boolean) => void
  onClose: () => void
}

interface StatusTransition {
  from: string
  to: string
  action: string
  description: string
  warnings: string[]
  estimatedTime: string
}

const BotStatusManager: React.FC<BotStatusManagerProps> = ({
  botId,
  botName,
  currentStatus,
  isActive,
  webhookStatus,
  onStatusChange,
  onClose
}) => {
  const [processing, setProcessing] = useState(false)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [pendingAction, setPendingAction] = useState<'activate' | 'deactivate' | null>(null)
  const [notifyUsers, setNotifyUsers] = useState(true)
  const [gracefulShutdown, setGracefulShutdown] = useState(true)
  const [preserveData, setPreserveData] = useState(true)

  const getStatusTransition = (): StatusTransition => {
    if (isActive) {
      return {
        from: 'Active',
        to: 'Inactive',
        action: 'Deactivate Bot',
        description: 'This will stop the bot from responding to new messages and disable all functionality.',
        warnings: [
          'Bot will stop responding to customer messages',
          'New orders cannot be placed through the bot',
          'Existing customer sessions will be terminated',
          'Webhook will be disconnected'
        ],
        estimatedTime: '30-60 seconds'
      }
    } else {
      return {
        from: 'Inactive',
        to: 'Active',
        action: 'Activate Bot',
        description: 'This will start the bot and make it available to customers.',
        warnings: [
          'Bot will begin responding to messages',
          'Webhook will be re-established',
          'All features will be restored',
          'Customers will be notified of availability'
        ],
        estimatedTime: '60-120 seconds'
      }
    }
  }

  const handleStatusToggle = async () => {
    const action = isActive ? 'deactivate' : 'activate'
    setPendingAction(action)
    setShowConfirmation(true)
  }

  const confirmStatusChange = async () => {
    if (!pendingAction) return

    setProcessing(true)
    setShowConfirmation(false)

    try {
      // Simulate status change process
      const steps = [
        'Validating bot configuration...',
        isActive ? 'Notifying active users...' : 'Establishing webhook connection...',
        isActive ? 'Gracefully shutting down bot...' : 'Starting bot services...',
        isActive ? 'Disconnecting webhook...' : 'Verifying bot functionality...',
        'Updating bot status...',
        'Process completed successfully!'
      ]

      for (let i = 0; i < steps.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 1000))
        toast.loading(steps[i], { id: 'status-change' })
      }

      toast.success(
        `Bot ${pendingAction === 'activate' ? 'activated' : 'deactivated'} successfully!`,
        { id: 'status-change' }
      )

      onStatusChange(!isActive)
      onClose()
    } catch (error) {
      console.error('Error changing bot status:', error)
      toast.error(`Failed to ${pendingAction} bot`, { id: 'status-change' })
    } finally {
      setProcessing(false)
      setPendingAction(null)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'text-green-600 bg-green-100'
      case 'INACTIVE':
        return 'text-gray-600 bg-gray-100'
      case 'ERROR':
        return 'text-red-600 bg-red-100'
      case 'SETUP':
        return 'text-yellow-600 bg-yellow-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <CheckCircle className="h-4 w-4" />
      case 'INACTIVE':
        return <XCircle className="h-4 w-4" />
      case 'ERROR':
        return <AlertTriangle className="h-4 w-4" />
      case 'SETUP':
        return <Clock className="h-4 w-4" />
      default:
        return <XCircle className="h-4 w-4" />
    }
  }

  const getWebhookStatusColor = (status: string) => {
    switch (status) {
      case 'CONNECTED':
        return 'text-green-600'
      case 'DISCONNECTED':
        return 'text-gray-600'
      case 'ERROR':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getWebhookStatusIcon = (status: string) => {
    switch (status) {
      case 'CONNECTED':
        return <Wifi className="h-4 w-4" />
      case 'DISCONNECTED':
        return <WifiOff className="h-4 w-4" />
      case 'ERROR':
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <WifiOff className="h-4 w-4" />
    }
  }

  const transition = getStatusTransition()

  if (processing) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
        <div className="relative mx-auto border w-full max-w-md shadow-2xl rounded-2xl bg-white">
          <div className="p-8 text-center">
            <div className="mb-6">
              <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
              </div>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">
              {pendingAction === 'activate' ? 'Activating' : 'Deactivating'} Bot
            </h3>
            <p className="text-gray-600 mb-4">
              Please wait while we {pendingAction} your bot. This may take a few moments.
            </p>
            <div className="bg-gray-50 p-4 rounded-xl">
              <div className="flex items-center gap-3">
                <Bot className="w-5 h-5 text-gray-600" />
                <div className="text-left">
                  <div className="font-semibold text-gray-900">{botName}</div>
                  <div className="text-sm text-gray-500">Processing status change...</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (showConfirmation) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
        <div className="relative mx-auto border w-full max-w-2xl shadow-2xl rounded-2xl bg-white">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${isActive ? 'bg-red-100' : 'bg-green-100'}`}>
                {isActive ? (
                  <PowerOff className="h-6 w-6 text-red-600" />
                ) : (
                  <Power className="h-6 w-6 text-green-600" />
                )}
              </div>
              <h3 className="text-xl font-bold text-gray-900">{transition.action}</h3>
            </div>
            <button
              onClick={() => {
                setShowConfirmation(false)
                setPendingAction(null)
              }}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
            >
              <XCircle className="h-6 w-6 text-gray-500" />
            </button>
          </div>

          <div className="p-6">
            <div className="mb-6">
              <p className="text-gray-600 mb-4">{transition.description}</p>
              
              <div className="bg-gray-50 p-4 rounded-xl mb-4">
                <div className="flex items-center gap-3 mb-3">
                  <Bot className="w-5 h-5 text-gray-600" />
                  <div>
                    <div className="font-semibold text-gray-900">{botName}</div>
                    <div className="text-sm text-gray-500">
                      Status: {transition.from} → {transition.to}
                    </div>
                  </div>
                </div>
                <div className="text-sm text-gray-600">
                  Estimated time: {transition.estimatedTime}
                </div>
              </div>

              <div className={`p-4 rounded-xl ${isActive ? 'bg-red-50' : 'bg-green-50'}`}>
                <h4 className={`font-semibold mb-2 flex items-center gap-2 ${isActive ? 'text-red-900' : 'text-green-900'}`}>
                  <Info className="w-4 h-4" />
                  What will happen:
                </h4>
                <ul className={`text-sm space-y-1 ${isActive ? 'text-red-800' : 'text-green-800'}`}>
                  {transition.warnings.map((warning, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-xs mt-1">•</span>
                      <span>{warning}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {isActive && (
              <div className="space-y-4 mb-6">
                <div className="flex items-center justify-between p-4 border border-gray-200 rounded-xl">
                  <div className="flex items-center gap-3">
                    <Bell className="w-5 h-5 text-blue-600" />
                    <div>
                      <div className="font-semibold text-gray-900">Notify Active Users</div>
                      <div className="text-sm text-gray-600">Send notification to users before shutdown</div>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={notifyUsers}
                      onChange={(e) => setNotifyUsers(e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between p-4 border border-gray-200 rounded-xl">
                  <div className="flex items-center gap-3">
                    <Pause className="w-5 h-5 text-orange-600" />
                    <div>
                      <div className="font-semibold text-gray-900">Graceful Shutdown</div>
                      <div className="text-sm text-gray-600">Allow current operations to complete</div>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={gracefulShutdown}
                      onChange={(e) => setGracefulShutdown(e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              </div>
            )}

            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-xl mb-6">
              <div className="flex items-center gap-3">
                <Shield className="w-5 h-5 text-purple-600" />
                <div>
                  <div className="font-semibold text-gray-900">Preserve Data</div>
                  <div className="text-sm text-gray-600">Keep customer data and order history</div>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={preserveData}
                  onChange={(e) => setPreserveData(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowConfirmation(false)
                  setPendingAction(null)
                }}
                className="px-6 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium"
              >
                Cancel
              </button>
              <button
                onClick={confirmStatusChange}
                className={`px-6 py-3 rounded-xl text-white transition-all duration-200 font-medium shadow-lg flex items-center gap-2 ${
                  isActive
                    ? 'bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700'
                    : 'bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700'
                }`}
              >
                {isActive ? (
                  <>
                    <PowerOff className="w-4 h-4" />
                    Deactivate Bot
                  </>
                ) : (
                  <>
                    <Power className="w-4 h-4" />
                    Activate Bot
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
      <div className="relative mx-auto border w-full max-w-lg shadow-2xl rounded-2xl bg-white">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Activity className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900">Bot Status Management</h3>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          >
            <XCircle className="h-6 w-6 text-gray-500" />
          </button>
        </div>

        <div className="p-6">
          <div className="bg-gray-50 p-4 rounded-xl mb-6">
            <div className="flex items-center gap-3 mb-4">
              <Bot className="w-6 h-6 text-gray-600" />
              <div>
                <div className="font-semibold text-gray-900">{botName}</div>
                <div className="text-sm text-gray-500">Bot ID: {botId}</div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <span className="text-gray-600">Status:</span>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold ${getStatusColor(currentStatus)}`}>
                  {getStatusIcon(currentStatus)}
                  <span className="ml-1">{currentStatus}</span>
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-600">Webhook:</span>
                <span className={`flex items-center gap-1 ${getWebhookStatusColor(webhookStatus)}`}>
                  {getWebhookStatusIcon(webhookStatus)}
                  <span className="text-xs">{webhookStatus}</span>
                </span>
              </div>
            </div>
          </div>

          <div className="text-center">
            <p className="text-gray-600 mb-6">
              {isActive 
                ? 'Your bot is currently active and responding to customer messages.'
                : 'Your bot is currently inactive and not responding to messages.'
              }
            </p>
            
            <button
              onClick={handleStatusToggle}
              className={`px-8 py-3 rounded-xl text-white transition-all duration-200 font-medium shadow-lg flex items-center gap-2 mx-auto ${
                isActive
                  ? 'bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700'
                  : 'bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700'
              }`}
            >
              {isActive ? (
                <>
                  <PowerOff className="w-5 h-5" />
                  Deactivate Bot
                </>
              ) : (
                <>
                  <Power className="w-5 h-5" />
                  Activate Bot
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BotStatusManager
