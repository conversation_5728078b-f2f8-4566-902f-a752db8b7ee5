import React, { useState, useEffect } from 'react'
import {
  Search,
  Download,
  Filter,
  MoreVertical
} from 'lucide-react'
import { apiService } from '../services/api'

interface Order {
  id: string
  customer: string
  amount: number
  date: string
  payment: string
  status: 'New' | 'Processing' | 'Completed' | 'Cancelled'
}

const TeleShopOrders: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('All Status')
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 7

  useEffect(() => {
    fetchOrders()
  }, [])

  const fetchOrders = async () => {
    try {
      setLoading(true)

      // Try to fetch from API first, fallback to mock data
      try {
        // Uncomment when API is ready
        // const response = await apiService.getOrders()
        // if (response.success) {
        //   setOrders(response.data.orders)
        // }
        throw new Error('API not implemented yet') // Remove this line when API is ready
      } catch (apiError) {
        console.log('Using mock data due to API error:', apiError.message)

        // Mock data fallback
        setOrders([
          { id: 'ORD-001', customer: 'John Doe', amount: 99.00, date: '5/10/2025, 5:30:00 PM', payment: 'Credit Card', status: 'New' },
          { id: 'ORD-002', customer: 'Jane Smith', amount: 499.00, date: '5/9/2025, 2:15:00 PM', payment: 'PayPal', status: 'Processing' },
          { id: 'ORD-003', customer: 'Robert Johnson', amount: 199.00, date: '5/8/2025, 7:45:00 PM', payment: 'Credit Card', status: 'Completed' },
          { id: 'ORD-004', customer: 'Sarah Wilson', amount: 158.00, date: '5/10/2025, 12:20:00 PM', payment: 'Credit Card', status: 'New' },
          { id: 'ORD-005', customer: 'Michael Brown', amount: 129.00, date: '5/7/2025, 4:10:00 PM', payment: 'PayPal', status: 'Cancelled' },
          { id: 'ORD-006', customer: 'Emily Davis', amount: 55.00, date: '5/6/2025, 1:05:00 PM', payment: 'Credit Card', status: 'Completed' },
          { id: 'ORD-007', customer: 'David Wilson', amount: 699.00, date: '5/9/2025, 11:50:00 AM', payment: 'Credit Card', status: 'Processing' },
        ])
      }

      setLoading(false)
    } catch (error) {
      console.error('Error fetching orders:', error)
      setLoading(false)
    }
  }

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customer.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'All Status' || order.status === selectedStatus
    return matchesSearch && matchesStatus
  })

  const totalPages = Math.ceil(filteredOrders.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedOrders = filteredOrders.slice(startIndex, startIndex + itemsPerPage)

  const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
    const getStatusColor = (status: string) => {
      switch (status) {
        case 'New': return 'badge-info'
        case 'Processing': return 'badge-warning'
        case 'Completed': return 'badge-success'
        case 'Cancelled': return 'badge-error'
        default: return 'badge-info'
      }
    }

    return (
      <span className={`badge ${getStatusColor(status)}`}>
        {status}
      </span>
    )
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-700 rounded w-64 mb-6"></div>
          <div className="grid grid-cols-1 gap-4">
            {[...Array(7)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
            Order Processing
          </h1>
          <p className="text-sm mt-1" style={{ color: 'var(--text-secondary)' }}>
            Manage and track customer orders
          </p>
        </div>
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <button className="btn btn-secondary">
            <Download className="w-4 h-4 mr-2" />
            Export Orders
          </button>
          <button className="btn btn-secondary">
            <Filter className="w-4 h-4 mr-2" />
            More Filters
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4" style={{ color: 'var(--text-secondary)' }} />
            <input
              type="text"
              placeholder="Search by order ID or customer..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input pl-10 pr-4 py-2 w-full"
            />
          </div>

          {/* Status Filter */}
          <div className="flex items-center space-x-3">
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="select"
            >
              <option>All Status</option>
              <option>New</option>
              <option>Processing</option>
              <option>Completed</option>
              <option>Cancelled</option>
            </select>
          </div>
        </div>
      </div>

      {/* Orders Table */}
      <div className="card p-0 overflow-hidden">
        <table className="table-teleshop">
          <thead>
            <tr>
              <th>Order ID</th>
              <th>Customer</th>
              <th>Amount</th>
              <th>Date</th>
              <th>Payment</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {paginatedOrders.map((order) => (
              <tr key={order.id}>
                <td>
                  <span className="font-medium" style={{ color: 'var(--text-primary)' }}>
                    {order.id}
                  </span>
                </td>
                <td>
                  <span style={{ color: 'var(--text-primary)' }}>
                    {order.customer}
                  </span>
                </td>
                <td>
                  <span className="font-medium" style={{ color: 'var(--text-primary)' }}>
                    ${order.amount.toFixed(2)}
                  </span>
                </td>
                <td>
                  <span style={{ color: 'var(--text-secondary)' }}>
                    {order.date}
                  </span>
                </td>
                <td>
                  <span style={{ color: 'var(--text-secondary)' }}>
                    {order.payment}
                  </span>
                </td>
                <td>
                  <StatusBadge status={order.status} />
                </td>
                <td>
                  <button className="p-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <MoreVertical className="w-4 h-4" style={{ color: 'var(--text-secondary)' }} />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
          Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredOrders.length)} of {filteredOrders.length} entries
        </p>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 rounded-lg border border-gray-600 text-sm disabled:opacity-50"
            style={{ color: 'var(--text-secondary)' }}
          >
            Previous
          </button>
          <span className="px-3 py-1 bg-blue-500 text-white rounded-lg text-sm">
            {currentPage}
          </span>
          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 rounded-lg border border-gray-600 text-sm disabled:opacity-50"
            style={{ color: 'var(--text-secondary)' }}
          >
            Next
          </button>
        </div>
      </div>
    </div>
  )
}

export default TeleShopOrders
