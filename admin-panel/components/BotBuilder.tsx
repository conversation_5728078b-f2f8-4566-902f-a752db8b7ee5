import React, { useState } from 'react'
import {
  Bot,
  ArrowRight,
  ArrowLeft,
  Check,
  Settings,
  MessageSquare,
  CreditCard,
  Palette,
  Zap,
  Globe,
  Users,
  Package,
  Shield,
  Smartphone,
  Eye,
  EyeOff,
  Copy,
  ExternalLink,
  HelpCircle,
  CheckCircle,
  AlertTriangle,
  Loader2,
  Sparkles,
  Rocket,
  Star,
  DollarSign,
  Languages,
  Info
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface BotBuilderProps {
  onComplete: (botData: any) => void
  onCancel: () => void
}

interface BotData {
  // Basic Info
  name: string
  username: string
  token: string
  description: string
  
  // Settings
  welcomeMessage: string
  language: string
  currency: string
  paymentMethods: string[]
  supportContact: string
  storeInfo: string
  
  // Theme
  primaryColor: string
  accentColor: string
  
  // Features
  features: {
    categories: boolean
    userProfiles: boolean
    balanceTopUp: boolean
    orderHistory: boolean
  }
  
  // Security
  security: {
    rateLimiting: boolean
    spamProtection: boolean
    adminNotifications: boolean
  }
}

const BotBuilder: React.FC<BotBuilderProps> = ({ onComplete, onCancel }) => {
  const [currentStep, setCurrentStep] = useState(1)
  const [showToken, setShowToken] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [botData, setBotData] = useState<BotData>({
    name: '',
    username: '',
    token: '',
    description: '',
    welcomeMessage: 'Welcome to my store! 🛍️ Browse our amazing products and find exactly what you need.',
    language: 'en',
    currency: 'USD',
    paymentMethods: ['BITCOIN', 'ETHEREUM'],
    supportContact: '',
    storeInfo: '',
    primaryColor: '#3B82F6',
    accentColor: '#8B5CF6',
    features: {
      categories: true,
      userProfiles: true,
      balanceTopUp: true,
      orderHistory: true
    },
    security: {
      rateLimiting: true,
      spamProtection: true,
      adminNotifications: true
    }
  })

  const steps = [
    {
      id: 1,
      title: 'Bot Setup',
      description: 'Basic bot information and token',
      icon: Bot,
      fields: ['name', 'username', 'token', 'description']
    },
    {
      id: 2,
      title: 'Messages & Info',
      description: 'Welcome message and store information',
      icon: MessageSquare,
      fields: ['welcomeMessage', 'storeInfo', 'supportContact']
    },
    {
      id: 3,
      title: 'Localization',
      description: 'Language and currency settings',
      icon: Globe,
      fields: ['language', 'currency']
    },
    {
      id: 4,
      title: 'Payments',
      description: 'Payment methods configuration',
      icon: CreditCard,
      fields: ['paymentMethods']
    },
    {
      id: 5,
      title: 'Features',
      description: 'Enable bot features and capabilities',
      icon: Package,
      fields: ['features']
    },
    {
      id: 6,
      title: 'Theme',
      description: 'Customize bot appearance',
      icon: Palette,
      fields: ['primaryColor', 'accentColor']
    },
    {
      id: 7,
      title: 'Security',
      description: 'Security and protection settings',
      icon: Shield,
      fields: ['security']
    },
    {
      id: 8,
      title: 'Review',
      description: 'Review and create your bot',
      icon: CheckCircle,
      fields: []
    }
  ]

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(botData.name && botData.username && botData.token)
      case 2:
        return !!(botData.welcomeMessage)
      case 3:
        return !!(botData.language && botData.currency)
      case 4:
        return botData.paymentMethods.length > 0
      default:
        return true
    }
  }

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(Math.min(steps.length, currentStep + 1))
    } else {
      toast.error('Please fill in all required fields')
    }
  }

  const prevStep = () => {
    setCurrentStep(Math.max(1, currentStep - 1))
  }

  const handleCreate = async () => {
    setIsCreating(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      toast.success('Bot created successfully!')
      onComplete(botData)
    } catch (error) {
      console.error('Error creating bot:', error)
      toast.error('Failed to create bot')
    } finally {
      setIsCreating(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied to clipboard!')
  }

  const togglePaymentMethod = (method: string) => {
    const methods = botData.paymentMethods.includes(method)
      ? botData.paymentMethods.filter(m => m !== method)
      : [...botData.paymentMethods, method]
    
    setBotData({ ...botData, paymentMethods: methods })
  }

  const currentStepData = steps[currentStep - 1]
  const isLastStep = currentStep === steps.length
  const canProceed = validateStep(currentStep)

  return (
    <div className="max-w-4xl mx-auto">
      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Create New Telegram Bot
          </h2>
          <span className="text-sm text-gray-500">
            Step {currentStep} of {steps.length}
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          {steps.map((step, index) => (
            <React.Fragment key={step.id}>
              <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium transition-all duration-200 ${
                index + 1 < currentStep
                  ? 'bg-green-500 text-white'
                  : index + 1 === currentStep
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-600'
              }`}>
                {index + 1 < currentStep ? (
                  <Check className="w-4 h-4" />
                ) : (
                  step.id
                )}
              </div>
              {index < steps.length - 1 && (
                <div className={`flex-1 h-1 rounded transition-all duration-200 ${
                  index + 1 < currentStep ? 'bg-green-500' : 'bg-gray-200'
                }`} />
              )}
            </React.Fragment>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
              <currentStepData.icon className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900">{currentStepData.title}</h3>
              <p className="text-gray-600">{currentStepData.description}</p>
            </div>
          </div>
        </div>

        <div className="p-6">
          {/* Step 1: Bot Setup */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="bg-blue-50 p-4 rounded-xl">
                <h4 className="font-semibold text-blue-900 mb-2 flex items-center gap-2">
                  <HelpCircle className="h-4 w-4" />
                  How to get a bot token
                </h4>
                <ol className="text-sm text-blue-800 space-y-1">
                  <li>1. Open Telegram and search for @BotFather</li>
                  <li>2. Send /newbot command</li>
                  <li>3. Choose a name and username for your bot</li>
                  <li>4. Copy the token provided by BotFather</li>
                  <li>5. Paste it in the token field below</li>
                </ol>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Bot className="h-4 w-4 inline mr-1" />
                    Bot Name *
                  </label>
                  <input
                    type="text"
                    required
                    value={botData.name}
                    onChange={(e) => setBotData({...botData, name: e.target.value})}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="My Digital Store Bot"
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Smartphone className="h-4 w-4 inline mr-1" />
                    Bot Username *
                  </label>
                  <input
                    type="text"
                    required
                    value={botData.username}
                    onChange={(e) => setBotData({...botData, username: e.target.value})}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="mystore_bot"
                  />
                  <p className="text-xs text-gray-500 mt-1">Username without @ symbol</p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Zap className="h-4 w-4 inline mr-1" />
                  Bot Token *
                </label>
                <div className="relative">
                  <input
                    type={showToken ? 'text' : 'password'}
                    required
                    value={botData.token}
                    onChange={(e) => setBotData({...botData, token: e.target.value})}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 pr-20 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="1234567890:AAEhBOweik6ad6PsVQF6XJhiknbhd6k-Ks"
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center gap-2 pr-3">
                    <button
                      type="button"
                      onClick={() => setShowToken(!showToken)}
                      className="p-1 text-gray-400 hover:text-gray-600"
                    >
                      {showToken ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                    {botData.token && (
                      <button
                        type="button"
                        onClick={() => copyToClipboard(botData.token)}
                        className="p-1 text-gray-400 hover:text-gray-600"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-1">Get this from @BotFather on Telegram</p>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <MessageSquare className="h-4 w-4 inline mr-1" />
                  Description
                </label>
                <textarea
                  value={botData.description}
                  onChange={(e) => setBotData({...botData, description: e.target.value})}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  rows={3}
                  placeholder="Brief description of your bot store..."
                />
              </div>
            </div>
          )}

          {/* Step 2: Messages & Info */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <MessageSquare className="h-4 w-4 inline mr-1" />
                  Welcome Message *
                </label>
                <textarea
                  required
                  value={botData.welcomeMessage}
                  onChange={(e) => setBotData({...botData, welcomeMessage: e.target.value})}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  rows={4}
                  placeholder="Welcome message shown to new users..."
                />
                <p className="text-xs text-gray-500 mt-1">This message will be sent when users first start your bot</p>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Info className="h-4 w-4 inline mr-1" />
                  Store Information
                </label>
                <textarea
                  value={botData.storeInfo}
                  onChange={(e) => setBotData({...botData, storeInfo: e.target.value})}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  rows={3}
                  placeholder="Information about your store, policies, return policy, etc..."
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Users className="h-4 w-4 inline mr-1" />
                  Support Contact
                </label>
                <input
                  type="text"
                  value={botData.supportContact}
                  onChange={(e) => setBotData({...botData, supportContact: e.target.value})}
                  className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  placeholder="@support_<NAME_EMAIL>"
                />
                <p className="text-xs text-gray-500 mt-1">Telegram username or email for customer support</p>
              </div>
            </div>
          )}

          {/* Step 3: Localization */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Languages className="h-4 w-4 inline mr-1" />
                    Language *
                  </label>
                  <select
                    required
                    value={botData.language}
                    onChange={(e) => setBotData({...botData, language: e.target.value})}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  >
                    <option value="en">🇺🇸 English</option>
                    <option value="es">🇪🇸 Spanish</option>
                    <option value="fr">🇫🇷 French</option>
                    <option value="de">🇩🇪 German</option>
                    <option value="it">🇮🇹 Italian</option>
                    <option value="pt">🇵🇹 Portuguese</option>
                    <option value="ru">🇷🇺 Russian</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <DollarSign className="h-4 w-4 inline mr-1" />
                    Currency *
                  </label>
                  <select
                    required
                    value={botData.currency}
                    onChange={(e) => setBotData({...botData, currency: e.target.value})}
                    className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  >
                    <option value="USD">USD - US Dollar</option>
                    <option value="EUR">EUR - Euro</option>
                    <option value="GBP">GBP - British Pound</option>
                    <option value="BTC">BTC - Bitcoin</option>
                    <option value="ETH">ETH - Ethereum</option>
                  </select>
                </div>
              </div>

              <div className="bg-yellow-50 p-4 rounded-xl">
                <h4 className="font-semibold text-yellow-900 mb-2 flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  Localization Tips
                </h4>
                <ul className="text-sm text-yellow-800 space-y-1">
                  <li>• Choose the primary language for your bot interface</li>
                  <li>• Currency affects how prices are displayed to customers</li>
                  <li>• You can change these settings later in bot configuration</li>
                </ul>
              </div>
            </div>
          )}

          {/* Step 4: Payments */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-4">
                  <CreditCard className="h-4 w-4 inline mr-1" />
                  Payment Methods *
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    { id: 'BITCOIN', name: 'Bitcoin', icon: '₿', color: 'orange' },
                    { id: 'ETHEREUM', name: 'Ethereum', icon: 'Ξ', color: 'blue' },
                    { id: 'USDT', name: 'Tether USDT', icon: '₮', color: 'green' },
                    { id: 'LITECOIN', name: 'Litecoin', icon: 'Ł', color: 'gray' },
                    { id: 'DOGECOIN', name: 'Dogecoin', icon: 'Ð', color: 'yellow' },
                    { id: 'MONERO', name: 'Monero', icon: 'ɱ', color: 'orange' }
                  ].map((method) => (
                    <div
                      key={method.id}
                      onClick={() => togglePaymentMethod(method.id)}
                      className={`p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                        botData.paymentMethods.includes(method.id)
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-8 h-8 rounded-full bg-${method.color}-500 flex items-center justify-center text-white font-bold`}>
                          {method.icon}
                        </div>
                        <div>
                          <div className="font-semibold text-gray-900">{method.name}</div>
                          <div className="text-sm text-gray-500">{method.id}</div>
                        </div>
                        {botData.paymentMethods.includes(method.id) && (
                          <CheckCircle className="w-5 h-5 text-blue-500 ml-auto" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-2">Select at least one payment method for your store</p>
              </div>

              <div className="bg-green-50 p-4 rounded-xl">
                <h4 className="font-semibold text-green-900 mb-2 flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Secure Payments
                </h4>
                <ul className="text-sm text-green-800 space-y-1">
                  <li>• All payments are processed securely through blockchain</li>
                  <li>• Automatic payment verification and confirmation</li>
                  <li>• Real-time payment status updates</li>
                  <li>• Built-in fraud protection and monitoring</li>
                </ul>
              </div>
            </div>
          )}

          {/* Step 5: Features */}
          {currentStep === 5 && (
            <div className="space-y-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  Bot Features
                </h4>
                <div className="space-y-4">
                  {[
                    {
                      key: 'categories',
                      title: 'Product Categories',
                      description: 'Organize products into categories for better navigation',
                      icon: Package,
                      recommended: true
                    },
                    {
                      key: 'userProfiles',
                      title: 'User Profiles',
                      description: 'Allow customers to view their profile, balance, and purchase history',
                      icon: Users,
                      recommended: true
                    },
                    {
                      key: 'balanceTopUp',
                      title: 'Balance Top-up',
                      description: 'Let customers add funds to their account for faster checkout',
                      icon: DollarSign,
                      recommended: true
                    },
                    {
                      key: 'orderHistory',
                      title: 'Order History',
                      description: 'Customers can view and download their previous orders',
                      icon: Package,
                      recommended: true
                    }
                  ].map((feature) => (
                    <div
                      key={feature.key}
                      className="flex items-center justify-between p-4 border border-gray-200 rounded-xl"
                    >
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <feature.icon className="w-5 h-5 text-blue-600" />
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-semibold text-gray-900">{feature.title}</span>
                            {feature.recommended && (
                              <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                                Recommended
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">{feature.description}</p>
                        </div>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={botData.features[feature.key as keyof typeof botData.features]}
                          onChange={(e) => setBotData({
                            ...botData,
                            features: {
                              ...botData.features,
                              [feature.key]: e.target.checked
                            }
                          })}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Step 6: Theme */}
          {currentStep === 6 && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Palette className="h-4 w-4 inline mr-1" />
                    Primary Color
                  </label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      value={botData.primaryColor}
                      onChange={(e) => setBotData({...botData, primaryColor: e.target.value})}
                      className="w-16 h-12 border border-gray-300 rounded-lg cursor-pointer"
                    />
                    <input
                      type="text"
                      value={botData.primaryColor}
                      onChange={(e) => setBotData({...botData, primaryColor: e.target.value})}
                      className="flex-1 border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      placeholder="#3B82F6"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Sparkles className="h-4 w-4 inline mr-1" />
                    Accent Color
                  </label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      value={botData.accentColor}
                      onChange={(e) => setBotData({...botData, accentColor: e.target.value})}
                      className="w-16 h-12 border border-gray-300 rounded-lg cursor-pointer"
                    />
                    <input
                      type="text"
                      value={botData.accentColor}
                      onChange={(e) => setBotData({...botData, accentColor: e.target.value})}
                      className="flex-1 border border-gray-300 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      placeholder="#8B5CF6"
                    />
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-6 rounded-xl">
                <h4 className="font-semibold text-gray-900 mb-4">Theme Preview</h4>
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <div className="flex items-center gap-3 mb-4">
                    <div
                      className="w-8 h-8 rounded-full flex items-center justify-center text-white font-bold"
                      style={{ backgroundColor: botData.primaryColor }}
                    >
                      B
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{botData.name || 'Your Bot Name'}</div>
                      <div className="text-sm text-gray-500">@{botData.username || 'your_bot'}</div>
                    </div>
                  </div>
                  <div
                    className="px-4 py-2 rounded-lg text-white text-sm mb-3"
                    style={{ backgroundColor: botData.primaryColor }}
                  >
                    {botData.welcomeMessage || 'Welcome message preview'}
                  </div>
                  <div className="flex gap-2">
                    <button
                      className="px-3 py-2 rounded-lg text-white text-sm"
                      style={{ backgroundColor: botData.accentColor }}
                    >
                      Browse Products
                    </button>
                    <button
                      className="px-3 py-2 rounded-lg border text-sm"
                      style={{ borderColor: botData.primaryColor, color: botData.primaryColor }}
                    >
                      My Profile
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Step 7: Security */}
          {currentStep === 7 && (
            <div className="space-y-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Security Settings
                </h4>
                <div className="space-y-4">
                  {[
                    {
                      key: 'rateLimiting',
                      title: 'Rate Limiting',
                      description: 'Prevent spam by limiting the number of requests per user',
                      icon: Shield,
                      recommended: true
                    },
                    {
                      key: 'spamProtection',
                      title: 'Spam Protection',
                      description: 'Automatically detect and block spam messages',
                      icon: Shield,
                      recommended: true
                    },
                    {
                      key: 'adminNotifications',
                      title: 'Admin Notifications',
                      description: 'Get notified about important bot events and security issues',
                      icon: AlertTriangle,
                      recommended: true
                    }
                  ].map((setting) => (
                    <div
                      key={setting.key}
                      className="flex items-center justify-between p-4 border border-gray-200 rounded-xl"
                    >
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-red-100 rounded-lg">
                          <setting.icon className="w-5 h-5 text-red-600" />
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-semibold text-gray-900">{setting.title}</span>
                            {setting.recommended && (
                              <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                                Recommended
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">{setting.description}</p>
                        </div>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={botData.security[setting.key as keyof typeof botData.security]}
                          onChange={(e) => setBotData({
                            ...botData,
                            security: {
                              ...botData.security,
                              [setting.key]: e.target.checked
                            }
                          })}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Step 8: Review */}
          {currentStep === 8 && (
            <div className="space-y-6">
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-xl">
                <h4 className="font-bold text-gray-900 mb-2 flex items-center gap-2">
                  <Rocket className="h-5 w-5" />
                  Ready to Launch Your Bot!
                </h4>
                <p className="text-gray-600">
                  Review your bot configuration below and click "Create Bot" to launch your Telegram store.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-white p-4 rounded-xl border border-gray-200">
                    <h5 className="font-semibold text-gray-900 mb-3">Basic Information</h5>
                    <div className="space-y-2 text-sm">
                      <div><span className="text-gray-600">Name:</span> <span className="font-medium">{botData.name}</span></div>
                      <div><span className="text-gray-600">Username:</span> <span className="font-medium">@{botData.username}</span></div>
                      <div><span className="text-gray-600">Language:</span> <span className="font-medium">{botData.language.toUpperCase()}</span></div>
                      <div><span className="text-gray-600">Currency:</span> <span className="font-medium">{botData.currency}</span></div>
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-xl border border-gray-200">
                    <h5 className="font-semibold text-gray-900 mb-3">Payment Methods</h5>
                    <div className="flex flex-wrap gap-2">
                      {botData.paymentMethods.map((method) => (
                        <span key={method} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                          {method}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-white p-4 rounded-xl border border-gray-200">
                    <h5 className="font-semibold text-gray-900 mb-3">Features</h5>
                    <div className="space-y-2 text-sm">
                      {Object.entries(botData.features).map(([key, enabled]) => (
                        <div key={key} className="flex items-center gap-2">
                          {enabled ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <XCircle className="w-4 h-4 text-gray-400" />
                          )}
                          <span className={enabled ? 'text-gray-900' : 'text-gray-500'}>
                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-xl border border-gray-200">
                    <h5 className="font-semibold text-gray-900 mb-3">Security</h5>
                    <div className="space-y-2 text-sm">
                      {Object.entries(botData.security).map(([key, enabled]) => (
                        <div key={key} className="flex items-center gap-2">
                          {enabled ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <XCircle className="w-4 h-4 text-gray-400" />
                          )}
                          <span className={enabled ? 'text-gray-900' : 'text-gray-500'}>
                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Navigation */}
        <div className="flex justify-between items-center p-6 border-t border-gray-200">
          <button
            onClick={currentStep === 1 ? onCancel : prevStep}
            className="px-6 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            {currentStep === 1 ? 'Cancel' : 'Previous'}
          </button>

          <div className="flex items-center gap-3">
            {!isLastStep ? (
              <button
                onClick={nextStep}
                disabled={!canProceed}
                className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                Next
                <ArrowRight className="w-4 h-4" />
              </button>
            ) : (
              <button
                onClick={handleCreate}
                disabled={isCreating}
                className="px-8 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {isCreating ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Creating Bot...
                  </>
                ) : (
                  <>
                    <Rocket className="w-4 h-4" />
                    Create Bot
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default BotBuilder
