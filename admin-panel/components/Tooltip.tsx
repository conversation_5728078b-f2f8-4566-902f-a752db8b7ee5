import React, { useState, useRef, useEffect } from 'react'
import { HelpCircle, Info, AlertCircle, CheckCircle } from 'lucide-react'

interface TooltipProps {
  content: string | React.ReactNode
  children: React.ReactNode
  position?: 'top' | 'bottom' | 'left' | 'right' | 'auto'
  trigger?: 'hover' | 'click' | 'focus'
  variant?: 'default' | 'info' | 'warning' | 'success'
  maxWidth?: string
  delay?: number
  disabled?: boolean
  className?: string
  ariaLabel?: string
}

interface TooltipIconProps {
  content: string | React.ReactNode
  position?: 'top' | 'bottom' | 'left' | 'right' | 'auto'
  variant?: 'default' | 'info' | 'warning' | 'success'
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

const Tooltip: React.FC<TooltipProps> = ({
  content,
  children,
  position = 'auto',
  trigger = 'hover',
  variant = 'default',
  maxWidth = '300px',
  delay = 200,
  disabled = false,
  className = '',
  ariaLabel
}) => {
  const [isVisible, setIsVisible] = useState(false)
  const [actualPosition, setActualPosition] = useState(position)
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null)
  const triggerRef = useRef<HTMLDivElement>(null)
  const tooltipRef = useRef<HTMLDivElement>(null)

  const calculatePosition = () => {
    if (!triggerRef.current || !tooltipRef.current || position !== 'auto') {
      setActualPosition(position)
      return
    }

    const triggerRect = triggerRef.current.getBoundingClientRect()
    const tooltipRect = tooltipRef.current.getBoundingClientRect()
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    }

    // Check available space in each direction
    const spaceTop = triggerRect.top
    const spaceBottom = viewport.height - triggerRect.bottom
    const spaceLeft = triggerRect.left
    const spaceRight = viewport.width - triggerRect.right

    // Determine best position based on available space
    if (spaceTop >= tooltipRect.height + 10) {
      setActualPosition('top')
    } else if (spaceBottom >= tooltipRect.height + 10) {
      setActualPosition('bottom')
    } else if (spaceRight >= tooltipRect.width + 10) {
      setActualPosition('right')
    } else if (spaceLeft >= tooltipRect.width + 10) {
      setActualPosition('left')
    } else {
      setActualPosition('top') // Default fallback
    }
  }

  const showTooltip = () => {
    if (disabled) return
    
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    const id = setTimeout(() => {
      setIsVisible(true)
      setTimeout(calculatePosition, 0)
    }, delay)
    
    setTimeoutId(id)
  }

  const hideTooltip = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      setTimeoutId(null)
    }
    setIsVisible(false)
  }

  const handleTriggerEvent = (event: React.MouseEvent | React.FocusEvent) => {
    if (trigger === 'click') {
      event.preventDefault()
      isVisible ? hideTooltip() : showTooltip()
    }
  }

  const getVariantStyles = () => {
    switch (variant) {
      case 'info':
        return 'bg-gradient-to-r from-blue-600 to-cyan-600 border-blue-500'
      case 'warning':
        return 'bg-gradient-to-r from-yellow-500 to-orange-500 border-yellow-400'
      case 'success':
        return 'bg-gradient-to-r from-green-500 to-emerald-500 border-green-400'
      default:
        return 'bg-gradient-to-r from-blue-600 to-purple-600 border-blue-500'
    }
  }

  const getPositionStyles = () => {
    const baseStyles = 'absolute z-50'
    
    switch (actualPosition) {
      case 'top':
        return `${baseStyles} bottom-full left-1/2 transform -translate-x-1/2 mb-2`
      case 'bottom':
        return `${baseStyles} top-full left-1/2 transform -translate-x-1/2 mt-2`
      case 'left':
        return `${baseStyles} right-full top-1/2 transform -translate-y-1/2 mr-2`
      case 'right':
        return `${baseStyles} left-full top-1/2 transform -translate-y-1/2 ml-2`
      default:
        return `${baseStyles} bottom-full left-1/2 transform -translate-x-1/2 mb-2`
    }
  }

  const getArrowStyles = () => {
    const arrowBase = 'absolute w-0 h-0'
    const arrowSize = 'border-4'
    
    switch (actualPosition) {
      case 'top':
        return `${arrowBase} ${arrowSize} border-transparent border-t-blue-600 top-full left-1/2 transform -translate-x-1/2`
      case 'bottom':
        return `${arrowBase} ${arrowSize} border-transparent border-b-blue-600 bottom-full left-1/2 transform -translate-x-1/2`
      case 'left':
        return `${arrowBase} ${arrowSize} border-transparent border-l-blue-600 left-full top-1/2 transform -translate-y-1/2`
      case 'right':
        return `${arrowBase} ${arrowSize} border-transparent border-r-blue-600 right-full top-1/2 transform -translate-y-1/2`
      default:
        return `${arrowBase} ${arrowSize} border-transparent border-t-blue-600 top-full left-1/2 transform -translate-x-1/2`
    }
  }

  useEffect(() => {
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }
  }, [timeoutId])

  return (
    <div 
      ref={triggerRef}
      className={`relative inline-block ${className}`}
      onMouseEnter={trigger === 'hover' ? showTooltip : undefined}
      onMouseLeave={trigger === 'hover' ? hideTooltip : undefined}
      onFocus={trigger === 'focus' ? showTooltip : undefined}
      onBlur={trigger === 'focus' ? hideTooltip : undefined}
      onClick={trigger === 'click' ? handleTriggerEvent : undefined}
      aria-describedby={isVisible ? 'tooltip' : undefined}
      aria-label={ariaLabel}
    >
      {children}
      
      {isVisible && (
        <div
          ref={tooltipRef}
          id="tooltip"
          role="tooltip"
          className={`${getPositionStyles()} ${getVariantStyles()} text-white text-sm rounded-lg px-3 py-2 shadow-lg border animate-in fade-in-0 zoom-in-95 duration-200`}
          style={{ maxWidth }}
        >
          <div className="relative z-10">
            {typeof content === 'string' ? (
              <p className="leading-relaxed">{content}</p>
            ) : (
              content
            )}
          </div>
          <div className={getArrowStyles()} />
        </div>
      )}
    </div>
  )
}

// Convenient TooltipIcon component for help icons
const TooltipIcon: React.FC<TooltipIconProps> = ({
  content,
  position = 'auto',
  variant = 'default',
  size = 'md',
  className = ''
}) => {
  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'w-3 h-3'
      case 'lg':
        return 'w-5 h-5'
      default:
        return 'w-4 h-4'
    }
  }

  const getIconColor = () => {
    switch (variant) {
      case 'info':
        return 'text-blue-500 hover:text-blue-600'
      case 'warning':
        return 'text-yellow-500 hover:text-yellow-600'
      case 'success':
        return 'text-green-500 hover:text-green-600'
      default:
        return 'text-gray-400 hover:text-gray-600'
    }
  }

  const getIcon = () => {
    switch (variant) {
      case 'info':
        return Info
      case 'warning':
        return AlertCircle
      case 'success':
        return CheckCircle
      default:
        return HelpCircle
    }
  }

  const IconComponent = getIcon()

  return (
    <Tooltip
      content={content}
      position={position}
      variant={variant}
      trigger="hover"
      className={className}
      ariaLabel="Help information"
    >
      <button
        type="button"
        className={`inline-flex items-center justify-center transition-colors duration-200 ${getIconColor()} hover:bg-gray-100 rounded-full p-1`}
        tabIndex={0}
      >
        <IconComponent className={getIconSize()} />
      </button>
    </Tooltip>
  )
}

export default Tooltip
export { TooltipIcon }
