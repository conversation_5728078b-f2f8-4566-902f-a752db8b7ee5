import axios, { AxiosInstance } from 'axios'
import {
  ApiResponse,
  Product,
  Order,
  User,
  Coupon,
  CryptoWallet,
  PaginatedResponse,
  SalesAnalytics
} from '@teleshop/shared'

class ApiService {
  private api: AxiosInstance

  constructor() {
    this.api = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    // Response interceptor for token refresh
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          const refreshToken = localStorage.getItem('refreshToken')
          if (refreshToken) {
            try {
              const response = await this.api.post('/auth/refresh', { refreshToken })
              const { tokens } = response.data.data
              localStorage.setItem('accessToken', tokens.accessToken)
              localStorage.setItem('refreshToken', tokens.refreshToken)
              this.setAuthToken(tokens.accessToken)
              // Retry original request
              return this.api.request(error.config)
            } catch (refreshError) {
              localStorage.removeItem('accessToken')
              localStorage.removeItem('refreshToken')
              window.location.href = '/login'
            }
          }
        }
        return Promise.reject(error)
      }
    )
  }

  setAuthToken(token: string) {
    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`
  }

  removeAuthToken() {
    delete this.api.defaults.headers.common['Authorization']
  }

  // Auth
  async login(email: string, password: string): Promise<ApiResponse<{ user: User; tokens: any }>> {
    console.log('API Service: Making login request to:', this.api.defaults.baseURL + '/auth/login')
    console.log('API Service: Login data:', { email, password: '***' })

    try {
      const response = await this.api.post('/auth/login', { email, password })
      console.log('API Service: Login response:', response.data)
      return response.data
    } catch (error: any) {
      console.error('API Service: Login error:', error.response?.data || error.message)
      throw error
    }
  }

  async getCurrentUser(): Promise<ApiResponse<{ user: User }>> {
    const response = await this.api.get('/auth/me')
    return response.data
  }

  // Products
  async getProducts(params?: any): Promise<ApiResponse<{ products: Product[]; pagination: any }>> {
    const response = await this.api.get('/products', { params })
    return response.data
  }

  async getProduct(id: string): Promise<ApiResponse<{ product: Product }>> {
    const response = await this.api.get(`/products/${id}`)
    return response.data
  }

  async createProduct(data: any): Promise<ApiResponse<{ product: Product }>> {
    const response = await this.api.post('/products', data)
    return response.data
  }

  async updateProduct(id: string, data: any): Promise<ApiResponse<{ product: Product }>> {
    const response = await this.api.put(`/products/${id}`, data)
    return response.data
  }

  async deleteProduct(id: string): Promise<ApiResponse> {
    const response = await this.api.delete(`/products/${id}`)
    return response.data
  }

  // File uploads
  async uploadProductFile(productId: string, file: File): Promise<ApiResponse> {
    const formData = new FormData()
    formData.append('product', file)
    const response = await this.api.post(`/upload/product/${productId}`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
    return response.data
  }

  async uploadPreview(productId: string, file: File): Promise<ApiResponse> {
    const formData = new FormData()
    formData.append('preview', file)
    const response = await this.api.post(`/upload/preview/${productId}`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
    return response.data
  }

  async uploadThumbnail(productId: string, file: File): Promise<ApiResponse> {
    const formData = new FormData()
    formData.append('thumbnail', file)
    const response = await this.api.post(`/upload/thumbnail/${productId}`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
    return response.data
  }

  // Orders
  async getOrders(params?: any): Promise<ApiResponse<{ orders: Order[]; pagination: any }>> {
    const response = await this.api.get('/orders', { params })
    return response.data
  }

  async getOrder(id: string): Promise<ApiResponse<{ order: Order }>> {
    const response = await this.api.get(`/orders/${id}`)
    return response.data
  }

  async updateOrderStatus(id: string, status: string, transactionHash?: string): Promise<ApiResponse<{ order: Order }>> {
    const response = await this.api.put(`/orders/${id}/status`, { status, transactionHash })
    return response.data
  }

  // Users
  async getUsers(params?: any): Promise<ApiResponse<{ users: User[]; pagination: any }>> {
    const response = await this.api.get('/users', { params })
    return response.data
  }

  async updateUser(id: string, data: any): Promise<ApiResponse<{ user: User }>> {
    const response = await this.api.put(`/users/${id}`, data)
    return response.data
  }

  // Coupons
  async getCoupons(params?: any): Promise<ApiResponse<{ coupons: Coupon[]; pagination: any }>> {
    const response = await this.api.get('/coupons', { params })
    return response.data
  }

  async createCoupon(data: any): Promise<ApiResponse<{ coupon: Coupon }>> {
    const response = await this.api.post('/coupons', data)
    return response.data
  }

  async updateCoupon(id: string, data: any): Promise<ApiResponse<{ coupon: Coupon }>> {
    const response = await this.api.put(`/coupons/${id}`, data)
    return response.data
  }

  async deleteCoupon(id: string): Promise<ApiResponse> {
    const response = await this.api.delete(`/coupons/${id}`)
    return response.data
  }

  // Wallets
  async getWallets(): Promise<ApiResponse<{ wallets: CryptoWallet[] }>> {
    const response = await this.api.get('/wallets')
    return response.data
  }

  async createWallet(data: any): Promise<ApiResponse<{ wallet: CryptoWallet }>> {
    const response = await this.api.post('/wallets', data)
    return response.data
  }

  async updateWallet(id: string, data: any): Promise<ApiResponse<{ wallet: CryptoWallet }>> {
    const response = await this.api.put(`/wallets/${id}`, data)
    return response.data
  }

  async deleteWallet(id: string): Promise<ApiResponse> {
    const response = await this.api.delete(`/wallets/${id}`)
    return response.data
  }

  // Analytics
  async getSalesAnalytics(period?: string): Promise<ApiResponse<{ analytics: SalesAnalytics }>> {
    const response = await this.api.get('/analytics/sales', { params: { period } })
    return response.data
  }

  async getDashboardData(): Promise<ApiResponse<{ dashboard: any }>> {
    const response = await this.api.get('/analytics/dashboard')
    return response.data
  }
}

export const apiService = new ApiService()
