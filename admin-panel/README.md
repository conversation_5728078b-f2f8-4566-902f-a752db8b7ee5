# TeleShop Admin Panel

A modern, responsive admin panel for the TeleShop e-commerce system with dark theme design and comprehensive management features.

## 🎨 Features

### TeleShop Dark Theme
- **Professional Design**: Modern dark theme with gradient accents
- **Responsive Layout**: Card-based design with smooth animations
- **Theme Toggle**: Dark/light mode support (currently dark theme active)
- **Typography**: Clean, readable fonts with proper hierarchy
- **Color System**: Sophisticated color palette with CSS variables

### Core Functionality
- **Dashboard**: Analytics overview with charts and key metrics
- **Product Management**: CRUD operations with pagination and file uploads
- **Order Management**: Order tracking and status updates
- **Customer Management**: User administration and account management
- **Settings Panel**: Telegram bot configuration, crypto wallets, store settings
- **Mock Authentication**: Development-ready authentication system

### Technical Features
- **Error Handling**: Graceful API fallbacks with mock data
- **Loading States**: Smooth loading animations and spinners
- **Form Validation**: React Hook Form with validation
- **Toast Notifications**: User feedback with react-hot-toast
- **Icon System**: Lucide React icons throughout the interface

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Git

### Installation

1. **Clone and navigate to admin panel**
   ```bash
   git clone https://github.com/marysarahmccolley44/TG.git
   cd TG/admin-panel
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Access the admin panel**
   - URL: http://localhost:4002
   - **Demo Credentials**: `<EMAIL>` / `admin123`

## 🔐 Authentication System

### Current: Mock Authentication

The admin panel currently uses mock authentication for development:

- **Login**: Uses hardcoded credentials in `contexts/AuthContext.tsx`
- **Session**: Stores mock tokens in localStorage
- **User Data**: Creates mock user object with admin role
- **Auto-fill**: Login page includes auto-fill button for convenience

### Demo Credentials
- **Email**: `<EMAIL>`
- **Password**: `admin123`

### Switching to Real API

To enable real authentication:

1. **Update AuthContext** (`contexts/AuthContext.tsx`):
   ```typescript
   // Uncomment API calls in login() and checkAuth()
   // Comment out mock authentication logic
   ```

2. **Update Login Page** (`pages/login.tsx`):
   ```typescript
   // Remove or hide demo credentials section
   ```

3. **Configure API** (`.env.local`):
   ```env
   NEXT_PUBLIC_API_URL=http://localhost:4000/api
   ```

## 📁 Project Structure

```
admin-panel/
├── components/           # React components
│   ├── Layout.tsx       # Main layout with navigation
│   ├── TeleShopDashboard.tsx    # Dashboard component
│   ├── TeleShopProducts.tsx     # Products management
│   ├── TeleShopOrders.tsx       # Orders management
│   ├── TeleShopCustomers.tsx    # Customer management
│   ├── ErrorBoundary.tsx        # Error handling
│   └── LoadingSpinner.tsx       # Loading component
├── contexts/            # React contexts
│   └── AuthContext.tsx  # Authentication context
├── pages/              # Next.js pages
│   ├── index.tsx       # Dashboard page
│   ├── login.tsx       # Login page
│   ├── products.tsx    # Products page
│   ├── orders.tsx      # Orders page
│   ├── customers.tsx   # Customers page
│   └── settings.tsx    # Settings page
├── services/           # API services
│   └── api.ts          # API service layer
├── styles/             # CSS styles
│   └── globals.css     # Global styles with TeleShop theme
└── public/             # Static assets
```

## 🎨 TeleShop Theme System

### CSS Variables
The theme uses CSS variables for consistent styling:

```css
:root {
  --bg-primary: #0f172a;      /* Main background */
  --bg-secondary: #1e293b;    /* Secondary background */
  --bg-tertiary: #334155;     /* Tertiary background */
  --text-primary: #f8fafc;    /* Primary text */
  --text-secondary: #cbd5e1;  /* Secondary text */
  --primary: #3b82f6;         /* Primary color */
  --border-color: #475569;    /* Border color */
}
```

### Component Classes
- `.card`: Standard card component with glass effect
- `.btn`: Button styles with variants (primary, secondary)
- `.input`: Form input styling
- `.gradient-text`: Gradient text effect

## 🛠 Development

### Available Scripts

```bash
npm run dev      # Start development server (port 4002)
npm run build    # Build for production
npm run start    # Start production server
npm run lint     # Run ESLint
```

### Environment Variables

Create `.env.local` file:

```env
NEXT_PUBLIC_API_URL=http://localhost:4000/api
```

### Mock Data

Components include mock data for development:
- Dashboard analytics
- Product listings
- Order history
- Customer data

### API Integration

The `services/api.ts` file provides:
- Authentication methods
- CRUD operations for all entities
- File upload handling
- Error handling with fallbacks

## 🔧 Configuration

### Telegram Bot Settings

Configure bot through the Settings page:
1. Navigate to Settings → Telegram Bot
2. Enter bot token from @BotFather
3. Configure API base URL
4. Set bot name and username
5. Save configuration

### Crypto Wallets

Set up payment wallets:
1. Go to Settings → Crypto Wallets
2. Add wallet addresses for supported currencies
3. Save configuration

## 🚀 Deployment

### Development
```bash
npm run dev
```

### Production Build
```bash
npm run build
npm run start
```

### Docker Deployment
```bash
docker build -t teleshop-admin .
docker run -p 4002:4002 teleshop-admin
```

## 🔍 Troubleshooting

### Common Issues

1. **429 API Errors**: System uses mock data fallback
2. **Login Issues**: Use demo credentials or check AuthContext
3. **Build Errors**: Ensure all dependencies are installed
4. **Port Conflicts**: Admin panel runs on port 4002

### Error Handling

- **Error Boundary**: Catches React errors gracefully
- **API Fallbacks**: Mock data when API unavailable
- **Toast Notifications**: User-friendly error messages
- **Loading States**: Proper loading indicators

## 📚 Dependencies

### Core Dependencies
- **Next.js 14**: React framework
- **React 18**: UI library
- **TypeScript**: Type safety
- **Tailwind CSS**: Styling framework
- **Lucide React**: Icon library

### Additional Libraries
- **React Hook Form**: Form handling
- **React Hot Toast**: Notifications
- **Axios**: HTTP client
- **Recharts**: Charts and analytics
- **Clsx**: Conditional classes

## 🤝 Contributing

1. Follow the TeleShop design system
2. Use TypeScript for type safety
3. Include error handling and loading states
4. Test with mock data before API integration
5. Maintain responsive design principles

---

**TeleShop Admin Panel - Professional e-commerce management interface**
