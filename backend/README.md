# TeleShop Backend API

RESTful API server for the TeleShop e-commerce system, built with Node.js, Express.js, TypeScript, and Prisma ORM.

## 🚀 Features

### Core API Functionality
- **Authentication**: JWT-based auth with refresh tokens
- **User Management**: Registration, login, profile management
- **Product Management**: CRUD operations for digital products
- **Order Management**: Order creation, tracking, and status updates
- **File Management**: Secure file upload and download system
- **Payment Processing**: Cryptocurrency payment integration

### Security Features
- **Rate Limiting**: Configurable request rate limiting
- **CORS Protection**: Cross-origin resource sharing configuration
- **Input Validation**: Request validation and sanitization
- **File Upload Security**: Type validation and size limits
- **JWT Security**: Secure token generation and validation

### Database Features
- **PostgreSQL**: Robust relational database
- **Prisma ORM**: Type-safe database access
- **Migrations**: Database schema versioning
- **Seeding**: Development data seeding

## 🛠 Technology Stack

- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Language**: TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT (jsonwebtoken)
- **File Upload**: Multer
- **Validation**: Joi
- **Security**: Helmet, CORS, bcryptjs
- **Development**: Nodemon, ts-node

## 📋 Prerequisites

- Node.js 18+ and npm
- PostgreSQL 15+
- Git

## 🚀 Quick Start

### Development Setup

1. **Navigate to backend directory**
   ```bash
   cd backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Set up database**
   ```bash
   # Create database
   createdb ecommerce_db

   # Run migrations
   npx prisma migrate dev

   # Seed database
   npx prisma db seed
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

6. **Access API**
   - Base URL: http://localhost:4000
   - API Docs: http://localhost:4000/api

### Docker Setup

```bash
# Build and run with Docker
docker build -t teleshop-backend .
docker run -p 4000:4000 teleshop-backend
```

## 🔧 Configuration

### Environment Variables

Create `.env` file from `.env.example`:

```env
# Database
DATABASE_URL="postgresql://postgres:password@localhost:5432/ecommerce_db"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key"
JWT_REFRESH_SECRET="your-super-secret-refresh-key"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Server
PORT=4000
NODE_ENV="development"
CORS_ORIGIN="http://localhost:4002"

# File Upload
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE=104857600  # 100MB

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100
```

### Database Configuration

The system uses PostgreSQL with Prisma ORM:

```bash
# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate dev

# Reset database (development)
npx prisma migrate reset

# View database
npx prisma studio
```

## 📚 API Documentation

### Authentication Endpoints

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "username": "username"
}
```

#### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Refresh Token
```http
POST /api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "your-refresh-token"
}
```

#### Get Current User
```http
GET /api/auth/me
Authorization: Bearer your-access-token
```

### Product Endpoints

#### List Products
```http
GET /api/products?page=1&limit=10&search=query
```

#### Get Product
```http
GET /api/products/:id
```

#### Create Product (Admin)
```http
POST /api/products
Authorization: Bearer admin-token
Content-Type: application/json

{
  "name": "Product Name",
  "description": "Product description",
  "price": 29.99,
  "category": "software",
  "isActive": true
}
```

#### Update Product (Admin)
```http
PUT /api/products/:id
Authorization: Bearer admin-token
Content-Type: application/json

{
  "name": "Updated Name",
  "price": 39.99
}
```

#### Delete Product (Admin)
```http
DELETE /api/products/:id
Authorization: Bearer admin-token
```

### Order Endpoints

#### List Orders
```http
GET /api/orders?page=1&limit=10&status=pending
Authorization: Bearer token
```

#### Get Order
```http
GET /api/orders/:id
Authorization: Bearer token
```

#### Create Order
```http
POST /api/orders
Authorization: Bearer token
Content-Type: application/json

{
  "items": [
    {
      "productId": "product-id",
      "quantity": 1
    }
  ],
  "paymentMethod": "bitcoin"
}
```

#### Update Order Status (Admin)
```http
PUT /api/orders/:id/status
Authorization: Bearer admin-token
Content-Type: application/json

{
  "status": "completed",
  "transactionHash": "tx-hash"
}
```

### File Upload Endpoints

#### Upload Product File
```http
POST /api/upload/product/:productId
Authorization: Bearer admin-token
Content-Type: multipart/form-data

product: [file]
```

#### Upload Thumbnail
```http
POST /api/upload/thumbnail/:productId
Authorization: Bearer admin-token
Content-Type: multipart/form-data

thumbnail: [file]
```

#### Generate Download Link
```http
GET /api/downloads/generate/:productId
Authorization: Bearer token
```

#### Download File
```http
GET /api/downloads/file/:token
```

## 🗄️ Database Schema

### Key Models

#### User
- id, email, username, password
- role (ADMIN, CUSTOMER)
- isActive, createdAt, updatedAt

#### Product
- id, name, description, price
- category, isActive
- productFile, thumbnail, preview
- createdAt, updatedAt

#### Order
- id, userId, status, totalAmount
- paymentMethod, transactionHash
- createdAt, updatedAt

#### OrderItem
- id, orderId, productId, quantity, price

#### CryptoWallet
- id, currency, address, isActive

## 🔒 Security

### Authentication
- JWT tokens with configurable expiration
- Refresh token rotation
- Password hashing with bcrypt
- Role-based access control

### File Security
- File type validation
- Size limits
- Secure file storage
- Download token expiration

### API Security
- Rate limiting per IP
- CORS configuration
- Helmet security headers
- Input validation and sanitization

## 🛠 Development

### Available Scripts

```bash
npm run dev          # Start development server with hot reload
npm run build        # Build TypeScript to JavaScript
npm run start        # Start production server
npm run test         # Run tests (when available)
npm run lint         # Run ESLint
npm run db:generate  # Generate Prisma client
npm run db:migrate   # Run database migrations
npm run db:seed      # Seed database with test data
npm run db:studio    # Open Prisma Studio
```

### Project Structure

```
backend/
├── src/
│   ├── controllers/     # Request handlers
│   ├── middleware/      # Express middleware
│   ├── routes/         # API routes
│   ├── services/       # Business logic
│   ├── utils/          # Utility functions
│   ├── types/          # TypeScript types
│   └── app.ts          # Express app setup
├── prisma/
│   ├── schema.prisma   # Database schema
│   ├── migrations/     # Database migrations
│   └── seed.ts         # Database seeding
├── uploads/            # File uploads directory
└── dist/              # Compiled JavaScript
```

### Adding New Endpoints

1. **Define route** in `src/routes/`
2. **Create controller** in `src/controllers/`
3. **Add validation** using Joi schemas
4. **Update types** in `src/types/`
5. **Add tests** (when test framework is set up)

## 🚀 Deployment

### Production Build

```bash
npm run build
npm start
```

### Environment Variables

Set production values:
- Strong JWT secrets
- Production database URL
- Disable debug mode
- Configure CORS for production domains

### Docker Deployment

```bash
docker build -t teleshop-backend .
docker run -d -p 4000:4000 --env-file .env teleshop-backend
```

## 🔍 Troubleshooting

### Common Issues

1. **Database Connection**: Check PostgreSQL is running and DATABASE_URL is correct
2. **Migration Errors**: Run `npx prisma migrate reset` to reset database
3. **File Upload Issues**: Check UPLOAD_DIR permissions and MAX_FILE_SIZE
4. **JWT Errors**: Verify JWT_SECRET is set and tokens are valid
5. **CORS Errors**: Check CORS_ORIGIN matches frontend URL

### Debug Mode

Enable debug logging:
```env
DEBUG=true
LOG_LEVEL=debug
```

## 📚 Additional Resources

- **Prisma Documentation**: https://www.prisma.io/docs
- **Express.js Guide**: https://expressjs.com/
- **JWT Best Practices**: https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/

## 🤝 Contributing

1. Follow TypeScript best practices
2. Use Prisma for database operations
3. Implement proper error handling
4. Add input validation for all endpoints
5. Follow RESTful API conventions
6. Update documentation for new features

---

**TeleShop Backend API - Powering digital commerce**
