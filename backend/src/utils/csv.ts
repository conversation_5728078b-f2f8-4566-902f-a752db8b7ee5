import { parse } from 'csv-parse';
import { stringify } from 'csv-stringify';
import { ProductImportRow, ImportResult, ImportError } from '@ecommerce/shared';
import { PrismaClient } from '@prisma/client';
import { Readable } from 'stream';

const prisma = new PrismaClient();

export interface CSVValidationRule {
  field: string;
  required: boolean;
  type: 'string' | 'number' | 'boolean';
  min?: number;
  max?: number;
  pattern?: RegExp;
}

export const PRODUCT_CSV_HEADERS = [
  'name',
  'description',
  'price',
  'currency',
  'category',
  'tags',
  'downloadLimit',
  'fileUrl',
  'fileName'
];

export const PRODUCT_VALIDATION_RULES: CSVValidationRule[] = [
  { field: 'name', required: true, type: 'string', min: 1, max: 255 },
  { field: 'description', required: true, type: 'string', min: 1, max: 1000 },
  { field: 'price', required: true, type: 'number', min: 0 },
  { field: 'currency', required: false, type: 'string', pattern: /^[A-Z]{3}$/ },
  { field: 'category', required: true, type: 'string', min: 1, max: 100 },
  { field: 'tags', required: false, type: 'string' },
  { field: 'downloadLimit', required: false, type: 'number', min: 1, max: 100 },
  { field: 'fileUrl', required: false, type: 'string' },
  { field: 'fileName', required: false, type: 'string' }
];

export function validateCSVRow(row: any, rowIndex: number, rules: CSVValidationRule[]): ImportError[] {
  const errors: ImportError[] = [];

  for (const rule of rules) {
    const value = row[rule.field];

    // Check required fields
    if (rule.required && (value === undefined || value === null || value === '')) {
      errors.push({
        row: rowIndex,
        field: rule.field,
        message: `${rule.field} is required`,
        value
      });
      continue;
    }

    // Skip validation if field is not required and empty
    if (!rule.required && (value === undefined || value === null || value === '')) {
      continue;
    }

    // Type validation
    if (rule.type === 'number') {
      const numValue = parseFloat(value);
      if (isNaN(numValue)) {
        errors.push({
          row: rowIndex,
          field: rule.field,
          message: `${rule.field} must be a valid number`,
          value
        });
        continue;
      }

      // Min/max validation for numbers
      if (rule.min !== undefined && numValue < rule.min) {
        errors.push({
          row: rowIndex,
          field: rule.field,
          message: `${rule.field} must be at least ${rule.min}`,
          value
        });
      }

      if (rule.max !== undefined && numValue > rule.max) {
        errors.push({
          row: rowIndex,
          field: rule.field,
          message: `${rule.field} must be at most ${rule.max}`,
          value
        });
      }
    }

    if (rule.type === 'string') {
      const strValue = String(value);

      // Length validation for strings
      if (rule.min !== undefined && strValue.length < rule.min) {
        errors.push({
          row: rowIndex,
          field: rule.field,
          message: `${rule.field} must be at least ${rule.min} characters`,
          value
        });
      }

      if (rule.max !== undefined && strValue.length > rule.max) {
        errors.push({
          row: rowIndex,
          field: rule.field,
          message: `${rule.field} must be at most ${rule.max} characters`,
          value
        });
      }

      // Pattern validation
      if (rule.pattern && !rule.pattern.test(strValue)) {
        errors.push({
          row: rowIndex,
          field: rule.field,
          message: `${rule.field} format is invalid`,
          value
        });
      }
    }
  }

  return errors;
}

export async function parseCSV(csvContent: string): Promise<any[]> {
  return new Promise((resolve, reject) => {
    const records: any[] = [];
    const parser = parse({
      columns: true,
      skip_empty_lines: true,
      trim: true
    });

    parser.on('readable', function() {
      let record;
      while (record = parser.read()) {
        records.push(record);
      }
    });

    parser.on('error', function(err) {
      reject(err);
    });

    parser.on('end', function() {
      resolve(records);
    });

    parser.write(csvContent);
    parser.end();
  });
}

export async function importProductsFromCSV(
  csvContent: string,
  storeId: string
): Promise<ImportResult> {
  try {
    const records = await parseCSV(csvContent);
    const errors: ImportError[] = [];
    let imported = 0;
    let failed = 0;

    for (let i = 0; i < records.length; i++) {
      const row = records[i];
      const rowIndex = i + 2; // +2 because CSV is 1-indexed and has header row

      // Validate row
      const rowErrors = validateCSVRow(row, rowIndex, PRODUCT_VALIDATION_RULES);
      if (rowErrors.length > 0) {
        errors.push(...rowErrors);
        failed++;
        continue;
      }

      try {
        // Process tags
        const tags = row.tags ? row.tags.split(',').map((tag: string) => tag.trim()) : [];

        // Create product
        await prisma.product.create({
          data: {
            name: row.name,
            description: row.description,
            price: parseFloat(row.price),
            currency: row.currency || 'USD',
            category: row.category,
            tags,
            downloadLimit: row.downloadLimit ? parseInt(row.downloadLimit) : 3,
            fileUrl: row.fileUrl || '',
            fileName: row.fileName || row.name,
            fileSize: 0, // Will be updated when file is uploaded
            storeId
          }
        });

        imported++;
      } catch (error) {
        errors.push({
          row: rowIndex,
          field: 'general',
          message: `Failed to create product: ${error instanceof Error ? error.message : 'Unknown error'}`,
          value: row
        });
        failed++;
      }
    }

    return {
      success: errors.length === 0,
      imported,
      failed,
      errors
    };
  } catch (error) {
    throw new Error(`CSV parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function exportProductsToCSV(storeId: string): Promise<string> {
  const products = await prisma.product.findMany({
    where: { storeId },
    select: {
      name: true,
      description: true,
      price: true,
      currency: true,
      category: true,
      tags: true,
      downloadLimit: true,
      fileUrl: true,
      fileName: true,
      isActive: true,
      createdAt: true
    },
    orderBy: { createdAt: 'desc' }
  });

  return new Promise((resolve, reject) => {
    const csvData: any[] = products.map(product => ({
      name: product.name,
      description: product.description,
      price: product.price,
      currency: product.currency,
      category: product.category,
      tags: product.tags.join(', '),
      downloadLimit: product.downloadLimit,
      fileUrl: product.fileUrl,
      fileName: product.fileName,
      isActive: product.isActive,
      createdAt: product.createdAt.toISOString()
    }));

    stringify(csvData, {
      header: true,
      columns: [
        'name',
        'description',
        'price',
        'currency',
        'category',
        'tags',
        'downloadLimit',
        'fileUrl',
        'fileName',
        'isActive',
        'createdAt'
      ]
    }, (err, output) => {
      if (err) {
        reject(err);
      } else {
        resolve(output);
      }
    });
  });
}

export function generateSampleCSV(): string {
  const sampleData = [
    {
      name: 'Sample Product 1',
      description: 'This is a sample product description',
      price: 29.99,
      currency: 'USD',
      category: 'Digital Downloads',
      tags: 'sample, digital, download',
      downloadLimit: 3,
      fileUrl: 'https://example.com/file1.zip',
      fileName: 'sample-file-1.zip'
    },
    {
      name: 'Sample Product 2',
      description: 'Another sample product with different attributes',
      price: 49.99,
      currency: 'USD',
      category: 'Software',
      tags: 'software, tools, productivity',
      downloadLimit: 5,
      fileUrl: 'https://example.com/file2.zip',
      fileName: 'sample-file-2.zip'
    }
  ];

  return new Promise<string>((resolve, reject) => {
    stringify(sampleData, {
      header: true,
      columns: PRODUCT_CSV_HEADERS
    }, (err, output) => {
      if (err) {
        reject(err);
      } else {
        resolve(output);
      }
    });
  }) as any; // Type assertion to handle Promise return
}
