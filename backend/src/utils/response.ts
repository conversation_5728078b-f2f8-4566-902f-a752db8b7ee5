import { ApiResponse } from '@ecommerce/shared';

export const createApiResponse = <T>(
  success: boolean,
  data?: T,
  message?: string,
  error?: string
): ApiResponse<T> => {
  return {
    success,
    data,
    message,
    error
  };
};

export const createPaginatedResponse = <T>(
  items: T[],
  page: number,
  limit: number,
  total: number,
  message?: string
) => {
  return createApiResponse(true, {
    items,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  }, message);
};

export const createErrorResponse = (
  message: string,
  statusCode: number = 500,
  details?: any
) => {
  return {
    success: false,
    error: message,
    statusCode,
    details,
    timestamp: new Date().toISOString()
  };
};
