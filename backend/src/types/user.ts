export enum UserRole {
  ADMIN = 'ADMIN',
  SELLER = 'SELLER',
  MANAGER = 'MANAGER',
  CUSTOMER = 'CUSTOMER'
}

export interface User {
  id: string;
  email: string;
  username?: string;
  telegramId?: string;
  role: UserRole;
  isActive: boolean;
  isBanned: boolean;
  storeId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserRequest {
  email: string;
  username?: string;
  password?: string;
  role: UserRole;
  storeId?: string;
}

export interface UpdateUserRequest {
  email?: string;
  username?: string;
  role?: UserRole;
  isActive?: boolean;
  isBanned?: boolean;
  storeId?: string;
}
