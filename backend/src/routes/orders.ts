import express from 'express';
import { PrismaClient } from '@prisma/client';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { validate, schemas } from '../middleware/validation';
import { asyncHandler } from '../middleware/errorHandler';
import { createApiResponse, AppError, UserRole, OrderStatus, calculatePagination, calculateDiscount } from '@ecommerce/shared';

const router = express.Router();
const prisma = new PrismaClient();

// Get orders (admin gets all, users get their own)
router.get('/', authenticate, asyncHandler(async (req: AuthRequest, res) => {
  const {
    page = 1,
    limit = 10,
    status,
    userId,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const offset = (pageNum - 1) * limitNum;

  // Build where clause
  const where: any = {};

  // Non-admin users can only see their own orders
  if (req.user!.role !== UserRole.ADMIN) {
    where.userId = req.user!.id;
  } else if (userId) {
    where.userId = userId;
  }

  if (status) {
    where.status = status;
  }

  // Get total count
  const total = await prisma.order.count({ where });

  // Get orders
  const orders = await prisma.order.findMany({
    where,
    skip: offset,
    take: limitNum,
    orderBy: { [sortBy as string]: sortOrder },
    include: {
      user: {
        select: {
          id: true,
          email: true,
          username: true
        }
      },
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              description: true,
              thumbnailUrl: true
            }
          }
        }
      }
    }
  });

  const pagination = calculatePagination(pageNum, limitNum, total);

  res.json(createApiResponse(true, {
    orders,
    pagination
  }));
}));

// Get single order
router.get('/:id', authenticate, asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;

  const where: any = { id };

  // Non-admin users can only see their own orders
  if (req.user!.role !== UserRole.ADMIN) {
    where.userId = req.user!.id;
  }

  const order = await prisma.order.findFirst({
    where,
    include: {
      user: {
        select: {
          id: true,
          email: true,
          username: true
        }
      },
      items: {
        include: {
          product: true
        }
      }
    }
  });

  if (!order) {
    throw new AppError('Order not found', 404);
  }

  res.json(createApiResponse(true, { order }));
}));

// Create order
router.post('/', authenticate, validate(schemas.createOrder), asyncHandler(async (req: AuthRequest, res) => {
  const { items, paymentMethod, couponCode } = req.body;
  const userId = req.user!.id;

  // Validate products and calculate total
  let totalAmount = 0;
  const orderItems = [];

  for (const item of items) {
    const product = await prisma.product.findFirst({
      where: {
        id: item.productId,
        isActive: true
      }
    });

    if (!product) {
      throw new AppError(`Product ${item.productId} not found or inactive`, 400);
    }

    const itemTotal = product.price * item.quantity;
    totalAmount += itemTotal;

    orderItems.push({
      productId: product.id,
      quantity: item.quantity,
      price: product.price
    });
  }

  // Apply coupon if provided
  let discountAmount = 0;
  if (couponCode) {
    const coupon = await prisma.coupon.findFirst({
      where: {
        code: couponCode,
        isActive: true,
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } }
        ]
      }
    });

    if (!coupon) {
      throw new AppError('Invalid or expired coupon code', 400);
    }

    if (coupon.maxUses && coupon.usedCount >= coupon.maxUses) {
      throw new AppError('Coupon usage limit exceeded', 400);
    }

    if (coupon.minOrderAmount && totalAmount < coupon.minOrderAmount) {
      throw new AppError(`Minimum order amount for this coupon is ${coupon.minOrderAmount}`, 400);
    }

    discountAmount = calculateDiscount(totalAmount, coupon.discountType, coupon.discountValue);

    // Update coupon usage
    await prisma.coupon.update({
      where: { id: coupon.id },
      data: { usedCount: { increment: 1 } }
    });
  }

  const finalAmount = totalAmount - discountAmount;

  // Get payment address for the selected method
  const wallet = await prisma.cryptoWallet.findFirst({
    where: {
      currency: paymentMethod,
      isActive: true
    }
  });

  if (!wallet) {
    throw new AppError(`No active wallet found for ${paymentMethod}`, 400);
  }

  // Create order
  const order = await prisma.order.create({
    data: {
      userId,
      totalAmount: finalAmount,
      paymentMethod,
      paymentAddress: wallet.address,
      couponCode,
      discountAmount,
      items: {
        create: orderItems
      }
    },
    include: {
      items: {
        include: {
          product: true
        }
      }
    }
  });

  res.status(201).json(createApiResponse(true, { order }, 'Order created successfully'));
}));

// Update order status (admin only)
router.put('/:id/status', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;
  const { status, transactionHash } = req.body;

  if (!Object.values(OrderStatus).includes(status)) {
    throw new AppError('Invalid order status', 400);
  }

  const order = await prisma.order.findUnique({
    where: { id },
    include: {
      items: {
        include: {
          product: true
        }
      },
      user: true
    }
  });

  if (!order) {
    throw new AppError('Order not found', 404);
  }

  const updateData: any = { status };
  if (transactionHash) {
    updateData.transactionHash = transactionHash;
  }

  const updatedOrder = await prisma.order.update({
    where: { id },
    data: updateData
  });

  // If order is marked as paid, create download links
  if (status === OrderStatus.PAID) {
    const downloads = order.items.map(item => ({
      userId: order.userId,
      productId: item.productId,
      downloadUrl: `/api/downloads/${item.productId}/${order.userId}`,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    }));

    await prisma.download.createMany({
      data: downloads
    });

    // Update order status to delivered
    await prisma.order.update({
      where: { id },
      data: { status: OrderStatus.DELIVERED }
    });
  }

  res.json(createApiResponse(true, { order: updatedOrder }, 'Order status updated successfully'));
}));

// Cancel order
router.put('/:id/cancel', authenticate, asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;

  const where: any = { id };

  // Non-admin users can only cancel their own orders
  if (req.user!.role !== UserRole.ADMIN) {
    where.userId = req.user!.id;
  }

  const order = await prisma.order.findFirst({
    where
  });

  if (!order) {
    throw new AppError('Order not found', 404);
  }

  if (order.status !== OrderStatus.PENDING) {
    throw new AppError('Only pending orders can be cancelled', 400);
  }

  const updatedOrder = await prisma.order.update({
    where: { id },
    data: { status: OrderStatus.CANCELLED }
  });

  res.json(createApiResponse(true, { order: updatedOrder }, 'Order cancelled successfully'));
}));

// Get order statistics (admin only)
router.get('/stats/overview', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req: AuthRequest, res) => {
  const [
    totalOrders,
    pendingOrders,
    paidOrders,
    totalRevenue,
    todayOrders,
    todayRevenue
  ] = await Promise.all([
    prisma.order.count(),
    prisma.order.count({ where: { status: OrderStatus.PENDING } }),
    prisma.order.count({ where: { status: OrderStatus.PAID } }),
    prisma.order.aggregate({
      where: { status: { in: [OrderStatus.PAID, OrderStatus.DELIVERED] } },
      _sum: { totalAmount: true }
    }),
    prisma.order.count({
      where: {
        createdAt: {
          gte: new Date(new Date().setHours(0, 0, 0, 0))
        }
      }
    }),
    prisma.order.aggregate({
      where: {
        status: { in: [OrderStatus.PAID, OrderStatus.DELIVERED] },
        createdAt: {
          gte: new Date(new Date().setHours(0, 0, 0, 0))
        }
      },
      _sum: { totalAmount: true }
    })
  ]);

  const stats = {
    totalOrders,
    pendingOrders,
    paidOrders,
    totalRevenue: totalRevenue._sum.totalAmount || 0,
    todayOrders,
    todayRevenue: todayRevenue._sum.totalAmount || 0
  };

  res.json(createApiResponse(true, { stats }));
}));

export default router;
