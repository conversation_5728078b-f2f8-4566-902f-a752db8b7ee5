import express from 'express';
import { PrismaClient } from '@prisma/client';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { validate, schemas } from '../middleware/validation';
import { asyncHandler } from '../middleware/errorHandler';
import { createApiResponse, AppError, UserRole, generateCouponCode, calculatePagination } from '@ecommerce/shared';

const router = express.Router();
const prisma = new PrismaClient();

// Get all coupons (admin only)
router.get('/', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req: AuthRequest, res) => {
  const {
    page = 1,
    limit = 10,
    isActive,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const offset = (pageNum - 1) * limitNum;

  const where: any = {};
  if (isActive !== undefined) {
    where.isActive = isActive === 'true';
  }

  const total = await prisma.coupon.count({ where });

  const coupons = await prisma.coupon.findMany({
    where,
    skip: offset,
    take: limitNum,
    orderBy: { [sortBy as string]: sortOrder }
  });

  const pagination = calculatePagination(pageNum, limitNum, total);

  res.json(createApiResponse(true, {
    coupons,
    pagination
  }));
}));

// Validate coupon (public)
router.post('/validate', asyncHandler(async (req, res) => {
  const { code, orderAmount } = req.body;

  if (!code) {
    throw new AppError('Coupon code is required', 400);
  }

  const coupon = await prisma.coupon.findFirst({
    where: {
      code: code.toUpperCase(),
      isActive: true,
      OR: [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } }
      ]
    }
  });

  if (!coupon) {
    throw new AppError('Invalid or expired coupon code', 400);
  }

  if (coupon.maxUses && coupon.usedCount >= coupon.maxUses) {
    throw new AppError('Coupon usage limit exceeded', 400);
  }

  if (coupon.minOrderAmount && orderAmount && orderAmount < coupon.minOrderAmount) {
    throw new AppError(`Minimum order amount for this coupon is ${coupon.minOrderAmount}`, 400);
  }

  res.json(createApiResponse(true, { coupon }, 'Coupon is valid'));
}));

// Create coupon (admin only)
router.post('/', authenticate, authorize(UserRole.ADMIN), validate(schemas.createCoupon), asyncHandler(async (req: AuthRequest, res) => {
  const couponData = req.body;

  // Generate code if not provided
  if (!couponData.code) {
    couponData.code = generateCouponCode();
  } else {
    couponData.code = couponData.code.toUpperCase();
  }

  // Check if code already exists
  const existingCoupon = await prisma.coupon.findUnique({
    where: { code: couponData.code }
  });

  if (existingCoupon) {
    throw new AppError('Coupon code already exists', 409);
  }

  const coupon = await prisma.coupon.create({
    data: couponData
  });

  res.status(201).json(createApiResponse(true, { coupon }, 'Coupon created successfully'));
}));

// Update coupon (admin only)
router.put('/:id', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;
  const updateData = req.body;

  if (updateData.code) {
    updateData.code = updateData.code.toUpperCase();
    
    // Check if new code already exists
    const existingCoupon = await prisma.coupon.findFirst({
      where: {
        code: updateData.code,
        id: { not: id }
      }
    });

    if (existingCoupon) {
      throw new AppError('Coupon code already exists', 409);
    }
  }

  const coupon = await prisma.coupon.update({
    where: { id },
    data: updateData
  });

  res.json(createApiResponse(true, { coupon }, 'Coupon updated successfully'));
}));

// Delete coupon (admin only)
router.delete('/:id', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;

  await prisma.coupon.delete({
    where: { id }
  });

  res.json(createApiResponse(true, null, 'Coupon deleted successfully'));
}));

// Generate random coupon code (admin only)
router.post('/generate-code', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req: AuthRequest, res) => {
  let code: string;
  let isUnique = false;

  // Generate unique code
  while (!isUnique) {
    code = generateCouponCode();
    const existingCoupon = await prisma.coupon.findUnique({
      where: { code }
    });
    if (!existingCoupon) {
      isUnique = true;
    }
  }

  res.json(createApiResponse(true, { code }, 'Coupon code generated'));
}));

export default router;
