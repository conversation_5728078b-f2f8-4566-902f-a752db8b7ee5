import express from 'express';
import path from 'path';
import fs from 'fs';
import { PrismaClient } from '@prisma/client';
import { authenticate, AuthRequest } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { createApiResponse, AppError, generateSecureToken } from '@ecommerce/shared';

const router = express.Router();
const prisma = new PrismaClient();

// Generate download link for purchased product
router.post('/generate/:productId', authenticate, asyncHandler(async (req: AuthRequest, res) => {
  const { productId } = req.params;
  const userId = req.user!.id;

  // Check if user has purchased this product
  const order = await prisma.order.findFirst({
    where: {
      userId,
      status: { in: ['PAID', 'DELIVERED'] },
      items: {
        some: {
          productId
        }
      }
    },
    include: {
      items: {
        where: { productId },
        include: { product: true }
      }
    }
  });

  if (!order) {
    throw new AppError('Product not purchased or order not paid', 403);
  }

  const product = order.items[0].product;

  // Check if product file exists
  if (!product.fileUrl || !fs.existsSync(path.join(process.env.UPLOAD_DIR || './uploads', product.fileUrl.replace('/uploads/', '')))) {
    throw new AppError('Product file not available', 404);
  }

  // Check existing download record
  let download = await prisma.download.findFirst({
    where: {
      userId,
      productId,
      expiresAt: { gt: new Date() }
    }
  });

  if (!download) {
    // Create new download record
    const downloadToken = generateSecureToken(32);
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    download = await prisma.download.create({
      data: {
        userId,
        productId,
        downloadUrl: `/api/downloads/file/${downloadToken}`,
        expiresAt
      }
    });
  }

  res.json(createApiResponse(true, {
    downloadUrl: download.downloadUrl,
    expiresAt: download.expiresAt,
    product: {
      id: product.id,
      name: product.name,
      fileName: product.fileName,
      fileSize: product.fileSize
    }
  }, 'Download link generated successfully'));
}));

// Download file with token
router.get('/file/:token', asyncHandler(async (req, res) => {
  const { token } = req.params;

  // Find download record by token
  const download = await prisma.download.findFirst({
    where: {
      downloadUrl: `/api/downloads/file/${token}`,
      expiresAt: { gt: new Date() }
    },
    include: {
      product: true,
      user: true
    }
  });

  if (!download) {
    throw new AppError('Download link expired or invalid', 404);
  }

  const product = download.product;
  const filePath = path.join(process.env.UPLOAD_DIR || './uploads', product.fileUrl.replace('/uploads/', ''));

  // Check if file exists
  if (!fs.existsSync(filePath)) {
    throw new AppError('File not found', 404);
  }

  // Set headers for file download
  res.setHeader('Content-Disposition', `attachment; filename="${product.fileName}"`);
  res.setHeader('Content-Type', 'application/octet-stream');
  res.setHeader('Content-Length', product.fileSize);

  // Stream file to response
  const fileStream = fs.createReadStream(filePath);
  fileStream.pipe(res);

  // Log download
  console.log(`File downloaded: ${product.name} by user ${download.user.email}`);
}));

// Get user's download history
router.get('/history', authenticate, asyncHandler(async (req: AuthRequest, res) => {
  const userId = req.user!.id;
  const { page = 1, limit = 10 } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const offset = (pageNum - 1) * limitNum;

  const downloads = await prisma.download.findMany({
    where: { userId },
    skip: offset,
    take: limitNum,
    orderBy: { createdAt: 'desc' },
    include: {
      product: {
        select: {
          id: true,
          name: true,
          fileName: true,
          fileSize: true,
          thumbnailUrl: true
        }
      }
    }
  });

  const total = await prisma.download.count({ where: { userId } });

  res.json(createApiResponse(true, {
    downloads,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      totalPages: Math.ceil(total / limitNum)
    }
  }));
}));

// Check download availability for product
router.get('/check/:productId', authenticate, asyncHandler(async (req: AuthRequest, res) => {
  const { productId } = req.params;
  const userId = req.user!.id;

  // Check if user has purchased this product
  const order = await prisma.order.findFirst({
    where: {
      userId,
      status: { in: ['PAID', 'DELIVERED'] },
      items: {
        some: { productId }
      }
    }
  });

  const isPurchased = !!order;

  // Check if download link exists and is valid
  const download = await prisma.download.findFirst({
    where: {
      userId,
      productId,
      expiresAt: { gt: new Date() }
    }
  });

  res.json(createApiResponse(true, {
    isPurchased,
    hasValidDownload: !!download,
    downloadUrl: download?.downloadUrl,
    expiresAt: download?.expiresAt
  }));
}));

export default router;
