import express from 'express';
import multer from 'multer';
import { PrismaClient } from '@prisma/client';
import { UserRole } from '@ecommerce/shared';
import { authenticate, authorize, AuthRequest, canManageStore } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { createApiResponse } from '../utils/response';
import { AppError } from '../utils/errors';
import { importProductsFromCSV, exportProductsToCSV, generateSampleCSV } from '../utils/csv';

const router = express.Router();
const prisma = new PrismaClient();

// Configure multer for CSV file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
      cb(null, true);
    } else {
      cb(new Error('Only CSV files are allowed'));
    }
  }
});

// All routes require authentication
router.use(authenticate);

// Get CSV sample template
router.get('/sample', asyncHandler(async (req: AuthRequest, res) => {
  const sampleCSV = generateSampleCSV();
  
  res.setHeader('Content-Type', 'text/csv');
  res.setHeader('Content-Disposition', 'attachment; filename="product-import-sample.csv"');
  res.send(sampleCSV);
}));

// Import products from CSV
router.post('/import/:storeId', 
  authorize(UserRole.ADMIN, UserRole.SELLER, UserRole.MANAGER),
  upload.single('csvFile'),
  asyncHandler(async (req: AuthRequest, res) => {
    const { storeId } = req.params;

    if (!req.file) {
      throw new AppError('CSV file is required', 400);
    }

    // Check permissions
    if (req.user!.role === UserRole.SELLER && req.user!.storeId !== storeId) {
      throw new AppError('Access denied. You can only import to your own store.', 403);
    }

    if (req.user!.role === UserRole.MANAGER) {
      const canManage = await canManageStore(req.user!.id, storeId);
      if (!canManage) {
        throw new AppError('Access denied. You can only import to stores you manage.', 403);
      }
    }

    // Verify store exists
    const store = await prisma.store.findUnique({
      where: { id: storeId }
    });

    if (!store) {
      throw new AppError('Store not found', 404);
    }

    try {
      const csvContent = req.file.buffer.toString('utf-8');
      const result = await importProductsFromCSV(csvContent, storeId);

      res.json(createApiResponse(true, result, 
        `Import completed. ${result.imported} products imported, ${result.failed} failed.`
      ));
    } catch (error) {
      throw new AppError(
        `CSV import failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        400
      );
    }
  })
);

// Export products to CSV
router.get('/export/:storeId', 
  authorize(UserRole.ADMIN, UserRole.SELLER, UserRole.MANAGER),
  asyncHandler(async (req: AuthRequest, res) => {
    const { storeId } = req.params;

    // Check permissions
    if (req.user!.role === UserRole.SELLER && req.user!.storeId !== storeId) {
      throw new AppError('Access denied. You can only export from your own store.', 403);
    }

    if (req.user!.role === UserRole.MANAGER) {
      const canManage = await canManageStore(req.user!.id, storeId);
      if (!canManage) {
        throw new AppError('Access denied. You can only export from stores you manage.', 403);
      }
    }

    // Verify store exists
    const store = await prisma.store.findUnique({
      where: { id: storeId },
      select: { id: true, name: true }
    });

    if (!store) {
      throw new AppError('Store not found', 404);
    }

    try {
      const csvContent = await exportProductsToCSV(storeId);
      const filename = `${store.name.replace(/[^a-zA-Z0-9]/g, '_')}_products_${new Date().toISOString().split('T')[0]}.csv`;

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.send(csvContent);
    } catch (error) {
      throw new AppError(
        `CSV export failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  })
);

// Get import/export guidelines
router.get('/guidelines', asyncHandler(async (req: AuthRequest, res) => {
  const guidelines = {
    importGuidelines: {
      fileFormat: 'CSV (Comma Separated Values)',
      maxFileSize: '5MB',
      encoding: 'UTF-8',
      requiredColumns: [
        'name',
        'description', 
        'price',
        'category'
      ],
      optionalColumns: [
        'currency',
        'tags',
        'downloadLimit',
        'fileUrl',
        'fileName'
      ],
      columnDescriptions: {
        name: 'Product name (1-255 characters)',
        description: 'Product description (1-1000 characters)',
        price: 'Product price (positive number)',
        currency: 'Currency code (3 letters, e.g., USD, EUR)',
        category: 'Product category (1-100 characters)',
        tags: 'Comma-separated tags (e.g., "digital, software, tools")',
        downloadLimit: 'Number of allowed downloads (1-100)',
        fileUrl: 'URL to the product file',
        fileName: 'Name of the product file'
      },
      validationRules: [
        'All required columns must be present',
        'Price must be a positive number',
        'Currency must be a valid 3-letter code (if provided)',
        'Download limit must be between 1 and 100 (if provided)',
        'Tags should be comma-separated',
        'Empty rows will be skipped'
      ],
      tips: [
        'Download the sample CSV template to see the correct format',
        'Ensure your CSV file uses UTF-8 encoding',
        'Remove any special characters that might cause parsing issues',
        'Test with a small batch first before importing large files',
        'File URLs should be accessible and point to actual files'
      ]
    },
    exportFeatures: {
      format: 'CSV with headers',
      includes: [
        'All product information',
        'Current status (active/inactive)',
        'Creation date',
        'All custom fields'
      ],
      sorting: 'Products are sorted by creation date (newest first)',
      filename: 'Auto-generated based on store name and export date'
    },
    supportedRoles: {
      [UserRole.ADMIN]: 'Can import/export for any store',
      [UserRole.SELLER]: 'Can import/export for their own store only',
      [UserRole.MANAGER]: 'Can import/export for assigned stores only',
      [UserRole.CUSTOMER]: 'No import/export access'
    }
  };

  res.json(createApiResponse(true, guidelines));
}));

// Validate CSV without importing
router.post('/validate/:storeId',
  authorize(UserRole.ADMIN, UserRole.SELLER, UserRole.MANAGER),
  upload.single('csvFile'),
  asyncHandler(async (req: AuthRequest, res) => {
    const { storeId } = req.params;

    if (!req.file) {
      throw new AppError('CSV file is required', 400);
    }

    // Check permissions (same as import)
    if (req.user!.role === UserRole.SELLER && req.user!.storeId !== storeId) {
      throw new AppError('Access denied. You can only validate for your own store.', 403);
    }

    if (req.user!.role === UserRole.MANAGER) {
      const canManage = await canManageStore(req.user!.id, storeId);
      if (!canManage) {
        throw new AppError('Access denied. You can only validate for stores you manage.', 403);
      }
    }

    try {
      const csvContent = req.file.buffer.toString('utf-8');
      
      // Parse and validate without saving
      const { parseCSV, validateCSVRow, PRODUCT_VALIDATION_RULES } = await import('../utils/csv');
      const records = await parseCSV(csvContent);
      
      let validRows = 0;
      let invalidRows = 0;
      const errors: any[] = [];

      for (let i = 0; i < records.length; i++) {
        const row = records[i];
        const rowIndex = i + 2;
        const rowErrors = validateCSVRow(row, rowIndex, PRODUCT_VALIDATION_RULES);
        
        if (rowErrors.length > 0) {
          errors.push(...rowErrors);
          invalidRows++;
        } else {
          validRows++;
        }
      }

      const result = {
        totalRows: records.length,
        validRows,
        invalidRows,
        errors: errors.slice(0, 50), // Limit to first 50 errors
        isValid: errors.length === 0,
        preview: records.slice(0, 5) // Show first 5 rows as preview
      };

      res.json(createApiResponse(true, result, 
        `Validation completed. ${validRows} valid rows, ${invalidRows} invalid rows.`
      ));
    } catch (error) {
      throw new AppError(
        `CSV validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        400
      );
    }
  })
);

export default router;
