import express from 'express';
import { PrismaClient } from '@prisma/client';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { validate, schemas } from '../middleware/validation';
import { asyncHandler } from '../middleware/errorHandler';
import { createApiResponse, AppError, UserRole, validateCryptoAddress } from '@ecommerce/shared';

const router = express.Router();
const prisma = new PrismaClient();

// Get all wallets (admin only)
router.get('/', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req: AuthRequest, res) => {
  const wallets = await prisma.cryptoWallet.findMany({
    orderBy: { createdAt: 'desc' }
  });

  res.json(createApiResponse(true, { wallets }));
}));

// Get active wallets (public - for payment options)
router.get('/active', asyncHandler(async (req, res) => {
  const wallets = await prisma.cryptoWallet.findMany({
    where: { isActive: true },
    select: {
      id: true,
      currency: true,
      address: true
    },
    orderBy: { currency: 'asc' }
  });

  res.json(createApiResponse(true, { wallets }));
}));

// Create wallet (admin only)
router.post('/', authenticate, authorize(UserRole.ADMIN), validate(schemas.createWallet), asyncHandler(async (req: AuthRequest, res) => {
  const { currency, address } = req.body;

  // Validate crypto address format
  if (!validateCryptoAddress(address, currency)) {
    throw new AppError(`Invalid ${currency} address format`, 400);
  }

  // Check if wallet already exists
  const existingWallet = await prisma.cryptoWallet.findUnique({
    where: { address }
  });

  if (existingWallet) {
    throw new AppError('Wallet address already exists', 409);
  }

  const wallet = await prisma.cryptoWallet.create({
    data: {
      currency,
      address
    }
  });

  res.status(201).json(createApiResponse(true, { wallet }, 'Wallet created successfully'));
}));

// Update wallet (admin only)
router.put('/:id', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;
  const { address, isActive } = req.body;

  const existingWallet = await prisma.cryptoWallet.findUnique({
    where: { id }
  });

  if (!existingWallet) {
    throw new AppError('Wallet not found', 404);
  }

  const updateData: any = {};

  if (address !== undefined) {
    // Validate new address format
    if (!validateCryptoAddress(address, existingWallet.currency)) {
      throw new AppError(`Invalid ${existingWallet.currency} address format`, 400);
    }

    // Check if new address already exists
    const duplicateWallet = await prisma.cryptoWallet.findFirst({
      where: {
        address,
        id: { not: id }
      }
    });

    if (duplicateWallet) {
      throw new AppError('Wallet address already exists', 409);
    }

    updateData.address = address;
  }

  if (isActive !== undefined) {
    updateData.isActive = isActive;
  }

  const wallet = await prisma.cryptoWallet.update({
    where: { id },
    data: updateData
  });

  res.json(createApiResponse(true, { wallet }, 'Wallet updated successfully'));
}));

// Delete wallet (admin only)
router.delete('/:id', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;

  const existingWallet = await prisma.cryptoWallet.findUnique({
    where: { id }
  });

  if (!existingWallet) {
    throw new AppError('Wallet not found', 404);
  }

  // Check if wallet is being used in any pending orders
  const pendingOrders = await prisma.order.count({
    where: {
      paymentAddress: existingWallet.address,
      status: 'PENDING'
    }
  });

  if (pendingOrders > 0) {
    throw new AppError('Cannot delete wallet with pending orders', 400);
  }

  await prisma.cryptoWallet.delete({
    where: { id }
  });

  res.json(createApiResponse(true, null, 'Wallet deleted successfully'));
}));

// Get wallet by currency (public)
router.get('/currency/:currency', asyncHandler(async (req, res) => {
  const { currency } = req.params;

  const wallet = await prisma.cryptoWallet.findFirst({
    where: {
      currency: currency.toUpperCase(),
      isActive: true
    },
    select: {
      id: true,
      currency: true,
      address: true
    }
  });

  if (!wallet) {
    throw new AppError(`No active wallet found for ${currency}`, 404);
  }

  res.json(createApiResponse(true, { wallet }));
}));

export default router;
