import express from 'express';
import { PrismaClient } from '@prisma/client';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { createApiResponse, UserRole, OrderStatus } from '@ecommerce/shared';

const router = express.Router();
const prisma = new PrismaClient();

// Get sales analytics (admin only)
router.get('/sales', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req: AuthRequest, res) => {
  const { period = '30' } = req.query;
  const days = parseInt(period as string);
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  // Total revenue and orders
  const [totalRevenue, totalOrders, totalCustomers] = await Promise.all([
    prisma.order.aggregate({
      where: {
        status: { in: [OrderStatus.PAID, OrderStatus.DELIVERED] },
        createdAt: { gte: startDate }
      },
      _sum: { totalAmount: true }
    }),
    prisma.order.count({
      where: {
        status: { in: [OrderStatus.PAID, OrderStatus.DELIVERED] },
        createdAt: { gte: startDate }
      }
    }),
    prisma.user.count({
      where: {
        role: UserRole.CUSTOMER,
        createdAt: { gte: startDate }
      }
    })
  ]);

  // Top products
  const topProducts = await prisma.orderItem.groupBy({
    by: ['productId'],
    where: {
      order: {
        status: { in: [OrderStatus.PAID, OrderStatus.DELIVERED] },
        createdAt: { gte: startDate }
      }
    },
    _sum: {
      quantity: true,
      price: true
    },
    orderBy: {
      _sum: {
        price: 'desc'
      }
    },
    take: 10
  });

  const topProductsWithDetails = await Promise.all(
    topProducts.map(async (item) => {
      const product = await prisma.product.findUnique({
        where: { id: item.productId },
        select: { id: true, name: true }
      });
      return {
        productId: item.productId,
        productName: product?.name || 'Unknown Product',
        totalSales: item._sum.quantity || 0,
        revenue: item._sum.price || 0
      };
    })
  );

  // Revenue by day
  const revenueByDay = await prisma.$queryRaw`
    SELECT 
      DATE(created_at) as date,
      SUM(total_amount) as revenue,
      COUNT(*) as orders
    FROM orders 
    WHERE status IN ('PAID', 'DELIVERED') 
      AND created_at >= ${startDate}
    GROUP BY DATE(created_at)
    ORDER BY date ASC
  ` as any[];

  // Orders by status
  const ordersByStatus = await prisma.order.groupBy({
    by: ['status'],
    where: {
      createdAt: { gte: startDate }
    },
    _count: {
      status: true
    }
  });

  const orderStatusCount = ordersByStatus.map(item => ({
    status: item.status,
    count: item._count.status
  }));

  const analytics = {
    totalRevenue: totalRevenue._sum.totalAmount || 0,
    totalOrders,
    totalCustomers,
    topProducts: topProductsWithDetails,
    revenueByDay: revenueByDay.map(item => ({
      date: item.date.toISOString().split('T')[0],
      revenue: parseFloat(item.revenue) || 0,
      orders: parseInt(item.orders) || 0
    })),
    ordersByStatus: orderStatusCount
  };

  res.json(createApiResponse(true, { analytics }));
}));

// Get product analytics (admin only)
router.get('/products', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req: AuthRequest, res) => {
  const { period = '30' } = req.query;
  const days = parseInt(period as string);
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  // Product performance
  const productStats = await prisma.product.findMany({
    select: {
      id: true,
      name: true,
      price: true,
      category: true,
      isActive: true,
      createdAt: true,
      _count: {
        select: {
          orderItems: {
            where: {
              order: {
                status: { in: [OrderStatus.PAID, OrderStatus.DELIVERED] },
                createdAt: { gte: startDate }
              }
            }
          }
        }
      }
    },
    orderBy: {
      orderItems: {
        _count: 'desc'
      }
    }
  });

  // Category performance
  const categoryStats = await prisma.orderItem.groupBy({
    by: ['productId'],
    where: {
      order: {
        status: { in: [OrderStatus.PAID, OrderStatus.DELIVERED] },
        createdAt: { gte: startDate }
      }
    },
    _sum: {
      quantity: true,
      price: true
    }
  });

  const categoryMap = new Map();
  
  for (const item of categoryStats) {
    const product = await prisma.product.findUnique({
      where: { id: item.productId },
      select: { category: true }
    });
    
    if (product) {
      const category = product.category;
      if (!categoryMap.has(category)) {
        categoryMap.set(category, { sales: 0, revenue: 0 });
      }
      const current = categoryMap.get(category);
      current.sales += item._sum.quantity || 0;
      current.revenue += item._sum.price || 0;
    }
  }

  const categoryPerformance = Array.from(categoryMap.entries()).map(([category, stats]) => ({
    category,
    sales: stats.sales,
    revenue: stats.revenue
  }));

  res.json(createApiResponse(true, {
    productStats,
    categoryPerformance
  }));
}));

// Get customer analytics (admin only)
router.get('/customers', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req: AuthRequest, res) => {
  const { period = '30' } = req.query;
  const days = parseInt(period as string);
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  // Customer registrations over time
  const registrations = await prisma.$queryRaw`
    SELECT 
      DATE(created_at) as date,
      COUNT(*) as registrations
    FROM users 
    WHERE role = 'CUSTOMER' 
      AND created_at >= ${startDate}
    GROUP BY DATE(created_at)
    ORDER BY date ASC
  ` as any[];

  // Top customers by spending
  const topCustomers = await prisma.order.groupBy({
    by: ['userId'],
    where: {
      status: { in: [OrderStatus.PAID, OrderStatus.DELIVERED] },
      createdAt: { gte: startDate }
    },
    _sum: {
      totalAmount: true
    },
    _count: {
      id: true
    },
    orderBy: {
      _sum: {
        totalAmount: 'desc'
      }
    },
    take: 10
  });

  const topCustomersWithDetails = await Promise.all(
    topCustomers.map(async (customer) => {
      const user = await prisma.user.findUnique({
        where: { id: customer.userId },
        select: { id: true, email: true, username: true }
      });
      return {
        userId: customer.userId,
        email: user?.email || 'Unknown',
        username: user?.username || 'Unknown',
        totalSpent: customer._sum.totalAmount || 0,
        orderCount: customer._count.id
      };
    })
  );

  res.json(createApiResponse(true, {
    registrations: registrations.map(item => ({
      date: item.date.toISOString().split('T')[0],
      registrations: parseInt(item.registrations) || 0
    })),
    topCustomers: topCustomersWithDetails
  }));
}));

// Get dashboard overview (admin only)
router.get('/dashboard', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req: AuthRequest, res) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const thisMonth = new Date();
  thisMonth.setDate(1);
  thisMonth.setHours(0, 0, 0, 0);

  const [
    // Today's stats
    todayRevenue,
    todayOrders,
    todayCustomers,
    
    // This month's stats
    monthRevenue,
    monthOrders,
    monthCustomers,
    
    // Overall stats
    totalProducts,
    totalActiveProducts,
    pendingOrders,
    
    // Recent orders
    recentOrders
  ] = await Promise.all([
    // Today
    prisma.order.aggregate({
      where: {
        status: { in: [OrderStatus.PAID, OrderStatus.DELIVERED] },
        createdAt: { gte: today }
      },
      _sum: { totalAmount: true }
    }),
    prisma.order.count({
      where: {
        status: { in: [OrderStatus.PAID, OrderStatus.DELIVERED] },
        createdAt: { gte: today }
      }
    }),
    prisma.user.count({
      where: {
        role: UserRole.CUSTOMER,
        createdAt: { gte: today }
      }
    }),
    
    // This month
    prisma.order.aggregate({
      where: {
        status: { in: [OrderStatus.PAID, OrderStatus.DELIVERED] },
        createdAt: { gte: thisMonth }
      },
      _sum: { totalAmount: true }
    }),
    prisma.order.count({
      where: {
        status: { in: [OrderStatus.PAID, OrderStatus.DELIVERED] },
        createdAt: { gte: thisMonth }
      }
    }),
    prisma.user.count({
      where: {
        role: UserRole.CUSTOMER,
        createdAt: { gte: thisMonth }
      }
    }),
    
    // Overall
    prisma.product.count(),
    prisma.product.count({ where: { isActive: true } }),
    prisma.order.count({ where: { status: OrderStatus.PENDING } }),
    
    // Recent orders
    prisma.order.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: { email: true, username: true }
        },
        items: {
          include: {
            product: {
              select: { name: true }
            }
          }
        }
      }
    })
  ]);

  const dashboard = {
    today: {
      revenue: todayRevenue._sum.totalAmount || 0,
      orders: todayOrders,
      customers: todayCustomers
    },
    thisMonth: {
      revenue: monthRevenue._sum.totalAmount || 0,
      orders: monthOrders,
      customers: monthCustomers
    },
    overall: {
      totalProducts,
      activeProducts: totalActiveProducts,
      pendingOrders
    },
    recentOrders
  };

  res.json(createApiResponse(true, { dashboard }));
}));

export default router;
