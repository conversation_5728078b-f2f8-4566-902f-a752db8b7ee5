import express from 'express';
import { PrismaClient } from '@prisma/client';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { validate, schemas } from '../middleware/validation';
import { asyncHandler } from '../middleware/errorHandler';
import { createApiResponse, AppError, UserRole, calculatePagination } from '@ecommerce/shared';

const router = express.Router();
const prisma = new PrismaClient();

// Get all users (admin only)
router.get('/', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req: AuthRequest, res) => {
  const {
    page = 1,
    limit = 10,
    role,
    isActive,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const offset = (pageNum - 1) * limitNum;

  const where: any = {};

  if (role) {
    where.role = role;
  }

  if (isActive !== undefined) {
    where.isActive = isActive === 'true';
  }

  if (search) {
    where.OR = [
      { email: { contains: search as string, mode: 'insensitive' } },
      { username: { contains: search as string, mode: 'insensitive' } }
    ];
  }

  const total = await prisma.user.count({ where });

  const users = await prisma.user.findMany({
    where,
    skip: offset,
    take: limitNum,
    orderBy: { [sortBy as string]: sortOrder },
    select: {
      id: true,
      email: true,
      username: true,
      telegramId: true,
      role: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
      _count: {
        select: {
          orders: true
        }
      }
    }
  });

  const pagination = calculatePagination(pageNum, limitNum, total);

  res.json(createApiResponse(true, {
    users,
    pagination
  }));
}));

// Get single user (admin only or own profile)
router.get('/:id', authenticate, asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;

  // Users can only access their own profile unless they're admin
  if (req.user!.role !== UserRole.ADMIN && req.user!.id !== id) {
    throw new AppError('Access denied', 403);
  }

  const user = await prisma.user.findUnique({
    where: { id },
    select: {
      id: true,
      email: true,
      username: true,
      telegramId: true,
      role: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
      _count: {
        select: {
          orders: true,
          downloads: true
        }
      }
    }
  });

  if (!user) {
    throw new AppError('User not found', 404);
  }

  res.json(createApiResponse(true, { user }));
}));

// Update user (admin only or own profile)
router.put('/:id', authenticate, validate(schemas.updateUser), asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;
  const updateData = req.body;

  // Users can only update their own profile unless they're admin
  if (req.user!.role !== UserRole.ADMIN && req.user!.id !== id) {
    throw new AppError('Access denied', 403);
  }

  // Non-admin users cannot change role or isActive status
  if (req.user!.role !== UserRole.ADMIN) {
    delete updateData.role;
    delete updateData.isActive;
  }

  // Check if email is already taken
  if (updateData.email) {
    const existingUser = await prisma.user.findFirst({
      where: {
        email: updateData.email,
        id: { not: id }
      }
    });

    if (existingUser) {
      throw new AppError('Email already in use', 409);
    }
  }

  const user = await prisma.user.update({
    where: { id },
    data: updateData,
    select: {
      id: true,
      email: true,
      username: true,
      telegramId: true,
      role: true,
      isActive: true,
      createdAt: true,
      updatedAt: true
    }
  });

  res.json(createApiResponse(true, { user }, 'User updated successfully'));
}));

// Delete user (admin only)
router.delete('/:id', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;

  // Prevent admin from deleting themselves
  if (req.user!.id === id) {
    throw new AppError('Cannot delete your own account', 400);
  }

  const user = await prisma.user.findUnique({
    where: { id }
  });

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Check if user has any orders
  const orderCount = await prisma.order.count({
    where: { userId: id }
  });

  if (orderCount > 0) {
    throw new AppError('Cannot delete user with existing orders', 400);
  }

  await prisma.user.delete({
    where: { id }
  });

  res.json(createApiResponse(true, null, 'User deleted successfully'));
}));

// Get user statistics (admin only)
router.get('/stats/overview', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req: AuthRequest, res) => {
  const [
    totalUsers,
    activeUsers,
    adminUsers,
    customerUsers,
    todayRegistrations
  ] = await Promise.all([
    prisma.user.count(),
    prisma.user.count({ where: { isActive: true } }),
    prisma.user.count({ where: { role: UserRole.ADMIN } }),
    prisma.user.count({ where: { role: UserRole.CUSTOMER } }),
    prisma.user.count({
      where: {
        createdAt: {
          gte: new Date(new Date().setHours(0, 0, 0, 0))
        }
      }
    })
  ]);

  const stats = {
    totalUsers,
    activeUsers,
    adminUsers,
    customerUsers,
    todayRegistrations
  };

  res.json(createApiResponse(true, { stats }));
}));

// Link Telegram account
router.post('/:id/link-telegram', authenticate, asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;
  const { telegramId } = req.body;

  // Users can only link their own account unless they're admin
  if (req.user!.role !== UserRole.ADMIN && req.user!.id !== id) {
    throw new AppError('Access denied', 403);
  }

  if (!telegramId) {
    throw new AppError('Telegram ID is required', 400);
  }

  // Check if Telegram ID is already linked to another account
  const existingUser = await prisma.user.findFirst({
    where: {
      telegramId: telegramId.toString(),
      id: { not: id }
    }
  });

  if (existingUser) {
    throw new AppError('Telegram account already linked to another user', 409);
  }

  const user = await prisma.user.update({
    where: { id },
    data: { telegramId: telegramId.toString() },
    select: {
      id: true,
      email: true,
      username: true,
      telegramId: true,
      role: true,
      isActive: true
    }
  });

  res.json(createApiResponse(true, { user }, 'Telegram account linked successfully'));
}));

export default router;
