import express from 'express';
import bcrypt from 'bcryptjs';
import { PrismaClient, UserRole as PrismaUserRole } from '@prisma/client';
import { UserRole, CreateUserRequest, UpdateUserRequest } from '@ecommerce/shared';
import { authenticate, authorize, auditLog, AuthRequest } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { createApiResponse } from '../utils/response';
import { AppError } from '../utils/errors';

const router = express.Router();
const prisma = new PrismaClient();

// All seller routes require seller authentication
router.use(authenticate);
router.use(authorize(UserRole.SELLER));

// Get seller's store information
router.get('/store', asyncHandler(async (req: AuthRequest, res) => {
  if (!req.user?.storeId) {
    throw new AppError('No store associated with this seller account', 404);
  }

  const store = await prisma.store.findUnique({
    where: { id: req.user.storeId },
    include: {
      _count: {
        select: { products: true, managers: true }
      }
    }
  });

  if (!store) {
    throw new AppError('Store not found', 404);
  }

  res.json(createApiResponse(true, { store }));
}));

// Update store information
router.put('/store', auditLog('STORE_UPDATED'), asyncHandler(async (req: AuthRequest, res) => {
  if (!req.user?.storeId) {
    throw new AppError('No store associated with this seller account', 404);
  }

  const { name, description, settings } = req.body;

  const store = await prisma.store.update({
    where: { id: req.user.storeId },
    data: {
      name,
      description,
      settings
    }
  });

  res.json(createApiResponse(true, { store }, 'Store updated successfully'));
}));

// Get all managers for the seller's store
router.get('/managers', asyncHandler(async (req: AuthRequest, res) => {
  if (!req.user?.storeId) {
    throw new AppError('No store associated with this seller account', 404);
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const search = req.query.search as string;
  const isActive = req.query.isActive === 'true' ? true : req.query.isActive === 'false' ? false : undefined;

  const skip = (page - 1) * limit;

  const where: any = {
    storeId: req.user.storeId
  };

  if (search) {
    where.user = {
      OR: [
        { email: { contains: search, mode: 'insensitive' } },
        { username: { contains: search, mode: 'insensitive' } }
      ]
    };
  }

  if (isActive !== undefined) {
    where.user = {
      ...where.user,
      isActive
    };
  }

  const [userStores, total] = await Promise.all([
    prisma.userStore.findMany({
      where,
      skip,
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            username: true,
            role: true,
            isActive: true,
            isBanned: true,
            createdAt: true,
            _count: {
              select: { orders: true }
            }
          }
        }
      },
      orderBy: { user: { createdAt: 'desc' } }
    }),
    prisma.userStore.count({ where })
  ]);

  const managers = userStores.map(us => us.user);

  res.json(createApiResponse(true, {
    managers,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  }));
}));

// Add a new manager to the store
router.post('/managers', auditLog('MANAGER_CREATED'), asyncHandler(async (req: AuthRequest, res) => {
  if (!req.user?.storeId) {
    throw new AppError('No store associated with this seller account', 404);
  }

  const { email, username, password }: CreateUserRequest = req.body;

  if (!email) {
    throw new AppError('Email is required', 400);
  }

  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email }
  });

  if (existingUser) {
    throw new AppError('User with this email already exists', 409);
  }

  // Hash password if provided
  let hashedPassword;
  if (password) {
    hashedPassword = await bcrypt.hash(password, 12);
  }

  // Create manager user
  const manager = await prisma.user.create({
    data: {
      email,
      username,
      password: hashedPassword,
      role: PrismaUserRole.MANAGER
    },
    select: {
      id: true,
      email: true,
      username: true,
      role: true,
      isActive: true,
      createdAt: true
    }
  });

  // Assign manager to store
  await prisma.userStore.create({
    data: {
      userId: manager.id,
      storeId: req.user.storeId
    }
  });

  res.status(201).json(createApiResponse(true, { manager }, 'Manager created successfully'));
}));

// Update manager
router.put('/managers/:id', auditLog('MANAGER_UPDATED'), asyncHandler(async (req: AuthRequest, res) => {
  if (!req.user?.storeId) {
    throw new AppError('No store associated with this seller account', 404);
  }

  const { id } = req.params;
  const updates: UpdateUserRequest = req.body;

  // Check if manager belongs to this store
  const userStore = await prisma.userStore.findUnique({
    where: {
      userId_storeId: {
        userId: id,
        storeId: req.user.storeId
      }
    }
  });

  if (!userStore) {
    throw new AppError('Manager not found in your store', 404);
  }

  const manager = await prisma.user.update({
    where: { id },
    data: updates,
    select: {
      id: true,
      email: true,
      username: true,
      role: true,
      isActive: true,
      isBanned: true,
      updatedAt: true
    }
  });

  res.json(createApiResponse(true, { manager }, 'Manager updated successfully'));
}));

// Ban/unban manager
router.patch('/managers/:id/ban', auditLog('MANAGER_BAN_TOGGLE'), asyncHandler(async (req: AuthRequest, res) => {
  if (!req.user?.storeId) {
    throw new AppError('No store associated with this seller account', 404);
  }

  const { id } = req.params;
  const { isBanned } = req.body;

  // Check if manager belongs to this store
  const userStore = await prisma.userStore.findUnique({
    where: {
      userId_storeId: {
        userId: id,
        storeId: req.user.storeId
      }
    }
  });

  if (!userStore) {
    throw new AppError('Manager not found in your store', 404);
  }

  const manager = await prisma.user.update({
    where: { id },
    data: { isBanned },
    select: {
      id: true,
      email: true,
      isBanned: true
    }
  });

  res.json(createApiResponse(true, { manager }, `Manager ${isBanned ? 'banned' : 'unbanned'} successfully`));
}));

// Activate/deactivate manager
router.patch('/managers/:id/activate', auditLog('MANAGER_ACTIVATION_TOGGLE'), asyncHandler(async (req: AuthRequest, res) => {
  if (!req.user?.storeId) {
    throw new AppError('No store associated with this seller account', 404);
  }

  const { id } = req.params;
  const { isActive } = req.body;

  // Check if manager belongs to this store
  const userStore = await prisma.userStore.findUnique({
    where: {
      userId_storeId: {
        userId: id,
        storeId: req.user.storeId
      }
    }
  });

  if (!userStore) {
    throw new AppError('Manager not found in your store', 404);
  }

  const manager = await prisma.user.update({
    where: { id },
    data: { isActive },
    select: {
      id: true,
      email: true,
      isActive: true
    }
  });

  res.json(createApiResponse(true, { manager }, `Manager ${isActive ? 'activated' : 'deactivated'} successfully`));
}));

// Remove manager from store (soft delete - removes association but keeps user)
router.delete('/managers/:id', auditLog('MANAGER_REMOVED'), asyncHandler(async (req: AuthRequest, res) => {
  if (!req.user?.storeId) {
    throw new AppError('No store associated with this seller account', 404);
  }

  const { id } = req.params;

  // Check if manager belongs to this store
  const userStore = await prisma.userStore.findUnique({
    where: {
      userId_storeId: {
        userId: id,
        storeId: req.user.storeId
      }
    }
  });

  if (!userStore) {
    throw new AppError('Manager not found in your store', 404);
  }

  // Remove association
  await prisma.userStore.delete({
    where: {
      userId_storeId: {
        userId: id,
        storeId: req.user.storeId
      }
    }
  });

  res.json(createApiResponse(true, null, 'Manager removed from store successfully'));
}));

export default router;
