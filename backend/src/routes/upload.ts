import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { PrismaClient } from '@prisma/client';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { createApiResponse, AppError, UserRole, generateUniqueFilename, isValidFileType, formatFileSize } from '@ecommerce/shared';

const router = express.Router();
const prisma = new PrismaClient();

// Ensure upload directory exists
const uploadDir = process.env.UPLOAD_DIR || './uploads';
const productsDir = path.join(uploadDir, 'products');
const previewsDir = path.join(uploadDir, 'previews');
const thumbnailsDir = path.join(uploadDir, 'thumbnails');

[uploadDir, productsDir, previewsDir, thumbnailsDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let uploadPath = productsDir;
    
    if (file.fieldname === 'preview') {
      uploadPath = previewsDir;
    } else if (file.fieldname === 'thumbnail') {
      uploadPath = thumbnailsDir;
    }
    
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueName = generateUniqueFilename(file.originalname);
    cb(null, uniqueName);
  }
});

const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedProductTypes = ['pdf', 'zip', 'rar', '7z', 'exe', 'dmg', 'pkg', 'deb', 'rpm', 'msi'];
  const allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
  
  if (file.fieldname === 'product') {
    if (isValidFileType(file.originalname, allowedProductTypes)) {
      cb(null, true);
    } else {
      cb(new Error(`Invalid file type. Allowed types: ${allowedProductTypes.join(', ')}`));
    }
  } else if (file.fieldname === 'preview' || file.fieldname === 'thumbnail') {
    if (isValidFileType(file.originalname, allowedImageTypes)) {
      cb(null, true);
    } else {
      cb(new Error(`Invalid image type. Allowed types: ${allowedImageTypes.join(', ')}`));
    }
  } else {
    cb(new Error('Invalid field name'));
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '104857600') // 100MB default
  }
});

// Upload product file
router.post('/product/:productId', 
  authenticate, 
  authorize(UserRole.ADMIN),
  upload.single('product'),
  asyncHandler(async (req: AuthRequest, res) => {
    const { productId } = req.params;
    const file = req.file;

    if (!file) {
      throw new AppError('No file uploaded', 400);
    }

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: productId }
    });

    if (!product) {
      // Clean up uploaded file
      fs.unlinkSync(file.path);
      throw new AppError('Product not found', 404);
    }

    // Delete old file if exists
    if (product.fileUrl) {
      const oldFilePath = path.join(uploadDir, product.fileUrl.replace('/uploads/', ''));
      if (fs.existsSync(oldFilePath)) {
        fs.unlinkSync(oldFilePath);
      }
    }

    // Update product with file information
    const fileUrl = `/uploads/products/${file.filename}`;
    
    const updatedProduct = await prisma.product.update({
      where: { id: productId },
      data: {
        fileUrl,
        fileName: file.originalname,
        fileSize: file.size
      }
    });

    res.json(createApiResponse(true, {
      product: updatedProduct,
      file: {
        originalName: file.originalname,
        filename: file.filename,
        size: file.size,
        sizeFormatted: formatFileSize(file.size),
        url: fileUrl
      }
    }, 'Product file uploaded successfully'));
  })
);

// Upload preview image
router.post('/preview/:productId',
  authenticate,
  authorize(UserRole.ADMIN),
  upload.single('preview'),
  asyncHandler(async (req: AuthRequest, res) => {
    const { productId } = req.params;
    const file = req.file;

    if (!file) {
      throw new AppError('No file uploaded', 400);
    }

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: productId }
    });

    if (!product) {
      // Clean up uploaded file
      fs.unlinkSync(file.path);
      throw new AppError('Product not found', 404);
    }

    // Delete old preview if exists
    if (product.previewUrl) {
      const oldFilePath = path.join(uploadDir, product.previewUrl.replace('/uploads/', ''));
      if (fs.existsSync(oldFilePath)) {
        fs.unlinkSync(oldFilePath);
      }
    }

    // Update product with preview URL
    const previewUrl = `/uploads/previews/${file.filename}`;
    
    const updatedProduct = await prisma.product.update({
      where: { id: productId },
      data: { previewUrl }
    });

    res.json(createApiResponse(true, {
      product: updatedProduct,
      preview: {
        originalName: file.originalname,
        filename: file.filename,
        size: file.size,
        url: previewUrl
      }
    }, 'Preview image uploaded successfully'));
  })
);

// Upload thumbnail image
router.post('/thumbnail/:productId',
  authenticate,
  authorize(UserRole.ADMIN),
  upload.single('thumbnail'),
  asyncHandler(async (req: AuthRequest, res) => {
    const { productId } = req.params;
    const file = req.file;

    if (!file) {
      throw new AppError('No file uploaded', 400);
    }

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: productId }
    });

    if (!product) {
      // Clean up uploaded file
      fs.unlinkSync(file.path);
      throw new AppError('Product not found', 404);
    }

    // Delete old thumbnail if exists
    if (product.thumbnailUrl) {
      const oldFilePath = path.join(uploadDir, product.thumbnailUrl.replace('/uploads/', ''));
      if (fs.existsSync(oldFilePath)) {
        fs.unlinkSync(oldFilePath);
      }
    }

    // Update product with thumbnail URL
    const thumbnailUrl = `/uploads/thumbnails/${file.filename}`;
    
    const updatedProduct = await prisma.product.update({
      where: { id: productId },
      data: { thumbnailUrl }
    });

    res.json(createApiResponse(true, {
      product: updatedProduct,
      thumbnail: {
        originalName: file.originalname,
        filename: file.filename,
        size: file.size,
        url: thumbnailUrl
      }
    }, 'Thumbnail uploaded successfully'));
  })
);

// Delete file
router.delete('/:productId/:fileType',
  authenticate,
  authorize(UserRole.ADMIN),
  asyncHandler(async (req: AuthRequest, res) => {
    const { productId, fileType } = req.params;

    if (!['product', 'preview', 'thumbnail'].includes(fileType)) {
      throw new AppError('Invalid file type', 400);
    }

    const product = await prisma.product.findUnique({
      where: { id: productId }
    });

    if (!product) {
      throw new AppError('Product not found', 404);
    }

    let fileUrl: string | null = null;
    let updateData: any = {};

    switch (fileType) {
      case 'product':
        fileUrl = product.fileUrl;
        updateData = { fileUrl: '', fileName: '', fileSize: 0 };
        break;
      case 'preview':
        fileUrl = product.previewUrl;
        updateData = { previewUrl: null };
        break;
      case 'thumbnail':
        fileUrl = product.thumbnailUrl;
        updateData = { thumbnailUrl: null };
        break;
    }

    if (fileUrl) {
      const filePath = path.join(uploadDir, fileUrl.replace('/uploads/', ''));
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }

    await prisma.product.update({
      where: { id: productId },
      data: updateData
    });

    res.json(createApiResponse(true, null, `${fileType} file deleted successfully`));
  })
);

export default router;
