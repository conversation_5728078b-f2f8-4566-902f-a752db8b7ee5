import express from 'express';
import { PrismaClient } from '@prisma/client';
import { authenticate, authorize, optionalAuth, AuthRequest } from '../middleware/auth';
import { validate, schemas } from '../middleware/validation';
import { asyncHandler } from '../middleware/errorHandler';
import { createApiResponse, AppError, UserRole, calculatePagination } from '@ecommerce/shared';

const router = express.Router();
const prisma = new PrismaClient();

// Get all products (public with optional auth for admin features)
router.get('/', optionalAuth, asyncHandler(async (req: AuthRequest, res) => {
  const {
    page = 1,
    limit = 10,
    category,
    search,
    tags,
    minPrice,
    maxPrice,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const offset = (pageNum - 1) * limitNum;

  // Build where clause
  const where: any = {};

  // Only show active products for non-admin users
  if (!req.user || req.user.role !== UserRole.ADMIN) {
    where.isActive = true;
  }

  if (category) {
    where.category = category;
  }

  if (search) {
    where.OR = [
      { name: { contains: search as string, mode: 'insensitive' } },
      { description: { contains: search as string, mode: 'insensitive' } }
    ];
  }

  if (tags) {
    const tagArray = Array.isArray(tags) ? tags : [tags];
    where.tags = { hasSome: tagArray };
  }

  if (minPrice || maxPrice) {
    where.price = {};
    if (minPrice) where.price.gte = parseFloat(minPrice as string);
    if (maxPrice) where.price.lte = parseFloat(maxPrice as string);
  }

  // Get total count
  const total = await prisma.product.count({ where });

  // Get products
  const products = await prisma.product.findMany({
    where,
    skip: offset,
    take: limitNum,
    orderBy: { [sortBy as string]: sortOrder },
    select: {
      id: true,
      name: true,
      description: true,
      price: true,
      currency: true,
      category: true,
      tags: true,
      isActive: true,
      fileSize: true,
      downloadLimit: true,
      previewUrl: true,
      thumbnailUrl: true,
      createdAt: true,
      updatedAt: true,
      // Only include file details for admin users
      ...(req.user?.role === UserRole.ADMIN && {
        fileUrl: true,
        fileName: true
      })
    }
  });

  const pagination = calculatePagination(pageNum, limitNum, total);

  res.json(createApiResponse(true, {
    products,
    pagination
  }));
}));

// Get single product
router.get('/:id', optionalAuth, asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;

  const where: any = { id };

  // Only show active products for non-admin users
  if (!req.user || req.user.role !== UserRole.ADMIN) {
    where.isActive = true;
  }

  const product = await prisma.product.findFirst({
    where,
    select: {
      id: true,
      name: true,
      description: true,
      price: true,
      currency: true,
      category: true,
      tags: true,
      isActive: true,
      fileSize: true,
      downloadLimit: true,
      previewUrl: true,
      thumbnailUrl: true,
      createdAt: true,
      updatedAt: true,
      // Only include file details for admin users
      ...(req.user?.role === UserRole.ADMIN && {
        fileUrl: true,
        fileName: true
      })
    }
  });

  if (!product) {
    throw new AppError('Product not found', 404);
  }

  res.json(createApiResponse(true, { product }));
}));

// Create product (admin only)
router.post('/', authenticate, authorize(UserRole.ADMIN), validate(schemas.createProduct), asyncHandler(async (req: AuthRequest, res) => {
  const productData = req.body;

  const product = await prisma.product.create({
    data: {
      ...productData,
      fileUrl: '', // Will be updated when file is uploaded
      fileName: '',
      fileSize: 0
    }
  });

  res.status(201).json(createApiResponse(true, { product }, 'Product created successfully'));
}));

// Update product (admin only)
router.put('/:id', authenticate, authorize(UserRole.ADMIN), validate(schemas.updateProduct), asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;
  const updateData = req.body;

  const existingProduct = await prisma.product.findUnique({
    where: { id }
  });

  if (!existingProduct) {
    throw new AppError('Product not found', 404);
  }

  const product = await prisma.product.update({
    where: { id },
    data: updateData
  });

  res.json(createApiResponse(true, { product }, 'Product updated successfully'));
}));

// Delete product (admin only)
router.delete('/:id', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;

  const existingProduct = await prisma.product.findUnique({
    where: { id }
  });

  if (!existingProduct) {
    throw new AppError('Product not found', 404);
  }

  await prisma.product.delete({
    where: { id }
  });

  res.json(createApiResponse(true, null, 'Product deleted successfully'));
}));

// Get product categories
router.get('/meta/categories', asyncHandler(async (req, res) => {
  const categories = await prisma.product.findMany({
    where: { isActive: true },
    select: { category: true },
    distinct: ['category']
  });

  const categoryList = categories.map(c => c.category);

  res.json(createApiResponse(true, { categories: categoryList }));
}));

// Get product tags
router.get('/meta/tags', asyncHandler(async (req, res) => {
  const products = await prisma.product.findMany({
    where: { isActive: true },
    select: { tags: true }
  });

  const allTags = products.flatMap(p => p.tags);
  const uniqueTags = [...new Set(allTags)];

  res.json(createApiResponse(true, { tags: uniqueTags }));
}));

export default router;
