import express from 'express';
import { PrismaClient } from '@prisma/client';
import { authenticate, authorize, optionalAuth, AuthRequest, canManageStore } from '../middleware/auth';
import { validate, schemas } from '../middleware/validation';
import { asyncHandler } from '../middleware/errorHandler';
import { createApiResponse } from '../utils/response';
import { AppError } from '../utils/errors';
import { UserRole } from '@ecommerce/shared';

const router = express.Router();
const prisma = new PrismaClient();

// Get all products (public with optional auth for admin features)
router.get('/', optionalAuth, asyncHandler(async (req: AuthRequest, res) => {
  const {
    page = 1,
    limit = 10,
    category,
    search,
    tags,
    minPrice,
    maxPrice,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    storeId
  } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const offset = (pageNum - 1) * limitNum;

  // Build where clause
  const where: any = {};

  // Only show active products for non-admin users
  if (!req.user || req.user.role !== UserRole.ADMIN) {
    where.isActive = true;
  }

  // Filter by store if specified
  if (storeId) {
    where.storeId = storeId;
  }

  if (category) {
    where.category = category;
  }

  if (search) {
    where.OR = [
      { name: { contains: search as string, mode: 'insensitive' } },
      { description: { contains: search as string, mode: 'insensitive' } }
    ];
  }

  if (tags) {
    const tagArray = Array.isArray(tags) ? tags : [tags];
    where.tags = { hasSome: tagArray };
  }

  if (minPrice || maxPrice) {
    where.price = {};
    if (minPrice) where.price.gte = parseFloat(minPrice as string);
    if (maxPrice) where.price.lte = parseFloat(maxPrice as string);
  }

  // Get total count
  const total = await prisma.product.count({ where });

  // Get products
  const products = await prisma.product.findMany({
    where,
    skip: offset,
    take: limitNum,
    orderBy: { [sortBy as string]: sortOrder },
    select: {
      id: true,
      name: true,
      description: true,
      price: true,
      currency: true,
      category: true,
      tags: true,
      isActive: true,
      fileSize: true,
      downloadLimit: true,
      previewUrl: true,
      thumbnailUrl: true,
      storeId: true,
      createdAt: true,
      updatedAt: true,
      store: {
        select: { id: true, name: true }
      },
      // Only include file details for admin users
      ...(req.user?.role === UserRole.ADMIN && {
        fileUrl: true,
        fileName: true
      })
    }
  });

  const pagination = {
    page: pageNum,
    limit: limitNum,
    total,
    totalPages: Math.ceil(total / limitNum)
  };

  res.json(createApiResponse(true, {
    products,
    pagination
  }));
}));

// Get single product
router.get('/:id', optionalAuth, asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;

  const where: any = { id };

  // Only show active products for non-admin users
  if (!req.user || req.user.role !== UserRole.ADMIN) {
    where.isActive = true;
  }

  const product = await prisma.product.findFirst({
    where,
    select: {
      id: true,
      name: true,
      description: true,
      price: true,
      currency: true,
      category: true,
      tags: true,
      isActive: true,
      fileSize: true,
      downloadLimit: true,
      previewUrl: true,
      thumbnailUrl: true,
      createdAt: true,
      updatedAt: true,
      // Only include file details for admin users
      ...(req.user?.role === UserRole.ADMIN && {
        fileUrl: true,
        fileName: true
      })
    }
  });

  if (!product) {
    throw new AppError('Product not found', 404);
  }

  res.json(createApiResponse(true, { product }));
}));

// Create product (admin, seller, or manager)
router.post('/', authenticate, authorize(UserRole.ADMIN, UserRole.SELLER, UserRole.MANAGER), asyncHandler(async (req: AuthRequest, res) => {
  const { storeId, ...productData } = req.body;

  if (!storeId) {
    throw new AppError('Store ID is required', 400);
  }

  // Check permissions
  if (req.user!.role === UserRole.SELLER && req.user!.storeId !== storeId) {
    throw new AppError('Access denied. You can only create products in your own store.', 403);
  }

  if (req.user!.role === UserRole.MANAGER) {
    const canManage = await canManageStore(req.user!.id, storeId);
    if (!canManage) {
      throw new AppError('Access denied. You can only create products in stores you manage.', 403);
    }
  }

  // Verify store exists
  const store = await prisma.store.findUnique({
    where: { id: storeId }
  });

  if (!store) {
    throw new AppError('Store not found', 404);
  }

  const product = await prisma.product.create({
    data: {
      ...productData,
      storeId,
      fileUrl: '', // Will be updated when file is uploaded
      fileName: '',
      fileSize: 0
    },
    include: {
      store: {
        select: { id: true, name: true }
      }
    }
  });

  res.status(201).json(createApiResponse(true, { product }, 'Product created successfully'));
}));

// Update product (admin, seller, or manager with restrictions)
router.put('/:id', authenticate, authorize(UserRole.ADMIN, UserRole.SELLER), asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;
  const updateData = req.body;

  const existingProduct = await prisma.product.findUnique({
    where: { id },
    include: {
      store: true
    }
  });

  if (!existingProduct) {
    throw new AppError('Product not found', 404);
  }

  // Check permissions
  if (req.user!.role === UserRole.SELLER && req.user!.storeId !== existingProduct.storeId) {
    throw new AppError('Access denied. You can only update products in your own store.', 403);
  }

  const product = await prisma.product.update({
    where: { id },
    data: updateData,
    include: {
      store: {
        select: { id: true, name: true }
      }
    }
  });

  res.json(createApiResponse(true, { product }, 'Product updated successfully'));
}));

// Delete product (admin and seller only - managers cannot delete)
router.delete('/:id', authenticate, authorize(UserRole.ADMIN, UserRole.SELLER), asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;

  const existingProduct = await prisma.product.findUnique({
    where: { id },
    include: {
      store: true
    }
  });

  if (!existingProduct) {
    throw new AppError('Product not found', 404);
  }

  // Check permissions
  if (req.user!.role === UserRole.SELLER && req.user!.storeId !== existingProduct.storeId) {
    throw new AppError('Access denied. You can only delete products from your own store.', 403);
  }

  await prisma.product.delete({
    where: { id }
  });

  res.json(createApiResponse(true, null, 'Product deleted successfully'));
}));

// Get product categories
router.get('/meta/categories', asyncHandler(async (req, res) => {
  const categories = await prisma.product.findMany({
    where: { isActive: true },
    select: { category: true },
    distinct: ['category']
  });

  const categoryList = categories.map(c => c.category);

  res.json(createApiResponse(true, { categories: categoryList }));
}));

// Get product tags
router.get('/meta/tags', asyncHandler(async (req, res) => {
  const products = await prisma.product.findMany({
    where: { isActive: true },
    select: { tags: true }
  });

  const allTags = products.flatMap(p => p.tags);
  const uniqueTags = [...new Set(allTags)];

  res.json(createApiResponse(true, { tags: uniqueTags }));
}));

export default router;
