import express from 'express';
import { asyncHandler } from '../middleware/errorHandler';

const router = express.Router();

// Telegram webhook endpoint
router.post('/', asyncHandler(async (req, res) => {
  // This will be handled by the Telegram bot service
  // For now, just acknowledge the webhook
  console.log('Telegram webhook received:', req.body);
  res.status(200).json({ ok: true });
}));

export default router;
