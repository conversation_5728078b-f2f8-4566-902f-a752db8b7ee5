import express from 'express';
import bcrypt from 'bcryptjs';
import { PrismaClient, UserRole as PrismaUserRole } from '@prisma/client';
import { UserRole, CreateUserRequest, UpdateUserRequest, ImpersonationRequest } from '@ecommerce/shared';
import { authenticate, authorize, auditLog, AuthRequest, canManageStore } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { createApiResponse } from '../utils/response';
import { AppError } from '../utils/errors';
import crypto from 'crypto';

const router = express.Router();
const prisma = new PrismaClient();

// All admin routes require admin authentication
router.use(authenticate);
router.use(authorize(UserRole.ADMIN));

// Get all users with pagination and filtering
router.get('/users', asyncHandler(async (req: AuthRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const role = req.query.role as UserRole;
  const search = req.query.search as string;
  const isActive = req.query.isActive === 'true' ? true : req.query.isActive === 'false' ? false : undefined;

  const skip = (page - 1) * limit;

  const where: any = {};
  if (role) where.role = role;
  if (isActive !== undefined) where.isActive = isActive;
  if (search) {
    where.OR = [
      { email: { contains: search, mode: 'insensitive' } },
      { username: { contains: search, mode: 'insensitive' } }
    ];
  }

  const [users, total] = await Promise.all([
    prisma.user.findMany({
      where,
      skip,
      take: limit,
      select: {
        id: true,
        email: true,
        username: true,
        role: true,
        isActive: true,
        isBanned: true,
        storeId: true,
        createdAt: true,
        updatedAt: true,
        ownedStore: {
          select: { id: true, name: true }
        },
        managedStores: {
          include: {
            store: { select: { id: true, name: true } }
          }
        },
        _count: {
          select: { orders: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    }),
    prisma.user.count({ where })
  ]);

  res.json(createApiResponse(true, {
    users,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  }));
}));

// Get single user details
router.get('/users/:id', asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;

  const user = await prisma.user.findUnique({
    where: { id },
    select: {
      id: true,
      email: true,
      username: true,
      telegramId: true,
      role: true,
      isActive: true,
      isBanned: true,
      storeId: true,
      createdAt: true,
      updatedAt: true,
      ownedStore: {
        select: { id: true, name: true, description: true, isActive: true }
      },
      managedStores: {
        include: {
          store: { select: { id: true, name: true, description: true } }
        }
      },
      _count: {
        select: { orders: true, downloads: true }
      }
    }
  });

  if (!user) {
    throw new AppError('User not found', 404);
  }

  res.json(createApiResponse(true, { user }));
}));

// Create new user
router.post('/users', auditLog('USER_CREATED'), asyncHandler(async (req: AuthRequest, res) => {
  const { email, username, password, role, storeId }: CreateUserRequest = req.body;

  // Validate required fields
  if (!email || !role) {
    throw new AppError('Email and role are required', 400);
  }

  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email }
  });

  if (existingUser) {
    throw new AppError('User with this email already exists', 409);
  }

  // For sellers, create a store if storeId is not provided
  let finalStoreId = storeId;
  if (role === UserRole.SELLER && !storeId) {
    const store = await prisma.store.create({
      data: {
        name: `${username || email.split('@')[0]}'s Store`,
        description: 'Default store description'
      }
    });
    finalStoreId = store.id;
  }

  // Hash password if provided
  let hashedPassword;
  if (password) {
    hashedPassword = await bcrypt.hash(password, 12);
  }

  const user = await prisma.user.create({
    data: {
      email,
      username,
      password: hashedPassword,
      role: role as PrismaUserRole,
      storeId: finalStoreId
    },
    select: {
      id: true,
      email: true,
      username: true,
      role: true,
      isActive: true,
      storeId: true,
      createdAt: true
    }
  });

  res.status(201).json(createApiResponse(true, { user }, 'User created successfully'));
}));

// Update user
router.put('/users/:id', auditLog('USER_UPDATED'), asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;
  const updates: UpdateUserRequest = req.body;

  const existingUser = await prisma.user.findUnique({
    where: { id }
  });

  if (!existingUser) {
    throw new AppError('User not found', 404);
  }

  // If changing role to seller and no storeId provided, create a store
  if (updates.role === UserRole.SELLER && !updates.storeId && !existingUser.storeId) {
    const store = await prisma.store.create({
      data: {
        name: `${updates.username || existingUser.username || existingUser.email.split('@')[0]}'s Store`,
        description: 'Default store description'
      }
    });
    updates.storeId = store.id;
  }

  const user = await prisma.user.update({
    where: { id },
    data: {
      ...updates,
      role: updates.role as PrismaUserRole
    },
    select: {
      id: true,
      email: true,
      username: true,
      role: true,
      isActive: true,
      isBanned: true,
      storeId: true,
      updatedAt: true
    }
  });

  res.json(createApiResponse(true, { user }, 'User updated successfully'));
}));

// Ban/unban user
router.patch('/users/:id/ban', auditLog('USER_BAN_TOGGLE'), asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;
  const { isBanned } = req.body;

  const user = await prisma.user.update({
    where: { id },
    data: { isBanned },
    select: {
      id: true,
      email: true,
      isBanned: true
    }
  });

  res.json(createApiResponse(true, { user }, `User ${isBanned ? 'banned' : 'unbanned'} successfully`));
}));

// Activate/deactivate user
router.patch('/users/:id/activate', auditLog('USER_ACTIVATION_TOGGLE'), asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;
  const { isActive } = req.body;

  const user = await prisma.user.update({
    where: { id },
    data: { isActive },
    select: {
      id: true,
      email: true,
      isActive: true
    }
  });

  res.json(createApiResponse(true, { user }, `User ${isActive ? 'activated' : 'deactivated'} successfully`));
}));

// Delete user permanently
router.delete('/users/:id', auditLog('USER_DELETED'), asyncHandler(async (req: AuthRequest, res) => {
  const { id } = req.params;

  // Check if user exists
  const user = await prisma.user.findUnique({
    where: { id },
    include: { ownedStore: true }
  });

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // If user owns a store, delete it too (cascade will handle products)
  if (user.ownedStore) {
    await prisma.store.delete({
      where: { id: user.ownedStore.id }
    });
  }

  await prisma.user.delete({
    where: { id }
  });

  res.json(createApiResponse(true, null, 'User deleted successfully'));
}));

// Impersonate user
router.post('/users/:id/impersonate', auditLog('IMPERSONATION_START'), asyncHandler(async (req: AuthRequest, res) => {
  const { id: targetUserId } = req.params;

  // Check if target user exists and is not an admin
  const targetUser = await prisma.user.findUnique({
    where: { id: targetUserId },
    select: {
      id: true,
      email: true,
      username: true,
      role: true,
      isActive: true,
      isBanned: true,
      storeId: true
    }
  });

  if (!targetUser) {
    throw new AppError('Target user not found', 404);
  }

  if (targetUser.role === UserRole.ADMIN) {
    throw new AppError('Cannot impersonate admin users', 403);
  }

  if (!targetUser.isActive || targetUser.isBanned) {
    throw new AppError('Cannot impersonate inactive or banned users', 403);
  }

  // Create impersonation session
  const sessionToken = crypto.randomBytes(32).toString('hex');
  const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

  const session = await prisma.userSession.create({
    data: {
      userId: targetUserId,
      originalUserId: req.user!.id,
      sessionToken,
      expiresAt
    }
  });

  res.json(createApiResponse(true, {
    sessionToken,
    targetUser,
    expiresAt
  }, 'Impersonation session created successfully'));
}));

// End impersonation session
router.post('/impersonation/end', auditLog('IMPERSONATION_END'), asyncHandler(async (req: AuthRequest, res) => {
  if (!req.user?.isImpersonating) {
    throw new AppError('Not currently impersonating', 400);
  }

  const token = req.header('Authorization')?.replace('Bearer ', '');

  await prisma.userSession.update({
    where: { sessionToken: token },
    data: {
      isActive: false,
      endedAt: new Date()
    }
  });

  res.json(createApiResponse(true, null, 'Impersonation session ended successfully'));
}));

// Get all stores
router.get('/stores', asyncHandler(async (req: AuthRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const search = req.query.search as string;
  const isActive = req.query.isActive === 'true' ? true : req.query.isActive === 'false' ? false : undefined;

  const skip = (page - 1) * limit;

  const where: any = {};
  if (isActive !== undefined) where.isActive = isActive;
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } }
    ];
  }

  const [stores, total] = await Promise.all([
    prisma.store.findMany({
      where,
      skip,
      take: limit,
      include: {
        owner: {
          select: { id: true, email: true, username: true }
        },
        _count: {
          select: { products: true, managers: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    }),
    prisma.store.count({ where })
  ]);

  res.json(createApiResponse(true, {
    stores,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  }));
}));

// Create new store
router.post('/stores', auditLog('STORE_CREATED'), asyncHandler(async (req: AuthRequest, res) => {
  const { name, description, ownerId } = req.body;

  if (!name) {
    throw new AppError('Store name is required', 400);
  }

  const store = await prisma.store.create({
    data: {
      name,
      description
    },
    include: {
      owner: {
        select: { id: true, email: true, username: true }
      }
    }
  });

  // If ownerId is provided, update the user
  if (ownerId) {
    await prisma.user.update({
      where: { id: ownerId },
      data: { storeId: store.id }
    });
  }

  res.status(201).json(createApiResponse(true, { store }, 'Store created successfully'));
}));

// Get audit logs
router.get('/audit-logs', asyncHandler(async (req: AuthRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const action = req.query.action as string;
  const userId = req.query.userId as string;

  const skip = (page - 1) * limit;

  const where: any = {};
  if (action) where.action = { contains: action, mode: 'insensitive' };
  if (userId) where.userId = userId;

  const [logs, total] = await Promise.all([
    prisma.auditLog.findMany({
      where,
      skip,
      take: limit,
      include: {
        user: {
          select: { id: true, email: true, username: true, role: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    }),
    prisma.auditLog.count({ where })
  ]);

  res.json(createApiResponse(true, {
    logs,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  }));
}));

export default router;
