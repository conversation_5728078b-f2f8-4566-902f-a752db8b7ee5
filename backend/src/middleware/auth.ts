import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { PrismaClient, UserRole as PrismaUserRole } from '@prisma/client';
import { UserRole } from '../types/user';

const prisma = new PrismaClient();

export interface AuthRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: UserRole;
    storeId?: string;
    isImpersonating?: boolean;
    originalUserId?: string;
  };
}

export const authenticate = async (req: AuthRequest, res: Response, next: NextFunction) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Access denied. No token provided.'
      });
    }

    // Check if it's a session token (for impersonation)
    const session = await prisma.userSession.findUnique({
      where: { sessionToken: token },
      include: {
        user: { select: { id: true, email: true, role: true, isActive: true, isBanned: true, storeId: true } },
        originalUser: { select: { id: true, email: true, role: true } }
      }
    });

    if (session && session.isActive && session.expiresAt > new Date()) {
      if (!session.user.isActive || session.user.isBanned) {
        return res.status(401).json({
          success: false,
          error: 'User account is inactive or banned.'
        });
      }

      req.user = {
        id: session.user.id,
        email: session.user.email,
        role: session.user.role as UserRole,
        storeId: session.user.storeId || undefined,
        isImpersonating: !!session.originalUserId,
        originalUserId: session.originalUserId || undefined
      };
      return next();
    }

    // Regular JWT token authentication
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;

    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: { id: true, email: true, role: true, isActive: true, isBanned: true, storeId: true }
    });

    if (!user || !user.isActive || user.isBanned) {
      return res.status(401).json({
        success: false,
        error: 'Invalid token or user not found.'
      });
    }

    req.user = {
      id: user.id,
      email: user.email,
      role: user.role as UserRole,
      storeId: user.storeId || undefined
    };
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      error: 'Invalid token.'
    });
  }
};

export const authorize = (...roles: UserRole[]) => {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Access denied. User not authenticated.'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Access denied. Insufficient permissions.'
      });
    }

    next();
  };
};

export const optionalAuth = async (req: AuthRequest, res: Response, next: NextFunction) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (token) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;

      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: { id: true, email: true, role: true, isActive: true, isBanned: true, storeId: true }
      });

      if (user && user.isActive && !user.isBanned) {
        req.user = {
          id: user.id,
          email: user.email,
          role: user.role as UserRole,
          storeId: user.storeId || undefined
        };
      }
    }

    next();
  } catch (error) {
    // Continue without authentication if token is invalid
    next();
  }
};

// Store-based authorization middleware
export const authorizeStore = (req: AuthRequest, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required.'
    });
  }

  const storeId = req.params.storeId || req.body.storeId || req.query.storeId;

  // Admins can access any store
  if (req.user.role === UserRole.ADMIN) {
    return next();
  }

  // Sellers can only access their own store
  if (req.user.role === UserRole.SELLER) {
    if (req.user.storeId !== storeId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied. You can only access your own store.'
      });
    }
    return next();
  }

  // Managers can only access stores they're assigned to
  if (req.user.role === UserRole.MANAGER) {
    // This will be checked against UserStore table in the route handler
    return next();
  }

  return res.status(403).json({
    success: false,
    error: 'Access denied. Insufficient permissions.'
  });
};

// Check if user can manage a specific store (for managers)
export const canManageStore = async (userId: string, storeId: string): Promise<boolean> => {
  const userStore = await prisma.userStore.findUnique({
    where: {
      userId_storeId: {
        userId,
        storeId
      }
    }
  });
  return !!userStore;
};

// Audit logging middleware
export const auditLog = (action: string) => {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    if (req.user) {
      try {
        await prisma.auditLog.create({
          data: {
            userId: req.user.originalUserId || req.user.id, // Log original user for impersonation
            action,
            details: {
              targetUserId: req.user.isImpersonating ? req.user.id : undefined,
              method: req.method,
              path: req.path,
              body: req.method !== 'GET' ? req.body : undefined
            },
            ipAddress: req.ip,
            userAgent: req.get('User-Agent')
          }
        });
      } catch (error) {
        console.error('Audit logging failed:', error);
      }
    }
    next();
  };
};
