import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

export const validate = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.body);
    
    if (error) {
      return res.status(400).json({
        success: false,
        error: error.details[0].message
      });
    }
    
    next();
  };
};

// Common validation schemas
export const schemas = {
  // Auth schemas
  register: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(8).required(),
    username: Joi.string().min(3).max(30).optional()
  }),

  login: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required()
  }),

  // Product schemas
  createProduct: Joi.object({
    name: Joi.string().min(1).max(255).required(),
    description: Joi.string().min(1).required(),
    price: Joi.number().positive().required(),
    currency: Joi.string().valid('USD', 'BITCOIN', 'ETHEREUM', 'USDT', 'LITECOIN').default('USD'),
    category: Joi.string().min(1).max(100).required(),
    tags: Joi.array().items(Joi.string().max(50)).default([]),
    downloadLimit: Joi.number().integer().min(1).default(3)
  }),

  updateProduct: Joi.object({
    name: Joi.string().min(1).max(255).optional(),
    description: Joi.string().min(1).optional(),
    price: Joi.number().positive().optional(),
    currency: Joi.string().valid('USD', 'BITCOIN', 'ETHEREUM', 'USDT', 'LITECOIN').optional(),
    category: Joi.string().min(1).max(100).optional(),
    tags: Joi.array().items(Joi.string().max(50)).optional(),
    downloadLimit: Joi.number().integer().min(1).optional(),
    isActive: Joi.boolean().optional()
  }),

  // Order schemas
  createOrder: Joi.object({
    items: Joi.array().items(
      Joi.object({
        productId: Joi.string().required(),
        quantity: Joi.number().integer().min(1).default(1)
      })
    ).min(1).required(),
    paymentMethod: Joi.string().valid('BITCOIN', 'ETHEREUM', 'USDT', 'LITECOIN').required(),
    couponCode: Joi.string().optional()
  }),

  // Coupon schemas
  createCoupon: Joi.object({
    code: Joi.string().min(3).max(20).required(),
    discountType: Joi.string().valid('PERCENTAGE', 'FIXED_AMOUNT').required(),
    discountValue: Joi.number().positive().required(),
    minOrderAmount: Joi.number().positive().optional(),
    maxUses: Joi.number().integer().positive().optional(),
    expiresAt: Joi.date().greater('now').optional()
  }),

  // Wallet schemas
  createWallet: Joi.object({
    currency: Joi.string().valid('BITCOIN', 'ETHEREUM', 'USDT', 'LITECOIN').required(),
    address: Joi.string().min(10).required()
  }),

  // User schemas
  updateUser: Joi.object({
    email: Joi.string().email().optional(),
    username: Joi.string().min(3).max(30).optional(),
    role: Joi.string().valid('ADMIN', 'CUSTOMER').optional(),
    isActive: Joi.boolean().optional()
  }),

  changePassword: Joi.object({
    currentPassword: Joi.string().required(),
    newPassword: Joi.string().min(8).required()
  })
};
