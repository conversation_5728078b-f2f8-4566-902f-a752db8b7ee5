const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const app = express();
const PORT = 4001; // Different port to avoid conflicts

// Middleware
app.use(cors({
  origin: 'http://localhost:4002',
  credentials: true
}));
app.use(express.json());

// Mock database - in production this would be your actual database
const users = [
  {
    id: '1',
    email: '<EMAIL>',
    username: 'admin',
    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
    role: 'ADMIN',
    isActive: true,
    createdAt: new Date().toISOString()
  }
];

const products = [];
const orders = [];

// Helper functions
const generateTokens = (userId) => {
  const accessToken = jwt.sign(
    { userId },
    'your-secret-key',
    { expiresIn: '15m' }
  );
  
  const refreshToken = jwt.sign(
    { userId },
    'your-refresh-secret-key',
    { expiresIn: '7d' }
  );
  
  return { accessToken, refreshToken };
};

const createApiResponse = (success, data, message) => ({
  success,
  data,
  message,
  timestamp: new Date().toISOString()
});

// Auth middleware
const authenticate = (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json(createApiResponse(false, null, 'Access token required'));
  }

  const token = authHeader.substring(7);
  try {
    const decoded = jwt.verify(token, 'your-secret-key');
    const user = users.find(u => u.id === decoded.userId);
    if (!user || !user.isActive) {
      return res.status(401).json(createApiResponse(false, null, 'Invalid token'));
    }
    req.user = user;
    next();
  } catch (error) {
    return res.status(401).json(createApiResponse(false, null, 'Invalid token'));
  }
};

// Routes

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    port: PORT
  });
});

// Auth routes
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json(createApiResponse(false, null, 'Email and password are required'));
    }

    const user = users.find(u => u.email === email);
    if (!user) {
      return res.status(401).json(createApiResponse(false, null, 'Invalid credentials'));
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json(createApiResponse(false, null, 'Invalid credentials'));
    }

    if (!user.isActive) {
      return res.status(401).json(createApiResponse(false, null, 'Account is deactivated'));
    }

    const tokens = generateTokens(user.id);
    
    const userResponse = {
      id: user.id,
      email: user.email,
      username: user.username,
      role: user.role,
      createdAt: user.createdAt
    };

    res.json(createApiResponse(true, {
      user: userResponse,
      ...tokens
    }, 'Login successful'));
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json(createApiResponse(false, null, 'Internal server error'));
  }
});

app.get('/api/auth/me', authenticate, (req, res) => {
  const userResponse = {
    id: req.user.id,
    email: req.user.email,
    username: req.user.username,
    role: req.user.role,
    createdAt: req.user.createdAt
  };
  
  res.json(createApiResponse(true, userResponse, 'User retrieved successfully'));
});

// Products routes
app.get('/api/products', (req, res) => {
  res.json(createApiResponse(true, {
    products: products,
    pagination: {
      page: 1,
      limit: 10,
      total: products.length,
      totalPages: Math.ceil(products.length / 10)
    }
  }, 'Products retrieved successfully'));
});

// Orders routes
app.get('/api/orders', authenticate, (req, res) => {
  const userOrders = orders.filter(order => order.userId === req.user.id);
  res.json(createApiResponse(true, {
    orders: userOrders,
    pagination: {
      page: 1,
      limit: 10,
      total: userOrders.length,
      totalPages: Math.ceil(userOrders.length / 10)
    }
  }, 'Orders retrieved successfully'));
});

// Analytics routes
app.get('/api/analytics/dashboard', authenticate, (req, res) => {
  if (req.user.role !== 'ADMIN') {
    return res.status(403).json(createApiResponse(false, null, 'Admin access required'));
  }
  
  res.json(createApiResponse(true, {
    totalUsers: users.length,
    totalProducts: products.length,
    totalOrders: orders.length,
    totalRevenue: 0,
    recentOrders: orders.slice(-5),
    salesData: []
  }, 'Dashboard data retrieved successfully'));
});

// Users routes
app.get('/api/users', authenticate, (req, res) => {
  if (req.user.role !== 'ADMIN') {
    return res.status(403).json(createApiResponse(false, null, 'Admin access required'));
  }
  
  const usersResponse = users.map(user => ({
    id: user.id,
    email: user.email,
    username: user.username,
    role: user.role,
    isActive: user.isActive,
    createdAt: user.createdAt
  }));
  
  res.json(createApiResponse(true, {
    users: usersResponse,
    pagination: {
      page: 1,
      limit: 10,
      total: users.length,
      totalPages: Math.ceil(users.length / 10)
    }
  }, 'Users retrieved successfully'));
});

// Wallets routes
app.get('/api/wallets', authenticate, (req, res) => {
  if (req.user.role !== 'ADMIN') {
    return res.status(403).json(createApiResponse(false, null, 'Admin access required'));
  }
  
  res.json(createApiResponse(true, [], 'Wallets retrieved successfully'));
});

// Error handling
app.use((req, res) => {
  res.status(404).json(createApiResponse(false, null, 'Route not found'));
});

app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json(createApiResponse(false, null, 'Internal server error'));
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Simple Backend Server running on port ${PORT}`);
  console.log(`🔗 API URL: http://localhost:${PORT}/api`);
  console.log(`📊 Health Check: http://localhost:${PORT}/health`);
  console.log(`\n📋 Test Login Credentials:`);
  console.log(`   Email: <EMAIL>`);
  console.log(`   Password: password`);
});
