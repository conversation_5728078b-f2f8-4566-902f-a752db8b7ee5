# Database
DATABASE_URL="postgresql://username:password@localhost:5432/ecommerce_db"

# JWT
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-here"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Server
PORT=4000
NODE_ENV="development"

# File Upload
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE=104857600  # 100MB in bytes

# Telegram Bot
TELEGRAM_BOT_TOKEN="your-telegram-bot-token"
TELEGRAM_WEBHOOK_URL="https://your-domain.com/webhook/telegram"

# Crypto Payment APIs (optional - for payment verification)
BLOCKCHAIN_INFO_API="https://blockchain.info/q"
ETHERSCAN_API_KEY="your-etherscan-api-key"
ETHERSCAN_API_URL="https://api.etherscan.io/api"

# CORS
CORS_ORIGIN="http://localhost:4002"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100
