import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 12);
  
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: adminPassword,
      username: 'admin',
      role: 'ADMIN',
      isActive: true
    }
  });

  console.log('✅ Admin user created:', admin.email);

  // Create sample customer
  const customerPassword = await bcrypt.hash('customer123', 12);
  
  const customer = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: customerPassword,
      username: 'customer',
      role: 'CUSTOMER',
      isActive: true
    }
  });

  console.log('✅ Sample customer created:', customer.email);

  // Create crypto wallets
  const wallets = [
    {
      currency: 'BITCOIN',
      address: '**********************************',
      isActive: true
    },
    {
      currency: 'ETHEREUM',
      address: '******************************************',
      isActive: true
    },
    {
      currency: 'USDT',
      address: '******************************************',
      isActive: true
    },
    {
      currency: 'LITECOIN',
      address: 'LTC1234567890abcdefghijklmnopqrstuvwxyz',
      isActive: true
    }
  ];

  for (const wallet of wallets) {
    await prisma.cryptoWallet.upsert({
      where: { address: wallet.address },
      update: {},
      create: wallet as any
    });
  }

  console.log('✅ Crypto wallets created');

  // Create default store and seller
  const sellerPassword = await bcrypt.hash('seller123', 12);

  const seller = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: sellerPassword,
      username: 'seller',
      role: 'SELLER',
      isActive: true
    }
  });

  const defaultStore = await prisma.store.upsert({
    where: { id: 'default-store' },
    update: {},
    create: {
      id: 'default-store',
      name: 'Digital Store',
      description: 'Default store for digital products',
      ownerId: seller.id,
      isActive: true
    }
  });

  // Update seller with store ID
  await prisma.user.update({
    where: { id: seller.id },
    data: { storeId: defaultStore.id }
  });

  console.log('✅ Default store and seller created');

  // Create sample products
  const products = [
    {
      name: 'Premium WordPress Theme',
      description: 'A beautiful and responsive WordPress theme perfect for business websites. Includes multiple layouts, custom widgets, and full documentation.',
      price: 49.99,
      currency: 'USD',
      category: 'Templates',
      tags: ['wordpress', 'theme', 'responsive', 'business'],
      isActive: true,
      fileUrl: '',
      fileName: 'premium-wp-theme.zip',
      fileSize: 15728640, // 15MB
      downloadLimit: 3,
      storeId: 'default-store'
    },
    {
      name: 'JavaScript Mastery Course',
      description: 'Complete JavaScript course from beginner to advanced. Includes 50+ hours of video content, exercises, and projects.',
      price: 99.99,
      currency: 'USD',
      category: 'Courses',
      tags: ['javascript', 'programming', 'web development', 'course'],
      isActive: true,
      fileUrl: '',
      fileName: 'js-mastery-course.zip',
      fileSize: 524288000, // 500MB
      downloadLimit: 5,
      storeId: 'default-store'
    },
    {
      name: 'Digital Marketing E-book',
      description: 'Comprehensive guide to digital marketing strategies, including SEO, social media marketing, and email campaigns.',
      price: 19.99,
      currency: 'USD',
      category: 'E-books',
      tags: ['marketing', 'seo', 'social media', 'ebook'],
      isActive: true,
      fileUrl: '',
      fileName: 'digital-marketing-guide.pdf',
      fileSize: 5242880, // 5MB
      downloadLimit: 3,
      storeId: 'default-store'
    },
    {
      name: 'Photo Editing Software',
      description: 'Professional photo editing software with advanced features for photographers and designers.',
      price: 79.99,
      currency: 'USD',
      category: 'Software',
      tags: ['photo editing', 'software', 'photography', 'design'],
      isActive: true,
      fileUrl: '',
      fileName: 'photo-editor-pro.exe',
      fileSize: 104857600, // 100MB
      downloadLimit: 2,
      storeId: 'default-store'
    },
    {
      name: 'UI/UX Design Kit',
      description: 'Complete UI/UX design kit with 100+ components, icons, and templates for mobile and web applications.',
      price: 39.99,
      currency: 'USD',
      category: 'Graphics',
      tags: ['ui', 'ux', 'design', 'templates', 'figma'],
      isActive: true,
      fileUrl: '',
      fileName: 'ui-ux-design-kit.zip',
      fileSize: 52428800, // 50MB
      downloadLimit: 3,
      storeId: 'default-store'
    }
  ];

  for (const product of products) {
    await prisma.product.create({
      data: product
    });
  }

  console.log('✅ Sample products created');

  // Create sample coupons
  const coupons = [
    {
      code: 'WELCOME10',
      discountType: 'PERCENTAGE',
      discountValue: 10,
      minOrderAmount: 20,
      maxUses: 100,
      usedCount: 0,
      isActive: true,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    },
    {
      code: 'SAVE20',
      discountType: 'FIXED_AMOUNT',
      discountValue: 20,
      minOrderAmount: 100,
      maxUses: 50,
      usedCount: 0,
      isActive: true,
      expiresAt: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000) // 60 days from now
    },
    {
      code: 'FIRSTBUY',
      discountType: 'PERCENTAGE',
      discountValue: 15,
      minOrderAmount: 50,
      maxUses: 200,
      usedCount: 0,
      isActive: true
    }
  ];

  for (const coupon of coupons) {
    await prisma.coupon.upsert({
      where: { code: coupon.code },
      update: {},
      create: coupon as any
    });
  }

  console.log('✅ Sample coupons created');

  // Create system settings
  const settings = [
    { key: 'SITE_NAME', value: 'Digital Store' },
    { key: 'SITE_DESCRIPTION', value: 'Your one-stop shop for digital products' },
    { key: 'SUPPORT_EMAIL', value: '<EMAIL>' },
    { key: 'MAX_DOWNLOAD_ATTEMPTS', value: '3' },
    { key: 'DOWNLOAD_LINK_EXPIRY_HOURS', value: '24' }
  ];

  for (const setting of settings) {
    await prisma.systemSettings.upsert({
      where: { key: setting.key },
      update: { value: setting.value },
      create: setting
    });
  }

  console.log('✅ System settings created');

  console.log('🎉 Database seed completed successfully!');
  console.log('\n📋 Login Credentials:');
  console.log('Admin: <EMAIL> / admin123');
  console.log('Customer: <EMAIL> / customer123');
}

main()
  .catch((e) => {
    console.error('❌ Seed failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
