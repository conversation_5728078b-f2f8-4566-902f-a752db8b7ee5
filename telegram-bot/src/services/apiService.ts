import axios, { AxiosInstance } from 'axios';
import { ApiResponse, Product, Order, User, CryptoWallet, PaginatedResponse } from '@ecommerce/shared';

export class ApiService {
  private api: AxiosInstance;

  constructor(baseURL: string) {
    this.api = axios.create({
      baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  // Set authorization token
  setAuthToken(token: string) {
    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  // Remove authorization token
  removeAuthToken() {
    delete this.api.defaults.headers.common['Authorization'];
  }

  // Auth methods
  async registerUser(email: string, password: string, username?: string): Promise<ApiResponse<{ user: User; tokens: any }>> {
    const response = await this.api.post('/auth/register', { email, password, username });
    return response.data;
  }

  async loginUser(email: string, password: string): Promise<ApiResponse<{ user: User; tokens: any }>> {
    const response = await this.api.post('/auth/login', { email, password });
    return response.data;
  }

  async getCurrentUser(): Promise<ApiResponse<{ user: User }>> {
    const response = await this.api.get('/auth/me');
    return response.data;
  }

  async linkTelegramAccount(userId: string, telegramId: string): Promise<ApiResponse<{ user: User }>> {
    const response = await this.api.post(`/users/${userId}/link-telegram`, { telegramId });
    return response.data;
  }

  // Product methods
  async getProducts(params?: {
    page?: number;
    limit?: number;
    category?: string;
    search?: string;
    tags?: string[];
  }): Promise<ApiResponse<{ products: Product[]; pagination: any }>> {
    const response = await this.api.get('/products', { params });
    return response.data;
  }

  async getProduct(id: string): Promise<ApiResponse<{ product: Product }>> {
    const response = await this.api.get(`/products/${id}`);
    return response.data;
  }

  async getProductCategories(): Promise<ApiResponse<{ categories: string[] }>> {
    const response = await this.api.get('/products/meta/categories');
    return response.data;
  }

  // Order methods
  async createOrder(orderData: {
    items: { productId: string; quantity: number }[];
    paymentMethod: string;
    couponCode?: string;
  }): Promise<ApiResponse<{ order: Order }>> {
    const response = await this.api.post('/orders', orderData);
    return response.data;
  }

  async getOrders(params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<ApiResponse<{ orders: Order[]; pagination: any }>> {
    const response = await this.api.get('/orders', { params });
    return response.data;
  }

  async getOrder(id: string): Promise<ApiResponse<{ order: Order }>> {
    const response = await this.api.get(`/orders/${id}`);
    return response.data;
  }

  // Wallet methods
  async getActiveWallets(): Promise<ApiResponse<{ wallets: CryptoWallet[] }>> {
    const response = await this.api.get('/wallets/active');
    return response.data;
  }

  async getWalletByCurrency(currency: string): Promise<ApiResponse<{ wallet: CryptoWallet }>> {
    const response = await this.api.get(`/wallets/currency/${currency}`);
    return response.data;
  }

  // Coupon methods
  async validateCoupon(code: string, orderAmount?: number): Promise<ApiResponse<any>> {
    const response = await this.api.post('/coupons/validate', { code, orderAmount });
    return response.data;
  }

  // User methods
  async updateUser(userId: string, data: Partial<User>): Promise<ApiResponse<{ user: User }>> {
    const response = await this.api.put(`/users/${userId}`, data);
    return response.data;
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse> {
    const response = await this.api.put('/auth/change-password', { currentPassword, newPassword });
    return response.data;
  }

  // Download methods
  async generateDownloadLink(productId: string): Promise<ApiResponse<any>> {
    const response = await this.api.post(`/downloads/generate/${productId}`);
    return response.data;
  }

  async getDownloadHistory(params?: { page?: number; limit?: number }): Promise<ApiResponse<any>> {
    const response = await this.api.get('/downloads/history', { params });
    return response.data;
  }
}
