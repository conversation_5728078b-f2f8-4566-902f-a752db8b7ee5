import { Context, Markup } from 'telegraf';
import { ApiService } from '../services/apiService';
import { UserSession } from '../types/session';
import { formatDate } from '@ecommerce/shared';

interface BotContext extends Context {
  session?: UserSession;
}

export const handleProfile = (apiService: ApiService) => {
  return async (ctx: BotContext) => {
    const callbackData = (ctx as any).match?.[1];
    const action = callbackData?.split(':')[0];
    const value = callbackData?.split(':')[1];

    try {
      switch (action) {
        case 'login':
          await showLoginPrompt(ctx);
          break;
        case 'register':
          await showRegisterPrompt(ctx);
          break;
        case 'view':
          await showProfile(ctx, apiService);
          break;
        case 'logout':
          await logout(ctx);
          break;
        default:
          if (ctx.session?.isAuthenticated) {
            await showProfile(ctx, apiService);
          } else {
            await showAuthMenu(ctx);
          }
      }
    } catch (error) {
      console.error('Profile handler error:', error);
      await ctx.reply('❌ Error processing profile request. Please try again.');
    }
  };
};

async function showAuthMenu(ctx: BotContext) {
  const message = `👤 *Account Management*\n\nTo access your profile and order history, please log in or create an account.\n\n🔐 *Benefits of having an account:*\n• Track your orders\n• Download purchased files\n• Faster checkout\n• Order history\n• Customer support`;

  const keyboard = [
    [Markup.button.callback('🔑 Login', 'profile:login')],
    [Markup.button.callback('📝 Register', 'profile:register')],
    [Markup.button.callback('🏠 Main Menu', 'main_menu')]
  ];

  await ctx.reply(message, {
    parse_mode: 'Markdown',
    reply_markup: Markup.inlineKeyboard(keyboard).reply_markup
  });
}

async function showLoginPrompt(ctx: BotContext) {
  const message = `🔑 *Login to Your Account*\n\nTo log in, please use the login command:\n\n\`/login <EMAIL> your_password\`\n\nExample:\n\`/login <EMAIL> mypassword123\`\n\n⚠️ *Security Note:*\nFor security reasons, please delete your message after sending it.`;

  await ctx.reply(message, {
    parse_mode: 'Markdown',
    reply_markup: Markup.inlineKeyboard([
      [Markup.button.callback('🔙 Back', 'profile:menu')]
    ]).reply_markup
  });
}

async function showRegisterPrompt(ctx: BotContext) {
  const message = `📝 *Create New Account*\n\nTo register, please use the register command:\n\n\`/register <EMAIL> your_password username\`\n\nExample:\n\`/register <EMAIL> mypassword123 john_doe\`\n\n📋 *Requirements:*\n• Valid email address\n• Password (min 8 characters)\n• Username (optional)\n\n⚠️ *Security Note:*\nFor security reasons, please delete your message after sending it.`;

  await ctx.reply(message, {
    parse_mode: 'Markdown',
    reply_markup: Markup.inlineKeyboard([
      [Markup.button.callback('🔙 Back', 'profile:menu')]
    ]).reply_markup
  });
}

async function showProfile(ctx: BotContext, apiService: ApiService) {
  if (!ctx.session?.isAuthenticated || !ctx.session.user) {
    await showAuthMenu(ctx);
    return;
  }

  try {
    // Get updated user info
    const userResponse = await apiService.getCurrentUser();
    
    if (!userResponse.success || !userResponse.data) {
      await ctx.reply('❌ Unable to load profile.');
      return;
    }

    const user = userResponse.data.user;

    let message = `👤 *Your Profile*\n\n`;
    message += `📧 *Email:* ${user.email}\n`;
    message += `👤 *Username:* ${user.username || 'Not set'}\n`;
    message += `🆔 *User ID:* \`${user.id}\`\n`;
    message += `📱 *Telegram:* ${user.telegramId ? 'Linked' : 'Not linked'}\n`;
    message += `👑 *Role:* ${user.role}\n`;
    message += `✅ *Status:* ${user.isActive ? 'Active' : 'Inactive'}\n`;
    message += `📅 *Member Since:* ${formatDate(user.createdAt)}\n`;

    const keyboard = [
      [Markup.button.callback('📦 My Orders', 'order:list:1')],
      [Markup.button.callback('🔄 Refresh Profile', 'profile:view')],
      [Markup.button.callback('🚪 Logout', 'profile:logout')],
      [Markup.button.callback('🏠 Main Menu', 'main_menu')]
    ];

    await ctx.reply(message, {
      parse_mode: 'Markdown',
      reply_markup: Markup.inlineKeyboard(keyboard).reply_markup
    });
  } catch (error) {
    console.error('Error showing profile:', error);
    await ctx.reply('❌ Error loading profile.');
  }
}

async function logout(ctx: BotContext) {
  if (ctx.session) {
    ctx.session.isAuthenticated = false;
    ctx.session.user = null;
    ctx.session.accessToken = undefined;
  }

  await ctx.reply(
    '🚪 *Logged Out*\n\nYou have been successfully logged out.\n\n💡 You can still browse products and add them to your cart, but you\'ll need to log in again to place orders.',
    {
      parse_mode: 'Markdown',
      reply_markup: Markup.inlineKeyboard([
        [Markup.button.callback('🔑 Login Again', 'profile:login')],
        [Markup.button.callback('🛍️ Browse Products', 'products:menu')],
        [Markup.button.callback('🏠 Main Menu', 'main_menu')]
      ]).reply_markup
    }
  );
}
