import { Context, Markup } from 'telegraf';
import { ApiService } from '../services/apiService';
import { UserSession } from '../types/session';
import { Product, formatCurrency, formatFileSize } from '@ecommerce/shared';

interface BotContext extends Context {
  session?: UserSession;
}

export const handleProducts = (apiService: ApiService) => {
  return async (ctx: BotContext) => {
    const callbackData = (ctx as any).match?.[1];
    const action = callbackData?.split(':')[0];
    const value = callbackData?.split(':')[1];

    try {
      switch (action) {
        case 'list':
          await showProductList(ctx, apiService, parseInt(value) || 1);
          break;
        case 'category':
          await showProductsByCategory(ctx, apiService, value);
          break;
        case 'view':
          await showProductDetails(ctx, apiService, value);
          break;
        case 'add_to_cart':
          await addToCart(ctx, apiService, value);
          break;
        default:
          await showProductMenu(ctx, apiService);
      }
    } catch (error) {
      console.error('Products handler error:', error);
      await ctx.reply('❌ Error loading products. Please try again.');
    }
  };
};

async function showProductMenu(ctx: BotContext, apiService: ApiService) {
  try {
    const categoriesResponse = await apiService.getProductCategories();
    
    if (!categoriesResponse.success || !categoriesResponse.data) {
      await ctx.reply('❌ Unable to load categories.');
      return;
    }

    const categories = categoriesResponse.data.categories;
    
    const keyboard = [];
    
    // Add "All Products" button
    keyboard.push([Markup.button.callback('📋 All Products', 'products:list:1')]);
    
    // Add category buttons (2 per row)
    for (let i = 0; i < categories.length; i += 2) {
      const row = [];
      row.push(Markup.button.callback(`📁 ${categories[i]}`, `products:category:${categories[i]}`));
      if (categories[i + 1]) {
        row.push(Markup.button.callback(`📁 ${categories[i + 1]}`, `products:category:${categories[i + 1]}`));
      }
      keyboard.push(row);
    }
    
    keyboard.push([Markup.button.callback('🏠 Main Menu', 'main_menu')]);

    await ctx.reply(
      '🛍️ *Product Categories*\n\nChoose a category to browse products:',
      Markup.inlineKeyboard(keyboard)
    );
  } catch (error) {
    console.error('Error showing product menu:', error);
    await ctx.reply('❌ Error loading product menu.');
  }
}

async function showProductList(ctx: BotContext, apiService: ApiService, page: number = 1) {
  try {
    const productsResponse = await apiService.getProducts({
      page,
      limit: 5,
      category: ctx.session?.currentCategory || undefined
    });

    if (!productsResponse.success || !productsResponse.data) {
      await ctx.reply('❌ Unable to load products.');
      return;
    }

    const { products, pagination } = productsResponse.data;

    if (products.length === 0) {
      await ctx.reply('📭 No products found.');
      return;
    }

    let message = '🛍️ *Available Products*\n\n';
    
    products.forEach((product: Product, index: number) => {
      const number = (page - 1) * 5 + index + 1;
      message += `${number}. *${product.name}*\n`;
      message += `💰 ${formatCurrency(product.price, product.currency)}\n`;
      message += `📁 ${product.category}\n`;
      message += `📄 ${formatFileSize(product.fileSize)}\n`;
      message += `📝 ${product.description.substring(0, 100)}${product.description.length > 100 ? '...' : ''}\n\n`;
    });

    const keyboard = [];
    
    // Product buttons
    products.forEach((product: Product, index: number) => {
      const number = (page - 1) * 5 + index + 1;
      keyboard.push([Markup.button.callback(`${number}. View Details`, `products:view:${product.id}`)]);
    });

    // Pagination
    const paginationRow = [];
    if (pagination.hasPrev) {
      paginationRow.push(Markup.button.callback('⬅️ Previous', `products:list:${page - 1}`));
    }
    if (pagination.hasNext) {
      paginationRow.push(Markup.button.callback('Next ➡️', `products:list:${page + 1}`));
    }
    if (paginationRow.length > 0) {
      keyboard.push(paginationRow);
    }

    keyboard.push([Markup.button.callback('🔙 Categories', 'products:menu')]);

    await ctx.editMessageText(message, {
      parse_mode: 'Markdown',
      reply_markup: Markup.inlineKeyboard(keyboard).reply_markup
    });
  } catch (error) {
    console.error('Error showing product list:', error);
    await ctx.reply('❌ Error loading products.');
  }
}

async function showProductsByCategory(ctx: BotContext, apiService: ApiService, category: string) {
  if (ctx.session) {
    ctx.session.currentCategory = category;
    ctx.session.currentPage = 1;
  }
  await showProductList(ctx, apiService, 1);
}

async function showProductDetails(ctx: BotContext, apiService: ApiService, productId: string) {
  try {
    const productResponse = await apiService.getProduct(productId);

    if (!productResponse.success || !productResponse.data) {
      await ctx.reply('❌ Product not found.');
      return;
    }

    const product = productResponse.data.product;

    let message = `🛍️ *${product.name}*\n\n`;
    message += `💰 *Price:* ${formatCurrency(product.price, product.currency)}\n`;
    message += `📁 *Category:* ${product.category}\n`;
    message += `📄 *File Size:* ${formatFileSize(product.fileSize)}\n`;
    message += `⬇️ *Download Limit:* ${product.downloadLimit} times\n\n`;
    message += `📝 *Description:*\n${product.description}\n\n`;
    
    if (product.tags && product.tags.length > 0) {
      message += `🏷️ *Tags:* ${product.tags.join(', ')}\n\n`;
    }

    const keyboard = [
      [Markup.button.callback('🛒 Add to Cart', `products:add_to_cart:${product.id}`)],
      [Markup.button.callback('🔙 Back to Products', 'products:list:1')]
    ];

    // Show product image if available
    if (product.thumbnailUrl) {
      await ctx.replyWithPhoto(
        { url: `${process.env.API_BASE_URL || 'http://localhost:4000'}${product.thumbnailUrl}` },
        {
          caption: message,
          parse_mode: 'Markdown',
          reply_markup: Markup.inlineKeyboard(keyboard).reply_markup
        }
      );
    } else {
      await ctx.reply(message, {
        parse_mode: 'Markdown',
        reply_markup: Markup.inlineKeyboard(keyboard).reply_markup
      });
    }
  } catch (error) {
    console.error('Error showing product details:', error);
    await ctx.reply('❌ Error loading product details.');
  }
}

async function addToCart(ctx: BotContext, apiService: ApiService, productId: string) {
  try {
    if (!ctx.session) {
      await ctx.reply('❌ Session error. Please restart the bot.');
      return;
    }

    // Get product details
    const productResponse = await apiService.getProduct(productId);
    
    if (!productResponse.success || !productResponse.data) {
      await ctx.reply('❌ Product not found.');
      return;
    }

    const product = productResponse.data.product;

    // Check if product is already in cart
    const existingItem = ctx.session.cart.items.find(item => item.productId === productId);
    
    if (existingItem) {
      await ctx.reply('ℹ️ This product is already in your cart.');
      return;
    }

    // Add to cart
    ctx.session.cart.items.push({
      productId: product.id,
      quantity: 1
    });

    // Update cart total
    ctx.session.cart.totalAmount += product.price;
    ctx.session.cart.currency = product.currency;

    await ctx.reply(
      `✅ *${product.name}* added to cart!\n\n💰 Price: ${formatCurrency(product.price, product.currency)}\n🛒 Cart total: ${formatCurrency(ctx.session.cart.totalAmount, ctx.session.cart.currency)}`,
      {
        parse_mode: 'Markdown',
        reply_markup: Markup.inlineKeyboard([
          [Markup.button.callback('🛒 View Cart', 'cart:view')],
          [Markup.button.callback('🛍️ Continue Shopping', 'products:list:1')]
        ]).reply_markup
      }
    );
  } catch (error) {
    console.error('Error adding to cart:', error);
    await ctx.reply('❌ Error adding product to cart.');
  }
}
