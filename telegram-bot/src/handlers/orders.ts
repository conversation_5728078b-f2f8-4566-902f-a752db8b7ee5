import { Context, Markup } from 'telegraf';
import { ApiService } from '../services/apiService';
import { UserSession } from '../types/session';
import { Order, OrderStatus, formatCurrency, formatDate } from '@ecommerce/shared';

interface BotContext extends Context {
  session?: UserSession;
}

export const handleOrders = (apiService: ApiService) => {
  return async (ctx: BotContext) => {
    const callbackData = (ctx as any).match?.[1];
    const action = callbackData?.split(':')[0];
    const value = callbackData?.split(':')[1];

    try {
      switch (action) {
        case 'list':
          await showOrderList(ctx, apiService, parseInt(value) || 1);
          break;
        case 'view':
          await showOrderDetails(ctx, apiService, value);
          break;
        case 'cancel':
          await cancelOrder(ctx, apiService, value);
          break;
        case 'download':
          await showDownloadLinks(ctx, apiService, value);
          break;
        case 'download_product':
          await generateDownloadLink(ctx, apiService, value);
          break;
        default:
          await showOrderList(ctx, apiService, 1);
      }
    } catch (error) {
      console.error('Orders handler error:', error);
      await ctx.reply('❌ Error processing order request. Please try again.');
    }
  };
};

async function showOrderList(ctx: BotContext, apiService: ApiService, page: number = 1) {
  if (!ctx.session?.isAuthenticated) {
    await ctx.reply(
      '🔐 *Authentication Required*\n\nPlease log in to view your orders.',
      {
        parse_mode: 'Markdown',
        reply_markup: Markup.inlineKeyboard([
          [Markup.button.callback('👤 Login', 'profile:login')]
        ]).reply_markup
      }
    );
    return;
  }

  try {
    const ordersResponse = await apiService.getOrders({
      page,
      limit: 5
    });

    if (!ordersResponse.success || !ordersResponse.data) {
      await ctx.reply('❌ Unable to load orders.');
      return;
    }

    const { orders, pagination } = ordersResponse.data;

    if (orders.length === 0) {
      await ctx.reply(
        '📭 *No orders found*\n\nYou haven\'t placed any orders yet. Start shopping to see your orders here!',
        {
          parse_mode: 'Markdown',
          reply_markup: Markup.inlineKeyboard([
            [Markup.button.callback('🛍️ Start Shopping', 'products:menu')]
          ]).reply_markup
        }
      );
      return;
    }

    let message = '📦 *Your Orders*\n\n';
    
    orders.forEach((order: Order, index: number) => {
      const number = (page - 1) * 5 + index + 1;
      const statusEmoji = getOrderStatusEmoji(order.status);
      
      message += `${number}. ${statusEmoji} *Order #${order.id.substring(0, 8)}*\n`;
      message += `   💰 ${formatCurrency(order.totalAmount, order.currency)}\n`;
      message += `   📅 ${formatDate(order.createdAt)}\n`;
      message += `   🔐 ${order.paymentMethod}\n`;
      message += `   📊 Status: ${order.status}\n\n`;
    });

    const keyboard = [];
    
    // Order detail buttons
    orders.forEach((order: Order, index: number) => {
      const number = (page - 1) * 5 + index + 1;
      keyboard.push([Markup.button.callback(`${number}. View Details`, `order:view:${order.id}`)]);
    });

    // Pagination
    const paginationRow = [];
    if (pagination.hasPrev) {
      paginationRow.push(Markup.button.callback('⬅️ Previous', `order:list:${page - 1}`));
    }
    if (pagination.hasNext) {
      paginationRow.push(Markup.button.callback('Next ➡️', `order:list:${page + 1}`));
    }
    if (paginationRow.length > 0) {
      keyboard.push(paginationRow);
    }

    keyboard.push([Markup.button.callback('🏠 Main Menu', 'main_menu')]);

    await ctx.reply(message, {
      parse_mode: 'Markdown',
      reply_markup: Markup.inlineKeyboard(keyboard).reply_markup
    });
  } catch (error) {
    console.error('Error showing order list:', error);
    await ctx.reply('❌ Error loading orders.');
  }
}

async function showOrderDetails(ctx: BotContext, apiService: ApiService, orderId: string) {
  if (!ctx.session?.isAuthenticated) {
    await ctx.reply('🔐 Please log in to view order details.');
    return;
  }

  try {
    const orderResponse = await apiService.getOrder(orderId);

    if (!orderResponse.success || !orderResponse.data) {
      await ctx.reply('❌ Order not found.');
      return;
    }

    const order = orderResponse.data.order;
    const statusEmoji = getOrderStatusEmoji(order.status);

    let message = `📦 *Order Details*\n\n`;
    message += `🆔 *Order ID:* \`${order.id}\`\n`;
    message += `${statusEmoji} *Status:* ${order.status}\n`;
    message += `💰 *Total:* ${formatCurrency(order.totalAmount, order.currency)}\n`;
    message += `🔐 *Payment Method:* ${order.paymentMethod}\n`;
    message += `📅 *Order Date:* ${formatDate(order.createdAt)}\n\n`;

    if (order.paymentAddress) {
      message += `📍 *Payment Address:*\n\`${order.paymentAddress}\`\n\n`;
    }

    if (order.transactionHash) {
      message += `🔗 *Transaction Hash:*\n\`${order.transactionHash}\`\n\n`;
    }

    if (order.couponCode) {
      message += `🎟️ *Coupon Used:* ${order.couponCode}\n`;
      message += `💸 *Discount:* ${formatCurrency(order.discountAmount, order.currency)}\n\n`;
    }

    message += `📋 *Items (${order.items.length}):*\n`;
    order.items.forEach((item: any, index: number) => {
      message += `${index + 1}. ${item.product.name}\n`;
      message += `   💰 ${formatCurrency(item.price, order.currency)} × ${item.quantity}\n`;
    });

    const keyboard = [];

    // Action buttons based on order status
    if (order.status === OrderStatus.PENDING) {
      keyboard.push([Markup.button.callback('❌ Cancel Order', `order:cancel:${order.id}`)]);
    }

    if (order.status === OrderStatus.DELIVERED) {
      keyboard.push([Markup.button.callback('⬇️ Download Files', `order:download:${order.id}`)]);
    }

    keyboard.push([
      Markup.button.callback('🔄 Refresh', `order:view:${order.id}`),
      Markup.button.callback('🔙 Back to Orders', 'order:list:1')
    ]);

    await ctx.reply(message, {
      parse_mode: 'Markdown',
      reply_markup: Markup.inlineKeyboard(keyboard).reply_markup
    });

    // Show payment instructions for pending orders
    if (order.status === OrderStatus.PENDING && order.paymentAddress) {
      const paymentMessage = `💳 *Payment Instructions*\n\n`;
      const instructions = `Send exactly ${formatCurrency(order.totalAmount, order.currency)} to:\n\`${order.paymentAddress}\`\n\n`;
      const warning = `⚠️ *Important:*\n• Send the exact amount\n• Payment will be verified automatically\n• You will receive download links after confirmation`;

      await ctx.reply(paymentMessage + instructions + warning, {
        parse_mode: 'Markdown'
      });
    }
  } catch (error) {
    console.error('Error showing order details:', error);
    await ctx.reply('❌ Error loading order details.');
  }
}

async function cancelOrder(ctx: BotContext, apiService: ApiService, orderId: string) {
  if (!ctx.session?.isAuthenticated) {
    await ctx.reply('🔐 Please log in to cancel orders.');
    return;
  }

  try {
    // Show confirmation dialog
    await ctx.reply(
      '⚠️ *Cancel Order*\n\nAre you sure you want to cancel this order? This action cannot be undone.',
      {
        parse_mode: 'Markdown',
        reply_markup: Markup.inlineKeyboard([
          [
            Markup.button.callback('✅ Yes, Cancel', `order:confirm_cancel:${orderId}`),
            Markup.button.callback('❌ No, Keep Order', `order:view:${orderId}`)
          ]
        ]).reply_markup
      }
    );
  } catch (error) {
    console.error('Error canceling order:', error);
    await ctx.reply('❌ Error canceling order.');
  }
}

async function showDownloadLinks(ctx: BotContext, apiService: ApiService, orderId: string) {
  if (!ctx.session?.isAuthenticated) {
    await ctx.reply('🔐 Please log in to download files.');
    return;
  }

  try {
    const orderResponse = await apiService.getOrder(orderId);

    if (!orderResponse.success || !orderResponse.data) {
      await ctx.reply('❌ Order not found.');
      return;
    }

    const order = orderResponse.data.order;

    if (order.status !== OrderStatus.DELIVERED) {
      await ctx.reply('❌ Downloads are only available for delivered orders.');
      return;
    }

    let message = `⬇️ *Download Your Files*\n\n`;
    message += `📦 Order #${order.id.substring(0, 8)}\n\n`;
    message += `📋 *Available Downloads:*\n`;

    const keyboard = [];

    order.items.forEach((item: any, index: number) => {
      message += `${index + 1}. ${item.product.name}\n`;
      keyboard.push([Markup.button.callback(`⬇️ Download ${item.product.name}`, `order:download_product:${item.product.id}`)]);
    });

    message += `\n💡 *Note:* Download links are valid for 24 hours.`;

    keyboard.push([Markup.button.callback('🔙 Back to Order', `order:view:${order.id}`)]);

    await ctx.reply(message, {
      parse_mode: 'Markdown',
      reply_markup: Markup.inlineKeyboard(keyboard).reply_markup
    });
  } catch (error) {
    console.error('Error showing download links:', error);
    await ctx.reply('❌ Error loading download links.');
  }
}

async function generateDownloadLink(ctx: BotContext, apiService: ApiService, productId: string) {
  if (!ctx.session?.isAuthenticated) {
    await ctx.reply('🔐 Please log in to download files.');
    return;
  }

  try {
    // Generate download link via API
    const response = await apiService.generateDownloadLink(productId);

    if (response.success && response.data) {
      const { downloadUrl, expiresAt, product } = response.data;
      const baseUrl = process.env.API_BASE_URL || 'http://localhost:4000';
      const fullDownloadUrl = `${baseUrl}${downloadUrl}`;

      let message = `⬇️ *Download Ready*\n\n`;
      message += `📄 *File:* ${product.name}\n`;
      message += `📁 *Original Name:* ${product.fileName}\n`;
      message += `📊 *Size:* ${formatFileSize(product.fileSize)}\n\n`;
      message += `🔗 *Download Link:*\n${fullDownloadUrl}\n\n`;
      message += `⏰ *Expires:* ${formatDate(new Date(expiresAt))}\n\n`;
      message += `💡 *Instructions:*\n`;
      message += `• Click the link to download\n`;
      message += `• Link expires in 24 hours\n`;
      message += `• Save the file to your device`;

      await ctx.reply(message, {
        parse_mode: 'Markdown',
        reply_markup: Markup.inlineKeyboard([
          [Markup.button.url('⬇️ Download Now', fullDownloadUrl)],
          [Markup.button.callback('🔙 Back to Downloads', `order:download:${productId}`)]
        ]).reply_markup
      });
    } else {
      await ctx.reply('❌ Unable to generate download link. Please contact support.');
    }
  } catch (error) {
    console.error('Error generating download link:', error);
    await ctx.reply('❌ Error generating download link. Make sure you have purchased this product.');
  }
}

function getOrderStatusEmoji(status: OrderStatus): string {
  switch (status) {
    case OrderStatus.PENDING: return '⏳';
    case OrderStatus.PAID: return '✅';
    case OrderStatus.DELIVERED: return '📦';
    case OrderStatus.CANCELLED: return '❌';
    case OrderStatus.REFUNDED: return '💸';
    default: return '❓';
  }
}
