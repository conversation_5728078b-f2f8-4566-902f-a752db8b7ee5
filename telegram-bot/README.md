# TeleShop Telegram Bot

Telegram bot interface for the TeleShop e-commerce system, providing customers with a seamless shopping experience directly through Telegram.

## 🚀 Features

### Customer Features
- **Product Catalog**: Browse products with search and filtering
- **Shopping Cart**: Add/remove products, manage quantities
- **User Authentication**: Register and login system
- **Secure Checkout**: Cryptocurrency payment processing
- **Order Tracking**: View order history and status updates
- **File Downloads**: Secure download links for purchased products
- **Payment Verification**: Automatic payment confirmation
- **Customer Support**: Integrated help and support system

### Bot Features
- **Interactive Menus**: Inline keyboards for easy navigation
- **Session Management**: User session handling and persistence
- **Payment Integration**: Cryptocurrency payment processing
- **File Delivery**: Secure file download system
- **Notifications**: Order status and payment notifications
- **Multi-language Support**: Configurable language options

### Technical Features
- **Webhook Support**: Production-ready webhook handling
- **Polling Mode**: Development-friendly polling mode
- **Rate Limiting**: User request rate limiting
- **Error Handling**: Comprehensive error handling and recovery
- **Logging**: Detailed logging for debugging and monitoring

## 🛠 Technology Stack

- **Runtime**: Node.js 18+
- **Bot Framework**: Telegraf
- **Language**: TypeScript
- **HTTP Client**: Axios
- **Session Storage**: Memory/Redis (configurable)
- **File Handling**: Built-in file download system

## 📋 Prerequisites

- Node.js 18+ and npm
- Telegram Bot Token (from @BotFather)
- TeleShop Backend API running
- Git

## 🚀 Quick Start

### Bot Setup

1. **Create Telegram Bot**
   - Message @BotFather on Telegram
   - Send `/newbot`
   - Choose bot name: "TeleShop Bot"
   - Choose username: "your_teleshop_bot"
   - Copy the bot token

2. **Clone and navigate to bot directory**
   ```bash
   git clone https://github.com/marysarahmccolley44/TG.git
   cd TG/telegram-bot
   ```

3. **Install dependencies**
   ```bash
   npm install
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your bot token and API URL
   ```

5. **Start the bot**
   ```bash
   npm run dev
   ```

6. **Test the bot**
   - Find your bot on Telegram
   - Send `/start` to begin

### Environment Configuration

Edit `.env` file:

```env
# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your-telegram-bot-token-from-botfather

# API Configuration
API_BASE_URL=http://localhost:4000/api

# Development Settings
NODE_ENV=development
DEBUG=true

# Bot Configuration
BOT_NAME=TeleShop Bot
BOT_USERNAME=your_bot_username
```

## 🤖 Bot Commands

### User Commands
- `/start` - Start the bot and show main menu
- `/products` - Browse product catalog
- `/cart` - View shopping cart
- `/orders` - View order history
- `/profile` - View user profile
- `/help` - Get help and support

### Authentication Commands
- `/register` - Register new account
- `/login` - Login to existing account
- `/logout` - Logout from account

### Admin Commands (if enabled)
- `/admin` - Access admin functions
- `/stats` - View bot statistics
- `/broadcast` - Send broadcast message

## 📱 Bot Flow

### User Registration
1. User sends `/start`
2. Bot shows welcome message and registration options
3. User provides email, password, and username
4. Bot creates account via API
5. User is logged in automatically

### Shopping Flow
1. User browses products via `/products`
2. User selects product and adds to cart
3. User views cart via `/cart`
4. User proceeds to checkout
5. Bot generates payment address
6. User sends payment
7. Bot verifies payment and delivers files

### Order Management
1. User views orders via `/orders`
2. Bot shows order history with status
3. User can download purchased files
4. User receives notifications for status updates

## 🔧 Configuration

### Webhook Mode (Production)

```env
TELEGRAM_WEBHOOK_URL=https://yourdomain.com/webhook/telegram
WEBHOOK_SECRET=your-webhook-secret-key
```

Set webhook:
```bash
curl -X POST "https://api.telegram.org/bot<TOKEN>/setWebhook" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://yourdomain.com/webhook/telegram"}'
```

### Polling Mode (Development)

```env
TELEGRAM_WEBHOOK_URL=
POLLING_TIMEOUT=30
POLLING_LIMIT=100
```

### Session Configuration

```env
SESSION_TTL=3600  # 1 hour
CACHE_TTL=300     # 5 minutes
```

### Payment Configuration

```env
PAYMENT_TIMEOUT=1800        # 30 minutes
PAYMENT_CHECK_INTERVAL=30   # 30 seconds
```

## 🗂 Project Structure

```
telegram-bot/
├── src/
│   ├── bot/              # Bot setup and configuration
│   ├── commands/         # Bot command handlers
│   ├── scenes/           # Conversation scenes
│   ├── services/         # Business logic services
│   ├── utils/            # Utility functions
│   ├── types/            # TypeScript types
│   └── index.ts          # Bot entry point
├── dist/                 # Compiled JavaScript
└── logs/                 # Log files
```

## 🛠 Development

### Available Scripts

```bash
npm run dev      # Start development bot with hot reload
npm run build    # Build TypeScript to JavaScript
npm run start    # Start production bot
npm run lint     # Run ESLint
npm run test     # Run tests (when available)
```

### Adding New Commands

1. **Create command handler** in `src/commands/`
2. **Register command** in bot setup
3. **Add help text** to help command
4. **Update types** if needed
5. **Test command** in development

### Adding New Scenes

1. **Create scene** in `src/scenes/`
2. **Define scene steps** and handlers
3. **Register scene** in bot setup
4. **Add entry points** from commands or menus

## 🔒 Security

### Bot Token Security
- Never commit bot token to version control
- Use environment variables
- Rotate token if compromised

### User Data Protection
- Encrypt sensitive user data
- Implement session timeouts
- Validate all user inputs

### Payment Security
- Verify payment amounts
- Check transaction confirmations
- Implement payment timeouts

## 🚀 Deployment

### Production Setup

1. **Set production environment**:
   ```env
   NODE_ENV=production
   DEBUG=false
   ```

2. **Configure webhook**:
   ```env
   TELEGRAM_WEBHOOK_URL=https://yourdomain.com/webhook
   ```

3. **Build and start**:
   ```bash
   npm run build
   npm start
   ```

### Docker Deployment

```bash
docker build -t teleshop-bot .
docker run -d --env-file .env teleshop-bot
```

### Process Management

Use PM2 for production:
```bash
npm install -g pm2
pm2 start dist/index.js --name teleshop-bot
pm2 save
pm2 startup
```

## 🔍 Troubleshooting

### Common Issues

1. **Bot not responding**: Check bot token and API connectivity
2. **Webhook errors**: Verify webhook URL and SSL certificate
3. **API connection**: Check backend API is running and accessible
4. **Payment issues**: Verify cryptocurrency wallet configuration
5. **File download errors**: Check file permissions and storage

### Debug Mode

Enable debug logging:
```env
DEBUG=true
LOG_LEVEL=debug
```

### Bot Testing

Test bot commands:
```bash
# Send test message
curl -X POST "https://api.telegram.org/bot<TOKEN>/sendMessage" \
  -H "Content-Type: application/json" \
  -d '{"chat_id": "YOUR_CHAT_ID", "text": "Test message"}'
```

## 📚 Bot Development Resources

- **Telegraf Documentation**: https://telegraf.js.org/
- **Telegram Bot API**: https://core.telegram.org/bots/api
- **Bot Best Practices**: https://core.telegram.org/bots/faq

## 🤝 Contributing

1. Follow Telegraf best practices
2. Implement proper error handling
3. Add user input validation
4. Test all conversation flows
5. Update command documentation
6. Follow TypeScript conventions

---

**TeleShop Telegram Bot - Digital commerce in your pocket**
