# TeleShop Telegram Bot Environment Variables

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your-telegram-bot-token-from-botfather
TELEGRAM_WEBHOOK_URL=

# API Configuration
API_BASE_URL=http://localhost:4000/api

# Development Settings
NODE_ENV=development
DEBUG=true

# Bot Configuration
BOT_NAME=TeleShop Bot
BOT_USERNAME=your_bot_username

# Webhook Configuration (Optional - leave empty for polling mode)
WEBHOOK_SECRET=your-webhook-secret-key

# Polling Configuration
POLLING_TIMEOUT=30
POLLING_LIMIT=100

# Session Configuration
SESSION_TTL=3600

# File Download Configuration
DOWNLOAD_TIMEOUT=30000
MAX_DOWNLOAD_SIZE=100MB

# Payment Configuration
PAYMENT_TIMEOUT=1800
PAYMENT_CHECK_INTERVAL=30

# Logging
LOG_LEVEL=info

# Rate Limiting
USER_RATE_LIMIT=10
USER_RATE_WINDOW=60

# Cache Configuration
CACHE_TTL=300

# Error Handling
MAX_RETRIES=3
RETRY_DELAY=1000

# Development Features
ENABLE_DEV_COMMANDS=true
ENABLE_LOGGING=true
