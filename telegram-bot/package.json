{"name": "@teleshop/telegram-bot", "version": "1.0.0", "description": "TeleShop Telegram bot for the e-commerce system", "main": "dist/bot.js", "repository": {"type": "git", "url": "https://github.com/marysarahmccolley44/TG.git"}, "homepage": "https://github.com/marysarahmccolley44/TG", "bugs": {"url": "https://github.com/marysarahmccolley44/TG/issues"}, "scripts": {"dev": "nodemon src/bot.ts", "build": "tsc", "start": "node dist/bot.js"}, "dependencies": {"@teleshop/shared": "file:../shared", "telegraf": "^4.15.6", "axios": "^1.6.2", "dotenv": "^16.3.1"}, "devDependencies": {"@types/node": "^20.10.5", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2"}}