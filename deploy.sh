#!/bin/bash

# Digital E-commerce System Deployment Script

set -e

echo "🚀 Starting deployment of Digital E-commerce System..."

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please create one based on .env.example"
    exit 1
fi

# Load environment variables
source .env

# Check required environment variables
required_vars=("POSTGRES_PASSWORD" "JWT_SECRET" "JWT_REFRESH_SECRET" "TELEGRAM_BOT_TOKEN")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ Required environment variable $var is not set"
        exit 1
    fi
done

echo "✅ Environment variables validated"

# Build and start services
echo "🔨 Building and starting services..."
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d

echo "⏳ Waiting for database to be ready..."
sleep 10

# Run database migrations
echo "🗄️ Running database migrations..."
docker-compose -f docker-compose.prod.yml exec -T backend npx prisma migrate deploy

# Seed database (optional)
if [ "$1" = "--seed" ]; then
    echo "🌱 Seeding database..."
    docker-compose -f docker-compose.prod.yml exec -T backend npx prisma db seed
fi

# Check service health
echo "🔍 Checking service health..."
sleep 5

# Check backend health
if curl -f http://localhost:4000/health > /dev/null 2>&1; then
    echo "✅ Backend is healthy"
else
    echo "❌ Backend health check failed"
    exit 1
fi

# Check admin panel health
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Admin panel is healthy"
else
    echo "❌ Admin panel health check failed"
    exit 1
fi

echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Service URLs:"
echo "   Admin Panel: http://localhost:3000"
echo "   API: http://localhost:4000"
echo "   Database: localhost:5432"
echo ""
echo "🔑 Default login credentials:"
echo "   Email: <EMAIL>"
echo "   Password: admin123"
echo ""
echo "📱 Telegram Bot: Search for your bot on Telegram"
echo ""
echo "📊 To view logs: docker-compose -f docker-compose.prod.yml logs -f"
echo "🛑 To stop services: docker-compose -f docker-compose.prod.yml down"
