version: '3.8'

# TeleShop E-commerce System
# Complete Docker Compose setup for development environment

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: teleshop_postgres
    environment:
      POSTGRES_DB: ecommerce_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - teleshop_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: teleshop_redis
    ports:
      - "6379:6379"
    networks:
      - teleshop_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # TeleShop Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: teleshop_backend
    environment:
      DATABASE_URL: ***********************************************/ecommerce_db
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      JWT_REFRESH_SECRET: your-super-secret-refresh-key-change-in-production
      NODE_ENV: development
      PORT: 4000
      CORS_ORIGIN: http://localhost:4002
      UPLOAD_DIR: /app/uploads
      MAX_FILE_SIZE: 104857600
    ports:
      - "4000:4000"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - uploads_data:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - teleshop_network
    command: npm run dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # TeleShop Admin Panel
  admin-panel:
    build:
      context: ./admin-panel
      dockerfile: Dockerfile
    container_name: teleshop_admin
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:4000/api
      NEXT_PUBLIC_MOCK_AUTH: "true"
      NEXT_PUBLIC_DEBUG: "true"
      NODE_ENV: development
    ports:
      - "4002:4002"
    volumes:
      - ./admin-panel:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - teleshop_network
    command: npm run dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4002"]
      interval: 30s
      timeout: 10s
      retries: 3

  # TeleShop Telegram Bot
  telegram-bot:
    build:
      context: ./telegram-bot
      dockerfile: Dockerfile
    container_name: teleshop_bot
    environment:
      TELEGRAM_BOT_TOKEN: ${TELEGRAM_BOT_TOKEN:-your-bot-token-here}
      API_BASE_URL: http://backend:4000/api
      NODE_ENV: development
      DEBUG: "true"
      BOT_NAME: "TeleShop Bot"
    volumes:
      - ./telegram-bot:/app
      - /app/node_modules
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - teleshop_network
    command: npm run dev
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  uploads_data:
    driver: local

networks:
  teleshop_network:
    driver: bridge
    name: teleshop_network
