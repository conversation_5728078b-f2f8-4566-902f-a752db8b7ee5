# TeleShop Troubleshooting Guide

Comprehensive troubleshooting guide for common issues in the TeleShop e-commerce system.

## 🚨 Common Issues and Solutions

### 1. 429 Rate Limiting Errors

**Problem**: API returns 429 "Too Many Requests" errors

**Solution**: The system includes graceful fallbacks to mock data

```typescript
// Already implemented in components
try {
  // API call
  const response = await apiService.getProducts()
  return response.data
} catch (error) {
  if (error.response?.status === 429) {
    console.log('Using mock data due to rate limiting')
    return mockProducts // Fallback to mock data
  }
  throw error
}
```

**Prevention**:
- Configure rate limiting in backend `.env`
- Use caching for frequently accessed data
- Implement request queuing for high-traffic scenarios

### 2. Authentication Issues

**Problem**: Login fails or user gets logged out unexpectedly

**Current Setup**: Mock authentication is active

**Mock Auth Credentials**:
- Email: `<EMAIL>`
- Password: `admin123`

**Switching to Real API**:
1. In `admin-panel/contexts/AuthContext.tsx`:
   ```typescript
   // Uncomment API calls
   // Comment out mock authentication
   ```
2. Ensure backend API is running
3. Set `NEXT_PUBLIC_MOCK_AUTH=false` in `.env.local`

**Real API Issues**:
- Check JWT_SECRET in backend `.env`
- Verify database connection
- Check token expiration settings

### 3. Database Connection Errors

**Problem**: Backend cannot connect to PostgreSQL

**Solutions**:

1. **Check PostgreSQL is running**:
   ```bash
   # macOS
   brew services list | grep postgresql
   brew services start postgresql

   # Ubuntu
   sudo systemctl status postgresql
   sudo systemctl start postgresql
   ```

2. **Verify DATABASE_URL**:
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/ecommerce_db"
   ```

3. **Create database if missing**:
   ```bash
   createdb ecommerce_db
   ```

4. **Run migrations**:
   ```bash
   cd backend
   npx prisma migrate dev
   ```

### 4. Port Conflicts

**Problem**: "Port already in use" errors

**Solutions**:

1. **Kill processes on ports**:
   ```bash
   # Kill process on port 4000 (backend)
   lsof -ti:4000 | xargs kill -9

   # Kill process on port 4002 (admin panel)
   lsof -ti:4002 | xargs kill -9
   ```

2. **Change ports in configuration**:
   ```env
   # Backend
   PORT=4001

   # Admin Panel (package.json)
   "dev": "next dev -p 4003"
   ```

### 5. Build and Compilation Errors

**Problem**: TypeScript compilation errors or build failures

**Solutions**:

1. **Clean install dependencies**:
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **Build shared package first**:
   ```bash
   npm run build:shared
   ```

3. **Check TypeScript errors**:
   ```bash
   npx tsc --noEmit
   ```

4. **Common fixes**:
   - Missing closing braces (like we fixed in pages)
   - Import/export issues
   - Type mismatches

### 6. File Upload Issues

**Problem**: File uploads fail or files not accessible

**Solutions**:

1. **Check upload directory permissions**:
   ```bash
   mkdir -p backend/uploads
   chmod 755 backend/uploads
   ```

2. **Verify file size limits**:
   ```env
   MAX_FILE_SIZE=104857600  # 100MB
   ```

3. **Check allowed file types**:
   ```env
   ALLOWED_FILE_TYPES="pdf,zip,rar,7z,mp4,mp3,jpg,jpeg,png"
   ```

### 7. Telegram Bot Issues

**Problem**: Bot not responding or webhook errors

**Solutions**:

1. **Verify bot token**:
   ```bash
   curl "https://api.telegram.org/bot<TOKEN>/getMe"
   ```

2. **Check API connectivity**:
   ```env
   API_BASE_URL=http://localhost:4000/api
   ```

3. **Switch to polling mode for development**:
   ```env
   TELEGRAM_WEBHOOK_URL=
   ```

4. **Test bot commands**:
   - Send `/start` to bot
   - Check console logs for errors

### 8. Docker Issues

**Problem**: Docker containers not starting or connecting

**Solutions**:

1. **Check Docker is running**:
   ```bash
   docker --version
   docker-compose --version
   ```

2. **Rebuild containers**:
   ```bash
   docker-compose down
   docker-compose up --build
   ```

3. **Check container logs**:
   ```bash
   docker-compose logs backend
   docker-compose logs admin-panel
   ```

4. **Network connectivity**:
   ```bash
   docker network ls
   docker network inspect ecommerce_network
   ```

### 9. Environment Variable Issues

**Problem**: Environment variables not loading

**Solutions**:

1. **Check file names**:
   - Backend: `.env`
   - Admin Panel: `.env.local`
   - Telegram Bot: `.env`

2. **Verify file locations**:
   ```
   backend/.env
   admin-panel/.env.local
   telegram-bot/.env
   ```

3. **Check syntax**:
   ```env
   # Correct
   DATABASE_URL="postgresql://user:pass@localhost:5432/db"

   # Incorrect (no quotes for complex values)
   DATABASE_URL=postgresql://user:pass@localhost:5432/db
   ```

### 10. CORS Errors

**Problem**: Frontend cannot access backend API

**Solutions**:

1. **Check CORS configuration**:
   ```env
   CORS_ORIGIN="http://localhost:4002"
   ```

2. **Multiple origins**:
   ```env
   CORS_ORIGIN="http://localhost:4002,http://localhost:3000"
   ```

3. **Development wildcard** (not for production):
   ```env
   CORS_ORIGIN="*"
   ```

## 🔧 Development Tools

### Debug Mode

Enable debug logging across services:

```env
# Backend
DEBUG=true
LOG_LEVEL=debug

# Admin Panel
NEXT_PUBLIC_DEBUG=true

# Telegram Bot
DEBUG=true
LOG_LEVEL=debug
```

### Health Checks

1. **Backend API**:
   ```bash
   curl http://localhost:4000/api/health
   ```

2. **Database connection**:
   ```bash
   cd backend && npx prisma db push
   ```

3. **Admin Panel**:
   - Visit http://localhost:4002
   - Check browser console for errors

### Log Analysis

1. **Backend logs**:
   ```bash
   cd backend && npm run dev
   # Check console output
   ```

2. **Admin Panel logs**:
   ```bash
   cd admin-panel && npm run dev
   # Check browser console
   ```

3. **Telegram Bot logs**:
   ```bash
   cd telegram-bot && npm run dev
   # Check console output
   ```

## 🚀 Performance Issues

### Slow API Responses

1. **Database optimization**:
   - Add database indexes
   - Optimize queries
   - Use connection pooling

2. **Caching**:
   - Implement Redis caching
   - Use browser caching
   - Cache static assets

3. **Rate limiting**:
   - Adjust rate limits
   - Implement request queuing

### Memory Issues

1. **Monitor memory usage**:
   ```bash
   # Check Node.js memory
   node --max-old-space-size=4096 app.js
   ```

2. **Database connections**:
   - Limit connection pool size
   - Close unused connections

## 🔒 Security Issues

### JWT Token Issues

1. **Token expiration**:
   ```env
   JWT_EXPIRES_IN="15m"
   JWT_REFRESH_EXPIRES_IN="7d"
   ```

2. **Secret rotation**:
   - Change JWT secrets regularly
   - Use strong, random secrets

### File Security

1. **File type validation**:
   - Whitelist allowed file types
   - Scan uploaded files

2. **Access control**:
   - Implement proper file permissions
   - Use secure download tokens

## 📞 Getting Help

### Error Reporting

When reporting issues, include:

1. **Error message** (full stack trace)
2. **Steps to reproduce**
3. **Environment details** (OS, Node.js version)
4. **Configuration** (sanitized environment variables)
5. **Logs** (relevant log entries)

### Debug Information

Collect debug information:

```bash
# System information
node --version
npm --version
docker --version

# Project information
npm list --depth=0

# Service status
curl http://localhost:4000/api/health
curl http://localhost:4002
```

### Community Support

- **GitHub Issues**: [Create detailed issue reports](https://github.com/marysarahmccolley44/TG/issues)
- **Documentation**: Check all README files in the repository
- **Repository**: [TeleShop GitHub Repository](https://github.com/marysarahmccolley44/TG)

---

**TeleShop Troubleshooting - Keeping your system running smoothly**
