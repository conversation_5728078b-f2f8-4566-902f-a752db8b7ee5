{"name": "@teleshop/shared", "version": "1.0.0", "description": "TeleShop shared types and utilities for the e-commerce system", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/marysarahmccolley44/TG.git"}, "homepage": "https://github.com/marysarahmccolley44/TG", "bugs": {"url": "https://github.com/marysarahmccolley44/TG/issues"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist"}, "devDependencies": {"typescript": "^5.3.3", "@types/node": "^20.10.5"}}