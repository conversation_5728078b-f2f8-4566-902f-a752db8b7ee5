// User and Authentication Types
export interface User {
  id: string;
  email: string;
  username?: string;
  telegramId?: string;
  role: UserRole;
  isActive: boolean;
  isBanned: boolean;
  storeId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'ADMIN',
  SELLER = 'SELLER',
  MANAGER = 'MANAGER',
  CUSTOMER = 'CUSTOMER'
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

// Product Types
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  category: string;
  tags: string[];
  isActive: boolean;
  fileUrl: string;
  fileName: string;
  fileSize: number;
  downloadLimit: number;
  previewUrl?: string;
  thumbnailUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProductCreateInput {
  name: string;
  description: string;
  price: number;
  currency: string;
  category: string;
  tags: string[];
  downloadLimit: number;
}

// Order Types
export interface Order {
  id: string;
  userId: string;
  items: OrderItem[];
  totalAmount: number;
  currency: string;
  status: OrderStatus;
  paymentMethod: PaymentMethod;
  paymentAddress?: string;
  transactionHash?: string;
  couponCode?: string;
  discountAmount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface OrderItem {
  id: string;
  productId: string;
  product: Product;
  quantity: number;
  price: number;
}

export enum OrderStatus {
  PENDING = 'PENDING',
  PAID = 'PAID',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED'
}

export enum PaymentMethod {
  BITCOIN = 'BITCOIN',
  ETHEREUM = 'ETHEREUM',
  USDT = 'USDT',
  LITECOIN = 'LITECOIN'
}

// Coupon Types
export interface Coupon {
  id: string;
  code: string;
  discountType: DiscountType;
  discountValue: number;
  minOrderAmount?: number;
  maxUses?: number;
  usedCount: number;
  isActive: boolean;
  expiresAt?: Date;
  createdAt: Date;
}

export enum DiscountType {
  PERCENTAGE = 'PERCENTAGE',
  FIXED_AMOUNT = 'FIXED_AMOUNT'
}

// Wallet Types
export interface CryptoWallet {
  id: string;
  currency: PaymentMethod;
  address: string;
  isActive: boolean;
  createdAt: Date;
}

// Analytics Types
export interface SalesAnalytics {
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
  topProducts: ProductSales[];
  revenueByDay: DailyRevenue[];
  ordersByStatus: OrderStatusCount[];
}

export interface ProductSales {
  productId: string;
  productName: string;
  totalSales: number;
  revenue: number;
}

export interface DailyRevenue {
  date: string;
  revenue: number;
  orders: number;
}

export interface OrderStatusCount {
  status: OrderStatus;
  count: number;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Cart Types
export interface CartItem {
  productId: string;
  quantity: number;
}

export interface Cart {
  items: CartItem[];
  totalAmount: number;
  currency: string;
}

// Store Types
export interface Store {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  settings?: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserStore {
  id: string;
  userId: string;
  storeId: string;
  user?: User;
  store?: Store;
}

// Audit and Session Types
export interface AuditLog {
  id: string;
  userId: string;
  action: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
  user?: User;
}

export interface UserSession {
  id: string;
  userId: string;
  originalUserId?: string;
  sessionToken: string;
  isActive: boolean;
  expiresAt: Date;
  createdAt: Date;
  endedAt?: Date;
}

// User Management Types
export interface CreateUserRequest {
  email: string;
  username?: string;
  password?: string;
  role: UserRole;
  storeId?: string; // For sellers
}

export interface UpdateUserRequest {
  email?: string;
  username?: string;
  role?: UserRole;
  isActive?: boolean;
  isBanned?: boolean;
  storeId?: string;
}

export interface ImpersonationRequest {
  targetUserId: string;
}

export interface ImpersonationResponse {
  sessionToken: string;
  targetUser: User;
  expiresAt: Date;
}

// CSV Import/Export Types
export interface ProductImportRow {
  name: string;
  description: string;
  price: number;
  currency: string;
  category: string;
  tags: string;
  downloadLimit: number;
  fileUrl?: string;
  fileName?: string;
}

export interface ImportResult {
  success: boolean;
  imported: number;
  failed: number;
  errors: ImportError[];
}

export interface ImportError {
  row: number;
  field: string;
  message: string;
  value: any;
}
