# TeleShop Shared Package

Shared TypeScript types, interfaces, and utilities used across all TeleShop services (backend, admin-panel, telegram-bot).

## 🚀 Features

### Type Definitions
- **User Types**: User, Admin, Customer interfaces
- **Product Types**: Product, Category, File interfaces
- **Order Types**: Order, OrderItem, Payment interfaces
- **API Types**: Request/Response interfaces
- **Common Types**: Pagination, Error, Status enums

### Utility Functions
- **Validation**: Common validation functions
- **Formatting**: Date, currency, file size formatters
- **Constants**: Shared constants and enums
- **Helpers**: Common helper functions

### API Interfaces
- **Response Types**: Standardized API response interfaces
- **Request Types**: Common request payload interfaces
- **Error Types**: Standardized error response types

## 🛠 Technology Stack

- **Language**: TypeScript
- **Build Tool**: TypeScript Compiler (tsc)
- **Package Manager**: npm

## 📋 Installation

The shared package is automatically installed as a workspace dependency:

```bash
# From project root
npm install

# Build shared package
npm run build:shared
```

## 📁 Package Structure

```
shared/
├── src/
│   ├── types/           # TypeScript type definitions
│   │   ├── user.ts      # User-related types
│   │   ├── product.ts   # Product-related types
│   │   ├── order.ts     # Order-related types
│   │   ├── api.ts       # API-related types
│   │   └── common.ts    # Common types
│   ├── utils/           # Utility functions
│   │   ├── validation.ts # Validation helpers
│   │   ├── formatting.ts # Formatting helpers
│   │   └── constants.ts  # Shared constants
│   └── index.ts         # Main export file
├── dist/                # Compiled JavaScript
├── package.json         # Package configuration
└── tsconfig.json        # TypeScript configuration
```

## 📚 Type Definitions

### User Types

```typescript
interface User {
  id: string
  email: string
  username: string
  role: UserRole
  isActive: boolean
  createdAt: string
  updatedAt: string
}

enum UserRole {
  ADMIN = 'ADMIN',
  CUSTOMER = 'CUSTOMER'
}
```

### Product Types

```typescript
interface Product {
  id: string
  name: string
  description: string
  price: number
  category: string
  isActive: boolean
  productFile?: string
  thumbnail?: string
  preview?: string
  createdAt: string
  updatedAt: string
}

interface ProductFile {
  id: string
  productId: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  path: string
}
```

### Order Types

```typescript
interface Order {
  id: string
  userId: string
  status: OrderStatus
  totalAmount: number
  paymentMethod: PaymentMethod
  transactionHash?: string
  items: OrderItem[]
  createdAt: string
  updatedAt: string
}

interface OrderItem {
  id: string
  orderId: string
  productId: string
  quantity: number
  price: number
  product?: Product
}

enum OrderStatus {
  PENDING = 'PENDING',
  PAID = 'PAID',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED'
}

enum PaymentMethod {
  BITCOIN = 'BITCOIN',
  ETHEREUM = 'ETHEREUM',
  USDT = 'USDT',
  LITECOIN = 'LITECOIN'
}
```

### API Types

```typescript
interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

interface PaginatedResponse<T> {
  items: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

interface ApiError {
  code: string
  message: string
  details?: any
}
```

### Common Types

```typescript
interface PaginationParams {
  page?: number
  limit?: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

interface FileUpload {
  fieldname: string
  originalname: string
  encoding: string
  mimetype: string
  size: number
  buffer: Buffer
}
```

## 🛠 Utility Functions

### Validation Utilities

```typescript
// Email validation
function isValidEmail(email: string): boolean

// Password strength validation
function isValidPassword(password: string): boolean

// File type validation
function isValidFileType(filename: string, allowedTypes: string[]): boolean

// Price validation
function isValidPrice(price: number): boolean
```

### Formatting Utilities

```typescript
// Format currency
function formatCurrency(amount: number, currency: string): string

// Format file size
function formatFileSize(bytes: number): string

// Format date
function formatDate(date: string | Date, format?: string): string

// Format order status
function formatOrderStatus(status: OrderStatus): string
```

### Constants

```typescript
// File upload constants
export const MAX_FILE_SIZE = 100 * 1024 * 1024 // 100MB
export const ALLOWED_FILE_TYPES = [
  'pdf', 'zip', 'rar', '7z', 'tar', 'gz',
  'mp4', 'mp3', 'jpg', 'jpeg', 'png', 'gif',
  'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'
]

// Pagination constants
export const DEFAULT_PAGE_SIZE = 10
export const MAX_PAGE_SIZE = 100

// Order constants
export const ORDER_TIMEOUT = 30 * 60 * 1000 // 30 minutes
export const PAYMENT_CONFIRMATION_BLOCKS = 3
```

## 🔧 Usage

### In Backend

```typescript
import { User, Product, Order, ApiResponse } from '@teleshop/shared'

// Use types in controllers
async function getProducts(): Promise<ApiResponse<Product[]>> {
  // Implementation
}
```

### In Admin Panel

```typescript
import { Product, Order, formatCurrency } from '@teleshop/shared'

// Use types in components
interface ProductListProps {
  products: Product[]
}

// Use utilities
const formattedPrice = formatCurrency(product.price, 'USD')
```

### In Telegram Bot

```typescript
import { User, Order, OrderStatus } from '@teleshop/shared'

// Use types in bot handlers
function handleOrderStatus(order: Order) {
  if (order.status === OrderStatus.COMPLETED) {
    // Send download links
  }
}
```

## 🛠 Development

### Building the Package

```bash
# Build TypeScript to JavaScript
npm run build

# Watch for changes (development)
npm run dev
```

### Adding New Types

1. **Create type file** in `src/types/`
2. **Export from index.ts**
3. **Build package**: `npm run build`
4. **Update consuming packages**

### Adding New Utilities

1. **Create utility file** in `src/utils/`
2. **Add tests** (when test framework is set up)
3. **Export from index.ts**
4. **Build package**: `npm run build`

## 📦 Package Configuration

### package.json

```json
{
  "name": "@teleshop/shared",
  "version": "1.0.0",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch"
  }
}
```

### tsconfig.json

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "declaration": true,
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
```

## 🔄 Version Management

### Updating the Package

1. **Make changes** to types or utilities
2. **Build package**: `npm run build`
3. **Update version** in package.json
4. **Rebuild consuming packages**

### Breaking Changes

When making breaking changes:
1. **Update major version**
2. **Update all consuming packages**
3. **Test all integrations**
4. **Update documentation**

## 🤝 Contributing

### Guidelines

1. **Follow TypeScript best practices**
2. **Use descriptive type names**
3. **Add JSDoc comments** for complex types
4. **Keep utilities pure functions**
5. **Maintain backward compatibility**
6. **Update documentation**

### Type Naming Conventions

- **Interfaces**: PascalCase (e.g., `User`, `Product`)
- **Enums**: PascalCase (e.g., `UserRole`, `OrderStatus`)
- **Types**: PascalCase (e.g., `ApiResponse`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_FILE_SIZE`)

## 📚 Resources

- **TypeScript Handbook**: https://www.typescriptlang.org/docs/
- **TypeScript Best Practices**: https://typescript-eslint.io/rules/
- **npm Workspaces**: https://docs.npmjs.com/cli/v7/using-npm/workspaces

---

**TeleShop Shared Package - Type-safe development across services**
