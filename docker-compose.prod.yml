version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ecommerce_postgres_prod
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-ecommerce_db}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - ecommerce_network
    restart: unless-stopped

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: ecommerce_redis_prod
    networks:
      - ecommerce_network
    restart: unless-stopped

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: ecommerce_backend_prod
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-ecommerce_db}
      JWT_SECRET: ${JWT_SECRET}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET}
      NODE_ENV: production
      PORT: 4000
      CORS_ORIGIN: ${CORS_ORIGIN}
      UPLOAD_DIR: /app/uploads
    volumes:
      - uploads_data:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - ecommerce_network
    restart: unless-stopped

  # Admin Panel
  admin-panel:
    build:
      context: ./admin-panel
      dockerfile: Dockerfile.prod
    container_name: ecommerce_admin_prod
    environment:
      NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL}
    depends_on:
      - backend
    networks:
      - ecommerce_network
    restart: unless-stopped

  # Telegram Bot
  telegram-bot:
    build:
      context: ./telegram-bot
      dockerfile: Dockerfile.prod
    container_name: ecommerce_telegram_bot_prod
    environment:
      TELEGRAM_BOT_TOKEN: ${TELEGRAM_BOT_TOKEN}
      TELEGRAM_WEBHOOK_URL: ${TELEGRAM_WEBHOOK_URL}
      API_BASE_URL: http://backend:4000/api
      NODE_ENV: production
    depends_on:
      - backend
    networks:
      - ecommerce_network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: ecommerce_nginx_prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - admin-panel
    networks:
      - ecommerce_network
    restart: unless-stopped

volumes:
  postgres_data:
  uploads_data:

networks:
  ecommerce_network:
    driver: bridge
