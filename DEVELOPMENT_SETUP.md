# TeleShop Development Setup Guide

Complete guide for setting up the TeleShop e-commerce system development environment.

## 📋 Prerequisites

### Required Software
- **Node.js**: Version 18.0.0 or higher
- **npm**: Version 8.0.0 or higher (comes with Node.js)
- **Git**: Latest version
- **PostgreSQL**: Version 15+ (for full system setup)
- **Docker & Docker Compose**: Latest version (for containerized setup)

### Optional Tools
- **VS Code**: Recommended IDE with extensions:
  - TypeScript and JavaScript Language Features
  - Tailwind CSS IntelliSense
  - Prisma
  - ESLint
  - Prettier

## 🚀 Quick Start Options

### Option 1: Admin Panel Only (Fastest)

Perfect for frontend development and testing the TeleShop admin interface.

```bash
# Clone repository
git clone https://github.com/marysarahmccolley44/TG.git
cd TG

# Install dependencies
npm install
cd admin-panel && npm install

# Start admin panel
npm run dev
```

**Access**: http://localhost:4002
**Credentials**: `<EMAIL>` / `admin123`

### Option 2: Full Development Environment

Complete setup with all services running.

```bash
# Clone repository
git clone https://github.com/marysarahmccolley44/TG.git
cd TG

# Install all dependencies
npm install

# Set up environment variables
cp backend/.env.example backend/.env
cp admin-panel/.env.local.example admin-panel/.env.local
cp telegram-bot/.env.example telegram-bot/.env

# Edit environment files with your configuration
# See Environment Configuration section below

# Build shared package
npm run build:shared

# Start all services
npm run dev
```

### Option 3: Docker Development

Containerized development environment.

```bash
# Clone repository
git clone https://github.com/marysarahmccolley44/TG.git
cd TG

# Set up environment variables
cp backend/.env.example backend/.env
cp admin-panel/.env.local.example admin-panel/.env.local
cp telegram-bot/.env.example telegram-bot/.env

# Start with Docker
docker-compose up -d

# Initialize database
docker-compose exec backend npx prisma migrate dev
docker-compose exec backend npx prisma db seed
```

## 🔧 Environment Configuration

### Backend Configuration

Edit `backend/.env`:

```env
# Database (for local PostgreSQL)
DATABASE_URL="postgresql://postgres:password@localhost:5432/ecommerce_db"

# JWT Secrets (change these!)
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-here"

# Server
PORT=4000
NODE_ENV="development"
CORS_ORIGIN="http://localhost:4002"

# File Upload
UPLOAD_DIR="./uploads"
```

### Admin Panel Configuration

Edit `admin-panel/.env.local`:

```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:4000/api

# Authentication (set to true for mock auth)
NEXT_PUBLIC_MOCK_AUTH=true

# Development
NODE_ENV=development
NEXT_PUBLIC_DEBUG=true
```

### Telegram Bot Configuration

Edit `telegram-bot/.env`:

```env
# Get token from @BotFather on Telegram
TELEGRAM_BOT_TOKEN=your-telegram-bot-token-here

# API Configuration
API_BASE_URL=http://localhost:4000/api

# Development
NODE_ENV=development
DEBUG=true
```

## 🗄️ Database Setup

### Local PostgreSQL

1. **Install PostgreSQL**:
   ```bash
   # macOS
   brew install postgresql
   brew services start postgresql

   # Ubuntu
   sudo apt update
   sudo apt install postgresql postgresql-contrib
   sudo systemctl start postgresql
   ```

2. **Create Database**:
   ```bash
   createdb ecommerce_db
   ```

3. **Run Migrations**:
   ```bash
   cd backend
   npx prisma migrate dev
   npx prisma db seed
   ```

### Docker PostgreSQL

Database is automatically set up with Docker Compose.

## 🤖 Telegram Bot Setup

### Create Bot

1. **Message @BotFather** on Telegram
2. **Create new bot**: `/newbot`
3. **Choose name**: "TeleShop Bot"
4. **Choose username**: "your_teleshop_bot"
5. **Copy token** to `telegram-bot/.env`

### Configure Bot

1. **Set commands** (optional):
   ```
   /setcommands
   start - Start the bot
   products - Browse products
   cart - View shopping cart
   orders - View order history
   help - Get help
   ```

2. **Set description**:
   ```
   /setdescription
   TeleShop - Your digital marketplace bot
   ```

## 📁 Project Structure

```
TG/
├── admin-panel/          # Next.js admin interface
│   ├── components/       # React components
│   ├── pages/           # Next.js pages
│   ├── contexts/        # React contexts
│   ├── services/        # API services
│   └── styles/          # CSS styles
├── backend/             # Express.js API server
│   ├── src/            # Source code
│   ├── prisma/         # Database schema
│   └── uploads/        # File uploads
├── telegram-bot/       # Telegram bot
│   └── src/           # Bot source code
├── shared/             # Shared types and utilities
│   └── src/           # Shared source code
└── docker-compose.yml  # Docker configuration
```

## 🔄 Development Workflow

### Starting Development

```bash
# Start all services
npm run dev

# Or start individually
npm run dev:backend    # Backend API (port 4000)
npm run dev:admin      # Admin panel (port 4002)
npm run dev:bot        # Telegram bot
```

### Making Changes

1. **Frontend Changes**: Hot reload automatically updates
2. **Backend Changes**: Nodemon restarts server automatically
3. **Database Changes**: Run `npx prisma migrate dev`
4. **Shared Package Changes**: Run `npm run build:shared`

### Testing

```bash
# Run tests (when available)
npm test

# Lint code
npm run lint

# Type checking
npm run type-check
```

## 🔐 Authentication System

### Current: Mock Authentication

The admin panel uses mock authentication for development:

- **File**: `admin-panel/contexts/AuthContext.tsx`
- **Credentials**: `<EMAIL>` / `admin123`
- **Features**: Full admin access with mock data

### Switching to Real API

1. **Enable API in AuthContext**:
   ```typescript
   // Uncomment API calls in login() and checkAuth()
   // Comment out mock authentication logic
   ```

2. **Update environment**:
   ```env
   NEXT_PUBLIC_MOCK_AUTH=false
   ```

3. **Ensure backend is running** with database

## 🛠️ Troubleshooting

### Common Issues

1. **Port Already in Use**:
   ```bash
   # Kill process on port
   lsof -ti:4000 | xargs kill -9
   lsof -ti:4002 | xargs kill -9
   ```

2. **Database Connection Error**:
   - Check PostgreSQL is running
   - Verify DATABASE_URL in `.env`
   - Run `npx prisma migrate dev`

3. **429 API Errors**:
   - System uses mock data fallback
   - Check API rate limiting configuration

4. **Build Errors**:
   ```bash
   # Clean install
   rm -rf node_modules package-lock.json
   npm install
   ```

5. **Prisma Issues**:
   ```bash
   # Reset database
   npx prisma migrate reset
   npx prisma generate
   ```

### Debug Mode

Enable debug logging:

```env
# Backend
DEBUG=true
LOG_LEVEL=debug

# Admin Panel
NEXT_PUBLIC_DEBUG=true

# Telegram Bot
DEBUG=true
LOG_LEVEL=debug
```

## 🚀 Production Deployment

### Environment Variables

Set production values:
- Strong JWT secrets
- Production database URL
- Disable debug mode
- Set proper CORS origins

### Build Commands

```bash
# Build all services
npm run build

# Or build individually
npm run build:backend
npm run build:admin
npm run build:bot
```

### Docker Production

```bash
# Use production compose file
docker-compose -f docker-compose.prod.yml up -d
```

## 📚 Additional Resources

- **API Documentation**: See `README.md` for API endpoints
- **TeleShop Theme**: See `admin-panel/TELESHOP_THEME.md`
- **Telegram Bot Setup**: See `TELEGRAM_BOT_SETUP.md`
- **System Overview**: See `SYSTEM_OVERVIEW.md`

## 🤝 Contributing

1. **Follow coding standards**: ESLint and Prettier configurations
2. **Use TypeScript**: Maintain type safety
3. **Test changes**: Ensure all components work with mock data
4. **Update documentation**: Keep docs current with changes
5. **Follow Git workflow**: Feature branches and pull requests

---

**Happy coding! 🚀**
