# 🤖 Telegram Bot Setup Guide

## 📋 Overview

This guide will help you set up and configure the Telegram bot for your digital e-commerce system. The bot provides a complete shopping experience including product browsing, cart management, order tracking, and file downloads.

## 🚀 Quick Setup

### 1. Create a Telegram Bot

1. Open Telegram and search for `@BotFather`
2. Start a conversation and send `/newbot`
3. Follow the prompts to:
   - Choose a name for your bot (e.g., "Digital Store Bot")
   - Choose a username (e.g., "digitalstore_bot")
4. Copy the bot token provided by BotFather

### 2. Configure in Admin Panel

1. Open your admin panel at `http://localhost:4002`
2. Navigate to **Settings** → **Telegram Bot**
3. Fill in the configuration:
   - **Bot Token**: Paste the token from BotFather
   - **Bot Name**: Your bot's display name
   - **Bot Username**: Your bot's username (with @)
   - **API Base URL**: `http://localhost:4000/api` (for development)
   - **Webhook URL**: Leave empty for polling mode

4. Click **Save Bot Configuration**
5. Copy the generated environment variables

### 3. Create Environment File

Create a `.env` file in the `telegram-bot/` directory:

```bash
# telegram-bot/.env
TELEGRAM_BOT_TOKEN=your_bot_token_here
API_BASE_URL=http://localhost:4000/api
NODE_ENV=development
```

### 4. Start the Bot

```bash
cd telegram-bot
npm install
npm run dev
```

## 🎯 Bot Features

### 🛍️ Shopping Experience
- **Product Browsing**: Browse by categories with pagination
- **Product Details**: View descriptions, prices, and previews
- **Shopping Cart**: Add/remove items, view totals
- **Checkout**: Select payment methods and place orders

### 💳 Payment Integration
- **Supported Currencies**: Bitcoin, Ethereum, USDT, Litecoin
- **Payment Flow**: Automatic address generation and instructions
- **Order Tracking**: Real-time status updates

### 📦 Order Management
- **Order History**: View all past orders with pagination
- **Status Tracking**: Monitor order progress with emojis
- **Download Links**: Access purchased files with 24h expiry
- **Order Cancellation**: Cancel pending orders

### 👤 User Management
- **Registration**: Create accounts via bot commands
- **Authentication**: Secure login with JWT tokens
- **Profile Management**: View account information
- **Account Linking**: Connect Telegram to user accounts

## 🔧 Bot Commands

### Public Commands
```
/start          - Welcome message and main menu
/help           - Bot usage instructions
/products       - Browse product categories
/cart           - View shopping cart
/orders         - View order history (requires login)
/profile        - User profile management
```

### Authentication Commands
```
/login email password           - Login to existing account
/register email password name   - Create new account
```

## 🎨 Bot Interface

### Main Menu Keyboard
```
🛍️ Products    🛒 Cart
📦 My Orders   👤 Profile
❓ Help
```

### Inline Keyboards
- **Product Categories**: Dynamic category buttons
- **Product Actions**: Add to cart, view details
- **Cart Management**: Remove items, checkout
- **Order Actions**: View details, download files
- **Payment Methods**: Select cryptocurrency

## 🔐 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Session Management**: In-memory session storage
- **Message Cleanup**: Prompts to delete sensitive messages
- **Token Validation**: Automatic token refresh and validation

## 🌐 Integration Points

### Backend API Integration
- **Authentication**: `/api/auth/login`, `/api/auth/register`
- **Products**: `/api/products`, `/api/products/categories`
- **Orders**: `/api/orders`, `/api/orders/stats`
- **Downloads**: `/api/downloads/generate`
- **Users**: `/api/users/me`

### Database Integration
- **User Linking**: Telegram ID linked to user accounts
- **Order Tracking**: Real-time order status updates
- **Download Management**: Temporary download link generation

## 🚨 Troubleshooting

### Common Issues

1. **Bot Not Responding**
   - Check if bot token is correct
   - Verify API base URL is accessible
   - Ensure backend server is running

2. **Authentication Errors**
   - Verify JWT secret configuration
   - Check database connection
   - Ensure user registration is working

3. **Payment Issues**
   - Configure crypto wallet addresses
   - Set up payment webhooks
   - Verify order creation process

### Debug Mode

Enable debug logging by setting:
```bash
NODE_ENV=development
DEBUG=bot:*
```

## 📊 Monitoring

### Bot Status Indicators
- **Running Status**: Green/Red indicator in admin panel
- **Configuration Status**: Shows if properly configured
- **Last Seen**: Timestamp of last activity
- **Webhook Status**: Webhook configuration status

### Performance Metrics
- **Active Users**: Number of users with sessions
- **Orders Processed**: Orders created via bot
- **Download Requests**: File download statistics

## 🔄 Production Deployment

### Environment Variables
```bash
TELEGRAM_BOT_TOKEN=your_production_token
API_BASE_URL=https://yourdomain.com/api
TELEGRAM_WEBHOOK_URL=https://yourdomain.com/webhook/telegram
NODE_ENV=production
```

### Webhook Setup
1. Set webhook URL in bot configuration
2. Configure SSL certificate
3. Set up webhook endpoint in backend
4. Test webhook delivery

### Scaling Considerations
- Use Redis for session storage in production
- Implement rate limiting
- Set up monitoring and alerting
- Configure load balancing for multiple bot instances

## 📞 Support

For technical support or questions:
- Check the admin panel bot status
- Review bot logs for errors
- Verify API connectivity
- Test individual bot commands

## 🎉 Success!

Once configured, your Telegram bot will provide a complete e-commerce experience for your customers, allowing them to browse products, make purchases, and download digital files directly through Telegram.
